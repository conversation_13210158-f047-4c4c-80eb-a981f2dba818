# S3-compatible APIs such as Ceph Object Storage with an S3-compatible API, can be used.
# If the API supports path-style URLs rather than virtual hosted bucket addressing,
# configure the URL in `storage_config` with the custom endpoint

schema_config:
  configs:
  - from: 2020-05-15
    store: aws
    object_store: s3
    schema: v11
    index:
      prefix: loki_
storage_config:
  aws:
    s3: s3://access_key:secret_access_key@region/bucket_name
    dynamodb:
      dynamodb_url: dynamodb://access_key:secret_access_key@region
      