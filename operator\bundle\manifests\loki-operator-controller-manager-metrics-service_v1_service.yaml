apiVersion: v1
kind: Service
metadata:
  annotations:
    service.beta.openshift.io/serving-cert-secret-name: loki-operator-metrics
  creationTimestamp: null
  labels:
    app.kubernetes.io/instance: loki-operator-v0.0.1
    app.kubernetes.io/managed-by: operator-lifecycle-manager
    app.kubernetes.io/name: loki-operator
    app.kubernetes.io/part-of: cluster-logging
    app.kubernetes.io/version: 0.0.1
  name: loki-operator-controller-manager-metrics-service
spec:
  ports:
  - name: https
    port: 8443
    protocol: TCP
    targetPort: https
  selector:
    app.kubernetes.io/managed-by: operator-lifecycle-manager
    app.kubernetes.io/name: loki-operator
    app.kubernetes.io/part-of: cluster-logging
    name: loki-operator-controller-manager
status:
  loadBalancer: {}
