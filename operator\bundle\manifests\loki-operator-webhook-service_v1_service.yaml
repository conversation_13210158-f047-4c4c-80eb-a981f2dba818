apiVersion: v1
kind: Service
metadata:
  annotations:
    service.beta.openshift.io/serving-cert-secret-name: loki-operator-webhook-service
  creationTimestamp: null
  labels:
    app.kubernetes.io/instance: loki-operator-v0.0.1
    app.kubernetes.io/managed-by: operator-lifecycle-manager
    app.kubernetes.io/name: loki-operator
    app.kubernetes.io/part-of: cluster-logging
    app.kubernetes.io/version: 0.0.1
  name: loki-operator-webhook-service
spec:
  ports:
  - port: 443
    protocol: TCP
    targetPort: 9443
  selector:
    app.kubernetes.io/managed-by: operator-lifecycle-manager
    app.kubernetes.io/name: loki-operator
    app.kubernetes.io/part-of: cluster-logging
status:
  loadBalancer: {}
