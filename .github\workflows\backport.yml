name: Backport PR Creator
on:
  pull_request_target:
    types:
      - closed
      - labeled

jobs:
  main:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout Actions
        uses: actions/checkout@v3
        with:
          repository: "grafana/grafana-github-actions"
          path: ./actions
          ref: main
      - name: Install Actions
        run: npm install --production --prefix ./actions
      - name: Run backport
        uses: ./actions/backport
        with:
          metricsWriteAPIKey: ${{secrets.GRAFANA_MISC_STATS_API_KEY}}
          token: ${{secrets.GH_BOT_ACCESS_TOKEN}}
          labelsToAdd: "backport"
          title: "[{{base}}] {{originalTitle}}"