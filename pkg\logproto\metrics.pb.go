// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: pkg/logproto/metrics.proto

package logproto

import (
	fmt "fmt"
	_ "github.com/gogo/protobuf/gogoproto"
	proto "github.com/gogo/protobuf/proto"
	io "io"
	math "math"
	math_bits "math/bits"
	reflect "reflect"
	strconv "strconv"
	strings "strings"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion3 // please upgrade the proto package

type WriteRequest_SourceEnum int32

const (
	API  WriteRequest_SourceEnum = 0
	RULE WriteRequest_SourceEnum = 1
)

var WriteRequest_SourceEnum_name = map[int32]string{
	0: "API",
	1: "RULE",
}

var WriteRequest_SourceEnum_value = map[string]int32{
	"API":  0,
	"RULE": 1,
}

func (WriteRequest_SourceEnum) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_d2388e514bd0aa0e, []int{0, 0}
}

type MetricMetadata_MetricType int32

const (
	UNKNOWN        MetricMetadata_MetricType = 0
	COUNTER        MetricMetadata_MetricType = 1
	GAUGE          MetricMetadata_MetricType = 2
	HISTOGRAM      MetricMetadata_MetricType = 3
	GAUGEHISTOGRAM MetricMetadata_MetricType = 4
	SUMMARY        MetricMetadata_MetricType = 5
	INFO           MetricMetadata_MetricType = 6
	STATESET       MetricMetadata_MetricType = 7
)

var MetricMetadata_MetricType_name = map[int32]string{
	0: "UNKNOWN",
	1: "COUNTER",
	2: "GAUGE",
	3: "HISTOGRAM",
	4: "GAUGEHISTOGRAM",
	5: "SUMMARY",
	6: "INFO",
	7: "STATESET",
}

var MetricMetadata_MetricType_value = map[string]int32{
	"UNKNOWN":        0,
	"COUNTER":        1,
	"GAUGE":          2,
	"HISTOGRAM":      3,
	"GAUGEHISTOGRAM": 4,
	"SUMMARY":        5,
	"INFO":           6,
	"STATESET":       7,
}

func (MetricMetadata_MetricType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_d2388e514bd0aa0e, []int{3, 0}
}

type WriteRequest struct {
	Timeseries              []PreallocTimeseries    `protobuf:"bytes,1,rep,name=timeseries,proto3,customtype=PreallocTimeseries" json:"timeseries"`
	Source                  WriteRequest_SourceEnum `protobuf:"varint,2,opt,name=Source,proto3,enum=logproto.WriteRequest_SourceEnum" json:"Source,omitempty"`
	Metadata                []*MetricMetadata       `protobuf:"bytes,3,rep,name=metadata,proto3" json:"metadata,omitempty"`
	SkipLabelNameValidation bool                    `protobuf:"varint,1000,opt,name=skip_label_name_validation,json=skipLabelNameValidation,proto3" json:"skip_label_name_validation,omitempty"`
}

func (m *WriteRequest) Reset()      { *m = WriteRequest{} }
func (*WriteRequest) ProtoMessage() {}
func (*WriteRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_d2388e514bd0aa0e, []int{0}
}
func (m *WriteRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *WriteRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_WriteRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *WriteRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WriteRequest.Merge(m, src)
}
func (m *WriteRequest) XXX_Size() int {
	return m.Size()
}
func (m *WriteRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_WriteRequest.DiscardUnknown(m)
}

var xxx_messageInfo_WriteRequest proto.InternalMessageInfo

func (m *WriteRequest) GetSource() WriteRequest_SourceEnum {
	if m != nil {
		return m.Source
	}
	return API
}

func (m *WriteRequest) GetMetadata() []*MetricMetadata {
	if m != nil {
		return m.Metadata
	}
	return nil
}

func (m *WriteRequest) GetSkipLabelNameValidation() bool {
	if m != nil {
		return m.SkipLabelNameValidation
	}
	return false
}

type WriteResponse struct {
}

func (m *WriteResponse) Reset()      { *m = WriteResponse{} }
func (*WriteResponse) ProtoMessage() {}
func (*WriteResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_d2388e514bd0aa0e, []int{1}
}
func (m *WriteResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *WriteResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_WriteResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *WriteResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WriteResponse.Merge(m, src)
}
func (m *WriteResponse) XXX_Size() int {
	return m.Size()
}
func (m *WriteResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_WriteResponse.DiscardUnknown(m)
}

var xxx_messageInfo_WriteResponse proto.InternalMessageInfo

type TimeSeries struct {
	Labels []LabelAdapter `protobuf:"bytes,1,rep,name=labels,proto3,customtype=LabelAdapter" json:"labels"`
	// Sorted by time, oldest sample first.
	Samples []LegacySample `protobuf:"bytes,2,rep,name=samples,proto3" json:"samples"`
}

func (m *TimeSeries) Reset()      { *m = TimeSeries{} }
func (*TimeSeries) ProtoMessage() {}
func (*TimeSeries) Descriptor() ([]byte, []int) {
	return fileDescriptor_d2388e514bd0aa0e, []int{2}
}
func (m *TimeSeries) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *TimeSeries) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_TimeSeries.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *TimeSeries) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TimeSeries.Merge(m, src)
}
func (m *TimeSeries) XXX_Size() int {
	return m.Size()
}
func (m *TimeSeries) XXX_DiscardUnknown() {
	xxx_messageInfo_TimeSeries.DiscardUnknown(m)
}

var xxx_messageInfo_TimeSeries proto.InternalMessageInfo

func (m *TimeSeries) GetSamples() []LegacySample {
	if m != nil {
		return m.Samples
	}
	return nil
}

type MetricMetadata struct {
	Type             MetricMetadata_MetricType `protobuf:"varint,1,opt,name=type,proto3,enum=logproto.MetricMetadata_MetricType" json:"type,omitempty"`
	MetricFamilyName string                    `protobuf:"bytes,2,opt,name=metric_family_name,json=metricFamilyName,proto3" json:"metric_family_name,omitempty"`
	Help             string                    `protobuf:"bytes,4,opt,name=help,proto3" json:"help,omitempty"`
	Unit             string                    `protobuf:"bytes,5,opt,name=unit,proto3" json:"unit,omitempty"`
}

func (m *MetricMetadata) Reset()      { *m = MetricMetadata{} }
func (*MetricMetadata) ProtoMessage() {}
func (*MetricMetadata) Descriptor() ([]byte, []int) {
	return fileDescriptor_d2388e514bd0aa0e, []int{3}
}
func (m *MetricMetadata) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *MetricMetadata) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_MetricMetadata.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *MetricMetadata) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MetricMetadata.Merge(m, src)
}
func (m *MetricMetadata) XXX_Size() int {
	return m.Size()
}
func (m *MetricMetadata) XXX_DiscardUnknown() {
	xxx_messageInfo_MetricMetadata.DiscardUnknown(m)
}

var xxx_messageInfo_MetricMetadata proto.InternalMessageInfo

func (m *MetricMetadata) GetType() MetricMetadata_MetricType {
	if m != nil {
		return m.Type
	}
	return UNKNOWN
}

func (m *MetricMetadata) GetMetricFamilyName() string {
	if m != nil {
		return m.MetricFamilyName
	}
	return ""
}

func (m *MetricMetadata) GetHelp() string {
	if m != nil {
		return m.Help
	}
	return ""
}

func (m *MetricMetadata) GetUnit() string {
	if m != nil {
		return m.Unit
	}
	return ""
}

type Metric struct {
	Labels []LabelAdapter `protobuf:"bytes,1,rep,name=labels,proto3,customtype=LabelAdapter" json:"labels"`
}

func (m *Metric) Reset()      { *m = Metric{} }
func (*Metric) ProtoMessage() {}
func (*Metric) Descriptor() ([]byte, []int) {
	return fileDescriptor_d2388e514bd0aa0e, []int{4}
}
func (m *Metric) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Metric) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Metric.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *Metric) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Metric.Merge(m, src)
}
func (m *Metric) XXX_Size() int {
	return m.Size()
}
func (m *Metric) XXX_DiscardUnknown() {
	xxx_messageInfo_Metric.DiscardUnknown(m)
}

var xxx_messageInfo_Metric proto.InternalMessageInfo

func init() {
	proto.RegisterEnum("logproto.WriteRequest_SourceEnum", WriteRequest_SourceEnum_name, WriteRequest_SourceEnum_value)
	proto.RegisterEnum("logproto.MetricMetadata_MetricType", MetricMetadata_MetricType_name, MetricMetadata_MetricType_value)
	proto.RegisterType((*WriteRequest)(nil), "logproto.WriteRequest")
	proto.RegisterType((*WriteResponse)(nil), "logproto.WriteResponse")
	proto.RegisterType((*TimeSeries)(nil), "logproto.TimeSeries")
	proto.RegisterType((*MetricMetadata)(nil), "logproto.MetricMetadata")
	proto.RegisterType((*Metric)(nil), "logproto.Metric")
}

func init() { proto.RegisterFile("pkg/logproto/metrics.proto", fileDescriptor_d2388e514bd0aa0e) }

var fileDescriptor_d2388e514bd0aa0e = []byte{
	// 631 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xac, 0x53, 0xcd, 0x6e, 0xd3, 0x40,
	0x10, 0xf6, 0x26, 0x69, 0x92, 0x4e, 0x7f, 0xb0, 0x56, 0x15, 0x98, 0x20, 0x6d, 0x8a, 0xe1, 0xd0,
	0x03, 0x24, 0x52, 0x91, 0x40, 0x20, 0x2e, 0x09, 0x4a, 0x43, 0x45, 0xf3, 0xc3, 0xda, 0xa1, 0xa2,
	0x97, 0x68, 0x93, 0x6e, 0xd3, 0x55, 0xed, 0xd8, 0xd8, 0x0e, 0x52, 0x6e, 0xbc, 0x00, 0x12, 0x67,
	0x9e, 0x80, 0x27, 0xe0, 0x19, 0x7a, 0xec, 0xb1, 0xe2, 0x50, 0x51, 0xf7, 0xd2, 0x63, 0x1f, 0x01,
	0x79, 0xed, 0xc4, 0x29, 0x12, 0x37, 0x4e, 0x9e, 0xf9, 0xbe, 0xf9, 0x66, 0x46, 0xdf, 0x78, 0xa1,
	0xe4, 0x9e, 0x8c, 0xaa, 0x96, 0x33, 0x72, 0x3d, 0x27, 0x70, 0xaa, 0x36, 0x0f, 0x3c, 0x31, 0xf4,
	0x2b, 0x32, 0xc3, 0xc5, 0x19, 0x5e, 0x7a, 0x3a, 0x12, 0xc1, 0xf1, 0x64, 0x50, 0x19, 0x3a, 0x76,
	0x75, 0xe4, 0x8c, 0x9c, 0xaa, 0x84, 0x07, 0x93, 0x23, 0x99, 0xc5, 0xda, 0x28, 0x8a, 0x85, 0xa5,
	0x07, 0xb7, 0x9a, 0xce, 0x82, 0x98, 0xd4, 0x7f, 0x66, 0x60, 0x75, 0xdf, 0x13, 0x01, 0xa7, 0xfc,
	0xd3, 0x84, 0xfb, 0x01, 0xee, 0x02, 0x04, 0xc2, 0xe6, 0x3e, 0xf7, 0x04, 0xf7, 0x35, 0xb4, 0x99,
	0xdd, 0x5a, 0xd9, 0xde, 0xa8, 0xcc, 0x55, 0xa6, 0xb0, 0xb9, 0x21, 0xb9, 0x7a, 0xe9, 0xf4, 0xa2,
	0xac, 0xfc, 0xba, 0x28, 0xe3, 0xae, 0xc7, 0x99, 0x65, 0x39, 0x43, 0x73, 0xae, 0xa3, 0x0b, 0x3d,
	0xf0, 0x4b, 0xc8, 0x1b, 0xce, 0xc4, 0x1b, 0x72, 0x2d, 0xb3, 0x89, 0xb6, 0xd6, 0xb7, 0x1f, 0xa6,
	0xdd, 0x16, 0x27, 0x57, 0xe2, 0xa2, 0xc6, 0x78, 0x62, 0xd3, 0x44, 0x80, 0x5f, 0x41, 0xd1, 0xe6,
	0x01, 0x3b, 0x64, 0x01, 0xd3, 0xb2, 0x72, 0x15, 0x2d, 0x15, 0xb7, 0xa4, 0x3d, 0xad, 0x84, 0xaf,
	0xe7, 0x4e, 0x2f, 0xca, 0x88, 0xce, 0xeb, 0xf1, 0x6b, 0x28, 0xf9, 0x27, 0xc2, 0xed, 0x5b, 0x6c,
	0xc0, 0xad, 0xfe, 0x98, 0xd9, 0xbc, 0xff, 0x99, 0x59, 0xe2, 0x90, 0x05, 0xc2, 0x19, 0x6b, 0xd7,
	0x85, 0x4d, 0xb4, 0x55, 0xa4, 0xf7, 0xa2, 0x92, 0xbd, 0xa8, 0xa2, 0xcd, 0x6c, 0xfe, 0x61, 0xce,
	0xeb, 0x65, 0x80, 0x74, 0x1f, 0x5c, 0x80, 0x6c, 0xad, 0xbb, 0xab, 0x2a, 0xb8, 0x08, 0x39, 0xda,
	0xdb, 0x6b, 0xa8, 0x48, 0xbf, 0x03, 0x6b, 0xc9, 0xf6, 0xbe, 0xeb, 0x8c, 0x7d, 0xae, 0x7f, 0x45,
	0x00, 0xa9, 0x3b, 0xb8, 0x09, 0x79, 0x39, 0x79, 0xe6, 0xe1, 0xfd, 0x74, 0xf1, 0x3d, 0x3e, 0x62,
	0xc3, 0xa9, 0x9c, 0xda, 0x65, 0xc2, 0xab, 0x6f, 0x24, 0x46, 0xae, 0x4a, 0xa8, 0x76, 0xc8, 0xdc,
	0x80, 0x7b, 0x34, 0x91, 0xe3, 0xe7, 0x50, 0xf0, 0x99, 0xed, 0x5a, 0xdc, 0xd7, 0x32, 0xb2, 0xd3,
	0xdd, 0xbf, 0x3b, 0x19, 0x92, 0x96, 0x06, 0x28, 0x74, 0x56, 0xac, 0x7f, 0xcf, 0xc0, 0xfa, 0x6d,
	0x8b, 0xf0, 0x0b, 0xc8, 0x05, 0x53, 0x97, 0x6b, 0x48, 0xde, 0xe1, 0xd1, 0xbf, 0xac, 0x4c, 0x52,
	0x73, 0xea, 0x72, 0x2a, 0x05, 0xf8, 0x09, 0xe0, 0xf8, 0x67, 0xec, 0x1f, 0x31, 0x5b, 0x58, 0x53,
	0x69, 0xa7, 0x3c, 0xe7, 0x32, 0x55, 0x63, 0x66, 0x47, 0x12, 0x91, 0x8b, 0x18, 0x43, 0xee, 0x98,
	0x5b, 0xae, 0x96, 0x93, 0xbc, 0x8c, 0x23, 0x6c, 0x32, 0x16, 0x81, 0xb6, 0x14, 0x63, 0x51, 0xac,
	0x4f, 0x01, 0xd2, 0x49, 0x78, 0x05, 0x0a, 0xbd, 0xf6, 0xbb, 0x76, 0x67, 0xbf, 0xad, 0x2a, 0x51,
	0xf2, 0xa6, 0xd3, 0x6b, 0x9b, 0x0d, 0xaa, 0x22, 0xbc, 0x0c, 0x4b, 0xcd, 0x5a, 0xaf, 0xd9, 0x50,
	0x33, 0x78, 0x0d, 0x96, 0xdf, 0xee, 0x1a, 0x66, 0xa7, 0x49, 0x6b, 0x2d, 0x35, 0x8b, 0x31, 0xac,
	0x4b, 0x26, 0xc5, 0x72, 0x91, 0xd4, 0xe8, 0xb5, 0x5a, 0x35, 0xfa, 0x51, 0x5d, 0x8a, 0xee, 0xb5,
	0xdb, 0xde, 0xe9, 0xa8, 0x79, 0xbc, 0x0a, 0x45, 0xc3, 0xac, 0x99, 0x0d, 0xa3, 0x61, 0xaa, 0x05,
	0xfd, 0x3d, 0xe4, 0xe3, 0xd1, 0xff, 0xed, 0x4e, 0xf5, 0x83, 0xb3, 0x4b, 0xa2, 0x9c, 0x5f, 0x12,
	0xe5, 0xe6, 0x92, 0xa0, 0x2f, 0x21, 0x41, 0x3f, 0x42, 0x82, 0x4e, 0x43, 0x82, 0xce, 0x42, 0x82,
	0x7e, 0x87, 0x04, 0x5d, 0x87, 0x44, 0xb9, 0x09, 0x09, 0xfa, 0x76, 0x45, 0x94, 0xb3, 0x2b, 0xa2,
	0x9c, 0x5f, 0x11, 0xe5, 0xe0, 0xf1, 0xe2, 0x5b, 0xf6, 0xd8, 0x11, 0x1b, 0xb3, 0xaa, 0xe5, 0x9c,
	0x88, 0xea, 0xe2, 0xa3, 0x1d, 0xe4, 0xe5, 0xe7, 0xd9, 0x9f, 0x00, 0x00, 0x00, 0xff, 0xff, 0xfb,
	0x14, 0x52, 0xd7, 0x20, 0x04, 0x00, 0x00,
}

func (x WriteRequest_SourceEnum) String() string {
	s, ok := WriteRequest_SourceEnum_name[int32(x)]
	if ok {
		return s
	}
	return strconv.Itoa(int(x))
}
func (x MetricMetadata_MetricType) String() string {
	s, ok := MetricMetadata_MetricType_name[int32(x)]
	if ok {
		return s
	}
	return strconv.Itoa(int(x))
}
func (this *WriteRequest) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*WriteRequest)
	if !ok {
		that2, ok := that.(WriteRequest)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if len(this.Timeseries) != len(that1.Timeseries) {
		return false
	}
	for i := range this.Timeseries {
		if !this.Timeseries[i].Equal(that1.Timeseries[i]) {
			return false
		}
	}
	if this.Source != that1.Source {
		return false
	}
	if len(this.Metadata) != len(that1.Metadata) {
		return false
	}
	for i := range this.Metadata {
		if !this.Metadata[i].Equal(that1.Metadata[i]) {
			return false
		}
	}
	if this.SkipLabelNameValidation != that1.SkipLabelNameValidation {
		return false
	}
	return true
}
func (this *WriteResponse) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*WriteResponse)
	if !ok {
		that2, ok := that.(WriteResponse)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	return true
}
func (this *TimeSeries) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*TimeSeries)
	if !ok {
		that2, ok := that.(TimeSeries)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if len(this.Labels) != len(that1.Labels) {
		return false
	}
	for i := range this.Labels {
		if !this.Labels[i].Equal(that1.Labels[i]) {
			return false
		}
	}
	if len(this.Samples) != len(that1.Samples) {
		return false
	}
	for i := range this.Samples {
		if !this.Samples[i].Equal(&that1.Samples[i]) {
			return false
		}
	}
	return true
}
func (this *MetricMetadata) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*MetricMetadata)
	if !ok {
		that2, ok := that.(MetricMetadata)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.Type != that1.Type {
		return false
	}
	if this.MetricFamilyName != that1.MetricFamilyName {
		return false
	}
	if this.Help != that1.Help {
		return false
	}
	if this.Unit != that1.Unit {
		return false
	}
	return true
}
func (this *Metric) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*Metric)
	if !ok {
		that2, ok := that.(Metric)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if len(this.Labels) != len(that1.Labels) {
		return false
	}
	for i := range this.Labels {
		if !this.Labels[i].Equal(that1.Labels[i]) {
			return false
		}
	}
	return true
}
func (this *WriteRequest) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 8)
	s = append(s, "&logproto.WriteRequest{")
	s = append(s, "Timeseries: "+fmt.Sprintf("%#v", this.Timeseries)+",\n")
	s = append(s, "Source: "+fmt.Sprintf("%#v", this.Source)+",\n")
	if this.Metadata != nil {
		s = append(s, "Metadata: "+fmt.Sprintf("%#v", this.Metadata)+",\n")
	}
	s = append(s, "SkipLabelNameValidation: "+fmt.Sprintf("%#v", this.SkipLabelNameValidation)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *WriteResponse) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 4)
	s = append(s, "&logproto.WriteResponse{")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *TimeSeries) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 6)
	s = append(s, "&logproto.TimeSeries{")
	s = append(s, "Labels: "+fmt.Sprintf("%#v", this.Labels)+",\n")
	if this.Samples != nil {
		vs := make([]*LegacySample, len(this.Samples))
		for i := range vs {
			vs[i] = &this.Samples[i]
		}
		s = append(s, "Samples: "+fmt.Sprintf("%#v", vs)+",\n")
	}
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *MetricMetadata) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 8)
	s = append(s, "&logproto.MetricMetadata{")
	s = append(s, "Type: "+fmt.Sprintf("%#v", this.Type)+",\n")
	s = append(s, "MetricFamilyName: "+fmt.Sprintf("%#v", this.MetricFamilyName)+",\n")
	s = append(s, "Help: "+fmt.Sprintf("%#v", this.Help)+",\n")
	s = append(s, "Unit: "+fmt.Sprintf("%#v", this.Unit)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *Metric) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 5)
	s = append(s, "&logproto.Metric{")
	s = append(s, "Labels: "+fmt.Sprintf("%#v", this.Labels)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func valueToGoStringMetrics(v interface{}, typ string) string {
	rv := reflect.ValueOf(v)
	if rv.IsNil() {
		return "nil"
	}
	pv := reflect.Indirect(rv).Interface()
	return fmt.Sprintf("func(v %v) *%v { return &v } ( %#v )", typ, typ, pv)
}
func (m *WriteRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *WriteRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *WriteRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.SkipLabelNameValidation {
		i--
		if m.SkipLabelNameValidation {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x3e
		i--
		dAtA[i] = 0xc0
	}
	if len(m.Metadata) > 0 {
		for iNdEx := len(m.Metadata) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Metadata[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintMetrics(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x1a
		}
	}
	if m.Source != 0 {
		i = encodeVarintMetrics(dAtA, i, uint64(m.Source))
		i--
		dAtA[i] = 0x10
	}
	if len(m.Timeseries) > 0 {
		for iNdEx := len(m.Timeseries) - 1; iNdEx >= 0; iNdEx-- {
			{
				size := m.Timeseries[iNdEx].Size()
				i -= size
				if _, err := m.Timeseries[iNdEx].MarshalTo(dAtA[i:]); err != nil {
					return 0, err
				}
				i = encodeVarintMetrics(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *WriteResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *WriteResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *WriteResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	return len(dAtA) - i, nil
}

func (m *TimeSeries) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TimeSeries) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *TimeSeries) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Samples) > 0 {
		for iNdEx := len(m.Samples) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Samples[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintMetrics(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x12
		}
	}
	if len(m.Labels) > 0 {
		for iNdEx := len(m.Labels) - 1; iNdEx >= 0; iNdEx-- {
			{
				size := m.Labels[iNdEx].Size()
				i -= size
				if _, err := m.Labels[iNdEx].MarshalTo(dAtA[i:]); err != nil {
					return 0, err
				}
				i = encodeVarintMetrics(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *MetricMetadata) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MetricMetadata) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *MetricMetadata) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Unit) > 0 {
		i -= len(m.Unit)
		copy(dAtA[i:], m.Unit)
		i = encodeVarintMetrics(dAtA, i, uint64(len(m.Unit)))
		i--
		dAtA[i] = 0x2a
	}
	if len(m.Help) > 0 {
		i -= len(m.Help)
		copy(dAtA[i:], m.Help)
		i = encodeVarintMetrics(dAtA, i, uint64(len(m.Help)))
		i--
		dAtA[i] = 0x22
	}
	if len(m.MetricFamilyName) > 0 {
		i -= len(m.MetricFamilyName)
		copy(dAtA[i:], m.MetricFamilyName)
		i = encodeVarintMetrics(dAtA, i, uint64(len(m.MetricFamilyName)))
		i--
		dAtA[i] = 0x12
	}
	if m.Type != 0 {
		i = encodeVarintMetrics(dAtA, i, uint64(m.Type))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *Metric) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Metric) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Metric) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Labels) > 0 {
		for iNdEx := len(m.Labels) - 1; iNdEx >= 0; iNdEx-- {
			{
				size := m.Labels[iNdEx].Size()
				i -= size
				if _, err := m.Labels[iNdEx].MarshalTo(dAtA[i:]); err != nil {
					return 0, err
				}
				i = encodeVarintMetrics(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func encodeVarintMetrics(dAtA []byte, offset int, v uint64) int {
	offset -= sovMetrics(v)
	base := offset
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return base
}
func (m *WriteRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Timeseries) > 0 {
		for _, e := range m.Timeseries {
			l = e.Size()
			n += 1 + l + sovMetrics(uint64(l))
		}
	}
	if m.Source != 0 {
		n += 1 + sovMetrics(uint64(m.Source))
	}
	if len(m.Metadata) > 0 {
		for _, e := range m.Metadata {
			l = e.Size()
			n += 1 + l + sovMetrics(uint64(l))
		}
	}
	if m.SkipLabelNameValidation {
		n += 3
	}
	return n
}

func (m *WriteResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	return n
}

func (m *TimeSeries) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Labels) > 0 {
		for _, e := range m.Labels {
			l = e.Size()
			n += 1 + l + sovMetrics(uint64(l))
		}
	}
	if len(m.Samples) > 0 {
		for _, e := range m.Samples {
			l = e.Size()
			n += 1 + l + sovMetrics(uint64(l))
		}
	}
	return n
}

func (m *MetricMetadata) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Type != 0 {
		n += 1 + sovMetrics(uint64(m.Type))
	}
	l = len(m.MetricFamilyName)
	if l > 0 {
		n += 1 + l + sovMetrics(uint64(l))
	}
	l = len(m.Help)
	if l > 0 {
		n += 1 + l + sovMetrics(uint64(l))
	}
	l = len(m.Unit)
	if l > 0 {
		n += 1 + l + sovMetrics(uint64(l))
	}
	return n
}

func (m *Metric) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Labels) > 0 {
		for _, e := range m.Labels {
			l = e.Size()
			n += 1 + l + sovMetrics(uint64(l))
		}
	}
	return n
}

func sovMetrics(x uint64) (n int) {
	return (math_bits.Len64(x|1) + 6) / 7
}
func sozMetrics(x uint64) (n int) {
	return sovMetrics(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (this *WriteRequest) String() string {
	if this == nil {
		return "nil"
	}
	repeatedStringForMetadata := "[]*MetricMetadata{"
	for _, f := range this.Metadata {
		repeatedStringForMetadata += strings.Replace(f.String(), "MetricMetadata", "MetricMetadata", 1) + ","
	}
	repeatedStringForMetadata += "}"
	s := strings.Join([]string{`&WriteRequest{`,
		`Timeseries:` + fmt.Sprintf("%v", this.Timeseries) + `,`,
		`Source:` + fmt.Sprintf("%v", this.Source) + `,`,
		`Metadata:` + repeatedStringForMetadata + `,`,
		`SkipLabelNameValidation:` + fmt.Sprintf("%v", this.SkipLabelNameValidation) + `,`,
		`}`,
	}, "")
	return s
}
func (this *WriteResponse) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&WriteResponse{`,
		`}`,
	}, "")
	return s
}
func (this *TimeSeries) String() string {
	if this == nil {
		return "nil"
	}
	repeatedStringForSamples := "[]LegacySample{"
	for _, f := range this.Samples {
		repeatedStringForSamples += fmt.Sprintf("%v", f) + ","
	}
	repeatedStringForSamples += "}"
	s := strings.Join([]string{`&TimeSeries{`,
		`Labels:` + fmt.Sprintf("%v", this.Labels) + `,`,
		`Samples:` + repeatedStringForSamples + `,`,
		`}`,
	}, "")
	return s
}
func (this *MetricMetadata) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&MetricMetadata{`,
		`Type:` + fmt.Sprintf("%v", this.Type) + `,`,
		`MetricFamilyName:` + fmt.Sprintf("%v", this.MetricFamilyName) + `,`,
		`Help:` + fmt.Sprintf("%v", this.Help) + `,`,
		`Unit:` + fmt.Sprintf("%v", this.Unit) + `,`,
		`}`,
	}, "")
	return s
}
func (this *Metric) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&Metric{`,
		`Labels:` + fmt.Sprintf("%v", this.Labels) + `,`,
		`}`,
	}, "")
	return s
}
func valueToStringMetrics(v interface{}) string {
	rv := reflect.ValueOf(v)
	if rv.IsNil() {
		return "nil"
	}
	pv := reflect.Indirect(rv).Interface()
	return fmt.Sprintf("*%v", pv)
}
func (m *WriteRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMetrics
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: WriteRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: WriteRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Timeseries", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMetrics
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMetrics
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthMetrics
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Timeseries = append(m.Timeseries, PreallocTimeseries{})
			if err := m.Timeseries[len(m.Timeseries)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Source", wireType)
			}
			m.Source = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMetrics
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Source |= WriteRequest_SourceEnum(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Metadata", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMetrics
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMetrics
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthMetrics
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Metadata = append(m.Metadata, &MetricMetadata{})
			if err := m.Metadata[len(m.Metadata)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 1000:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field SkipLabelNameValidation", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMetrics
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.SkipLabelNameValidation = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipMetrics(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMetrics
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthMetrics
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *WriteResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMetrics
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: WriteResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: WriteResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipMetrics(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMetrics
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthMetrics
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *TimeSeries) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMetrics
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: TimeSeries: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: TimeSeries: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Labels", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMetrics
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMetrics
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthMetrics
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Labels = append(m.Labels, LabelAdapter{})
			if err := m.Labels[len(m.Labels)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Samples", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMetrics
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMetrics
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthMetrics
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Samples = append(m.Samples, LegacySample{})
			if err := m.Samples[len(m.Samples)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipMetrics(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMetrics
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthMetrics
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *MetricMetadata) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMetrics
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: MetricMetadata: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: MetricMetadata: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMetrics
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= MetricMetadata_MetricType(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field MetricFamilyName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMetrics
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthMetrics
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthMetrics
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.MetricFamilyName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Help", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMetrics
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthMetrics
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthMetrics
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Help = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Unit", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMetrics
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthMetrics
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthMetrics
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Unit = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipMetrics(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMetrics
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthMetrics
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Metric) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMetrics
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Metric: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Metric: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Labels", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMetrics
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMetrics
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthMetrics
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Labels = append(m.Labels, LabelAdapter{})
			if err := m.Labels[len(m.Labels)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipMetrics(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMetrics
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthMetrics
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipMetrics(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowMetrics
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowMetrics
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowMetrics
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if length < 0 {
				return 0, ErrInvalidLengthMetrics
			}
			iNdEx += length
			if iNdEx < 0 {
				return 0, ErrInvalidLengthMetrics
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start int = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowMetrics
					}
					if iNdEx >= l {
						return 0, io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipMetrics(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
				if iNdEx < 0 {
					return 0, ErrInvalidLengthMetrics
				}
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthMetrics = fmt.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowMetrics   = fmt.Errorf("proto: integer overflow")
)
