rule_files:
  - ../prometheus-alerts.yaml

evaluation_interval: 1m

tests:
  - interval: 1m

    input_series:
      - series: 'loki_request_duration_seconds_count{status_code="500", namespace="my-ns", job="ingester", route="my-route"}'
        values: '1+1x20'
      - series: 'loki_request_duration_seconds_count{status_code="200", namespace="my-ns", job="ingester", route="my-route"}'
        values: '1+3x20'

      - series: 'loki_panic_total{namespace="my-ns", job="ingester"}'
        values: '0 1 1 2+0x10'

    # Unit test for alerting rules.
    alert_rule_test:
      # --------- LokiRequestErrors ---------
      - eval_time: 16m
        alertname: LokiRequestErrors
        exp_alerts:
          - exp_labels:
              namespace: my-ns
              job: ingester
              route: my-route
              severity: critical
            exp_annotations:
              summary: "At least 10% of requests are responded by 5xx server errors."
              message: "ingester my-route is experiencing 25.00% errors."
              runbook_url: "[[ .RunbookURL ]]#Loki-Request-Errors"

      # --------- LokiRequestPanics ---------
      - eval_time: 10m
        alertname: LokiRequestPanics
        exp_alerts:
          - exp_labels:
              namespace: my-ns
              job: ingester
              severity: critical
            exp_annotations:
              summary: "A panic was triggered."
              message: "ingester is experiencing an increase of 2 panics."
              runbook_url: "[[ .RunbookURL ]]#Loki-Request-Panics"
