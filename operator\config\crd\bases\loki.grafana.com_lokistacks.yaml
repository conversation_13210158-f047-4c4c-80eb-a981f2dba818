---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.8.0
  creationTimestamp: null
  name: lokistacks.loki.grafana.com
spec:
  group: loki.grafana.com
  names:
    categories:
    - logging
    kind: LokiStack
    listKind: LokiStackList
    plural: lokistacks
    singular: lokistack
  scope: Namespaced
  versions:
  - name: v1beta1
    schema:
      openAPIV3Schema:
        description: LokiStack is the Schema for the lokistacks API
        properties:
          apiVersion:
            description: 'APIVersion defines the versioned schema of this representation
              of an object. Servers should convert recognized schemas to the latest
              internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources'
            type: string
          kind:
            description: 'Kind is a string value representing the REST resource this
              object represents. Servers may infer this from the endpoint the client
              submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
            type: string
          metadata:
            type: object
          spec:
            description: LokiStackSpec defines the desired state of LokiStack
            properties:
              limits:
                description: Limits defines the limits to be applied to log stream
                  processing.
                properties:
                  global:
                    description: Global defines the limits applied globally across
                      the cluster.
                    properties:
                      ingestion:
                        description: IngestionLimits defines the limits applied on
                          ingested log streams.
                        properties:
                          ingestionBurstSize:
                            description: IngestionBurstSize defines the local rate-limited
                              sample size per distributor replica. It should be set
                              to the set at least to the maximum logs size expected
                              in a single push request.
                            format: int32
                            type: integer
                          ingestionRate:
                            description: IngestionRate defines the sample size per
                              second. Units MB.
                            format: int32
                            type: integer
                          maxGlobalStreamsPerTenant:
                            description: MaxGlobalStreamsPerTenant defines the maximum
                              number of active streams per tenant, across the cluster.
                            format: int32
                            type: integer
                          maxLabelNameLength:
                            description: MaxLabelNameLength defines the maximum number
                              of characters allowed for label keys in log streams.
                            format: int32
                            type: integer
                          maxLabelNamesPerSeries:
                            description: MaxLabelNamesPerSeries defines the maximum
                              number of label names per series in each log stream.
                            format: int32
                            type: integer
                          maxLabelValueLength:
                            description: MaxLabelValueLength defines the maximum number
                              of characters allowed for label values in log streams.
                            format: int32
                            type: integer
                          maxLineSize:
                            description: MaxLineSize defines the maximum line size
                              on ingestion path. Units in Bytes.
                            format: int32
                            type: integer
                        type: object
                      queries:
                        description: QueryLimits defines the limit applied on querying
                          log streams.
                        properties:
                          maxChunksPerQuery:
                            description: MaxChunksPerQuery defines the maximum number
                              of chunks that can be fetched by a single query.
                            format: int32
                            type: integer
                          maxEntriesLimitPerQuery:
                            description: MaxEntriesLimitsPerQuery defines the maximum
                              number of log entries that will be returned for a query.
                            format: int32
                            type: integer
                          maxQuerySeries:
                            description: MaxQuerySeries defines the the maximum of
                              unique series that is returned by a metric query.
                            format: int32
                            type: integer
                        type: object
                    type: object
                  tenants:
                    additionalProperties:
                      description: LimitsTemplateSpec defines the limits  applied
                        at ingestion or query path.
                      properties:
                        ingestion:
                          description: IngestionLimits defines the limits applied
                            on ingested log streams.
                          properties:
                            ingestionBurstSize:
                              description: IngestionBurstSize defines the local rate-limited
                                sample size per distributor replica. It should be
                                set to the set at least to the maximum logs size expected
                                in a single push request.
                              format: int32
                              type: integer
                            ingestionRate:
                              description: IngestionRate defines the sample size per
                                second. Units MB.
                              format: int32
                              type: integer
                            maxGlobalStreamsPerTenant:
                              description: MaxGlobalStreamsPerTenant defines the maximum
                                number of active streams per tenant, across the cluster.
                              format: int32
                              type: integer
                            maxLabelNameLength:
                              description: MaxLabelNameLength defines the maximum
                                number of characters allowed for label keys in log
                                streams.
                              format: int32
                              type: integer
                            maxLabelNamesPerSeries:
                              description: MaxLabelNamesPerSeries defines the maximum
                                number of label names per series in each log stream.
                              format: int32
                              type: integer
                            maxLabelValueLength:
                              description: MaxLabelValueLength defines the maximum
                                number of characters allowed for label values in log
                                streams.
                              format: int32
                              type: integer
                            maxLineSize:
                              description: MaxLineSize defines the maximum line size
                                on ingestion path. Units in Bytes.
                              format: int32
                              type: integer
                          type: object
                        queries:
                          description: QueryLimits defines the limit applied on querying
                            log streams.
                          properties:
                            maxChunksPerQuery:
                              description: MaxChunksPerQuery defines the maximum number
                                of chunks that can be fetched by a single query.
                              format: int32
                              type: integer
                            maxEntriesLimitPerQuery:
                              description: MaxEntriesLimitsPerQuery defines the maximum
                                number of log entries that will be returned for a
                                query.
                              format: int32
                              type: integer
                            maxQuerySeries:
                              description: MaxQuerySeries defines the the maximum
                                of unique series that is returned by a metric query.
                              format: int32
                              type: integer
                          type: object
                      type: object
                    description: Tenants defines the limits applied per tenant.
                    type: object
                type: object
              managementState:
                default: Managed
                description: ManagementState defines if the CR should be managed by
                  the operator or not. Default is managed.
                enum:
                - Managed
                - Unmanaged
                type: string
              replicationFactor:
                default: 1
                description: ReplicationFactor defines the policy for log stream replication.
                format: int32
                minimum: 1
                type: integer
              rules:
                description: Rules defines the spec for the ruler component
                properties:
                  enabled:
                    description: Enabled defines a flag to enable/disable the ruler
                      component
                    type: boolean
                  namespaceSelector:
                    description: Namespaces to be selected for PrometheusRules discovery.
                      If unspecified, only the same namespace as the LokiStack object
                      is in is used.
                    properties:
                      matchExpressions:
                        description: matchExpressions is a list of label selector
                          requirements. The requirements are ANDed.
                        items:
                          description: A label selector requirement is a selector
                            that contains values, a key, and an operator that relates
                            the key and values.
                          properties:
                            key:
                              description: key is the label key that the selector
                                applies to.
                              type: string
                            operator:
                              description: operator represents a key's relationship
                                to a set of values. Valid operators are In, NotIn,
                                Exists and DoesNotExist.
                              type: string
                            values:
                              description: values is an array of string values. If
                                the operator is In or NotIn, the values array must
                                be non-empty. If the operator is Exists or DoesNotExist,
                                the values array must be empty. This array is replaced
                                during a strategic merge patch.
                              items:
                                type: string
                              type: array
                          required:
                          - key
                          - operator
                          type: object
                        type: array
                      matchLabels:
                        additionalProperties:
                          type: string
                        description: matchLabels is a map of {key,value} pairs. A
                          single {key,value} in the matchLabels map is equivalent
                          to an element of matchExpressions, whose key field is "key",
                          the operator is "In", and the values array contains only
                          "value". The requirements are ANDed.
                        type: object
                    type: object
                  selector:
                    description: A selector to select which LokiRules to mount for
                      loading alerting/recording rules from.
                    properties:
                      matchExpressions:
                        description: matchExpressions is a list of label selector
                          requirements. The requirements are ANDed.
                        items:
                          description: A label selector requirement is a selector
                            that contains values, a key, and an operator that relates
                            the key and values.
                          properties:
                            key:
                              description: key is the label key that the selector
                                applies to.
                              type: string
                            operator:
                              description: operator represents a key's relationship
                                to a set of values. Valid operators are In, NotIn,
                                Exists and DoesNotExist.
                              type: string
                            values:
                              description: values is an array of string values. If
                                the operator is In or NotIn, the values array must
                                be non-empty. If the operator is Exists or DoesNotExist,
                                the values array must be empty. This array is replaced
                                during a strategic merge patch.
                              items:
                                type: string
                              type: array
                          required:
                          - key
                          - operator
                          type: object
                        type: array
                      matchLabels:
                        additionalProperties:
                          type: string
                        description: matchLabels is a map of {key,value} pairs. A
                          single {key,value} in the matchLabels map is equivalent
                          to an element of matchExpressions, whose key field is "key",
                          the operator is "In", and the values array contains only
                          "value". The requirements are ANDed.
                        type: object
                    type: object
                required:
                - enabled
                type: object
              size:
                description: Size defines one of the support Loki deployment scale
                  out sizes.
                enum:
                - 1x.extra-small
                - 1x.small
                - 1x.medium
                type: string
              storage:
                description: Storage defines the spec for the object storage endpoint
                  to store logs.
                properties:
                  secret:
                    description: Secret for object storage authentication. Name of
                      a secret in the same namespace as the LokiStack custom resource.
                    properties:
                      name:
                        description: Name of a secret in the namespace configured
                          for object storage secrets.
                        type: string
                      type:
                        description: Type of object storage that should be used
                        enum:
                        - azure
                        - gcs
                        - s3
                        - swift
                        type: string
                    required:
                    - name
                    - type
                    type: object
                  tls:
                    description: TLS configuration for reaching the object storage
                      endpoint.
                    properties:
                      caName:
                        description: CA is the name of a ConfigMap containing a CA
                          certificate. It needs to be in the same namespace as the
                          LokiStack custom resource.
                        type: string
                    type: object
                required:
                - secret
                type: object
              storageClassName:
                description: Storage class name defines the storage class for ingester/querier
                  PVCs.
                type: string
              template:
                description: Template defines the resource/limits/tolerations/nodeselectors
                  per component
                properties:
                  compactor:
                    description: Compactor defines the compaction component spec.
                    properties:
                      nodeSelector:
                        additionalProperties:
                          type: string
                        description: NodeSelector defines the labels required by a
                          node to schedule the component onto it.
                        type: object
                      replicas:
                        description: Replicas defines the number of replica pods of
                          the component.
                        format: int32
                        type: integer
                      tolerations:
                        description: Tolerations defines the tolerations required
                          by a node to schedule the component onto it.
                        items:
                          description: The pod this Toleration is attached to tolerates
                            any taint that matches the triple <key,value,effect> using
                            the matching operator <operator>.
                          properties:
                            effect:
                              description: Effect indicates the taint effect to match.
                                Empty means match all taint effects. When specified,
                                allowed values are NoSchedule, PreferNoSchedule and
                                NoExecute.
                              type: string
                            key:
                              description: Key is the taint key that the toleration
                                applies to. Empty means match all taint keys. If the
                                key is empty, operator must be Exists; this combination
                                means to match all values and all keys.
                              type: string
                            operator:
                              description: Operator represents a key's relationship
                                to the value. Valid operators are Exists and Equal.
                                Defaults to Equal. Exists is equivalent to wildcard
                                for value, so that a pod can tolerate all taints of
                                a particular category.
                              type: string
                            tolerationSeconds:
                              description: TolerationSeconds represents the period
                                of time the toleration (which must be of effect NoExecute,
                                otherwise this field is ignored) tolerates the taint.
                                By default, it is not set, which means tolerate the
                                taint forever (do not evict). Zero and negative values
                                will be treated as 0 (evict immediately) by the system.
                              format: int64
                              type: integer
                            value:
                              description: Value is the taint value the toleration
                                matches to. If the operator is Exists, the value should
                                be empty, otherwise just a regular string.
                              type: string
                          type: object
                        type: array
                    type: object
                  distributor:
                    description: Distributor defines the distributor component spec.
                    properties:
                      nodeSelector:
                        additionalProperties:
                          type: string
                        description: NodeSelector defines the labels required by a
                          node to schedule the component onto it.
                        type: object
                      replicas:
                        description: Replicas defines the number of replica pods of
                          the component.
                        format: int32
                        type: integer
                      tolerations:
                        description: Tolerations defines the tolerations required
                          by a node to schedule the component onto it.
                        items:
                          description: The pod this Toleration is attached to tolerates
                            any taint that matches the triple <key,value,effect> using
                            the matching operator <operator>.
                          properties:
                            effect:
                              description: Effect indicates the taint effect to match.
                                Empty means match all taint effects. When specified,
                                allowed values are NoSchedule, PreferNoSchedule and
                                NoExecute.
                              type: string
                            key:
                              description: Key is the taint key that the toleration
                                applies to. Empty means match all taint keys. If the
                                key is empty, operator must be Exists; this combination
                                means to match all values and all keys.
                              type: string
                            operator:
                              description: Operator represents a key's relationship
                                to the value. Valid operators are Exists and Equal.
                                Defaults to Equal. Exists is equivalent to wildcard
                                for value, so that a pod can tolerate all taints of
                                a particular category.
                              type: string
                            tolerationSeconds:
                              description: TolerationSeconds represents the period
                                of time the toleration (which must be of effect NoExecute,
                                otherwise this field is ignored) tolerates the taint.
                                By default, it is not set, which means tolerate the
                                taint forever (do not evict). Zero and negative values
                                will be treated as 0 (evict immediately) by the system.
                              format: int64
                              type: integer
                            value:
                              description: Value is the taint value the toleration
                                matches to. If the operator is Exists, the value should
                                be empty, otherwise just a regular string.
                              type: string
                          type: object
                        type: array
                    type: object
                  gateway:
                    description: Gateway defines the lokistack gateway component spec.
                    properties:
                      nodeSelector:
                        additionalProperties:
                          type: string
                        description: NodeSelector defines the labels required by a
                          node to schedule the component onto it.
                        type: object
                      replicas:
                        description: Replicas defines the number of replica pods of
                          the component.
                        format: int32
                        type: integer
                      tolerations:
                        description: Tolerations defines the tolerations required
                          by a node to schedule the component onto it.
                        items:
                          description: The pod this Toleration is attached to tolerates
                            any taint that matches the triple <key,value,effect> using
                            the matching operator <operator>.
                          properties:
                            effect:
                              description: Effect indicates the taint effect to match.
                                Empty means match all taint effects. When specified,
                                allowed values are NoSchedule, PreferNoSchedule and
                                NoExecute.
                              type: string
                            key:
                              description: Key is the taint key that the toleration
                                applies to. Empty means match all taint keys. If the
                                key is empty, operator must be Exists; this combination
                                means to match all values and all keys.
                              type: string
                            operator:
                              description: Operator represents a key's relationship
                                to the value. Valid operators are Exists and Equal.
                                Defaults to Equal. Exists is equivalent to wildcard
                                for value, so that a pod can tolerate all taints of
                                a particular category.
                              type: string
                            tolerationSeconds:
                              description: TolerationSeconds represents the period
                                of time the toleration (which must be of effect NoExecute,
                                otherwise this field is ignored) tolerates the taint.
                                By default, it is not set, which means tolerate the
                                taint forever (do not evict). Zero and negative values
                                will be treated as 0 (evict immediately) by the system.
                              format: int64
                              type: integer
                            value:
                              description: Value is the taint value the toleration
                                matches to. If the operator is Exists, the value should
                                be empty, otherwise just a regular string.
                              type: string
                          type: object
                        type: array
                    type: object
                  indexGateway:
                    description: IndexGateway defines the index gateway component
                      spec.
                    properties:
                      nodeSelector:
                        additionalProperties:
                          type: string
                        description: NodeSelector defines the labels required by a
                          node to schedule the component onto it.
                        type: object
                      replicas:
                        description: Replicas defines the number of replica pods of
                          the component.
                        format: int32
                        type: integer
                      tolerations:
                        description: Tolerations defines the tolerations required
                          by a node to schedule the component onto it.
                        items:
                          description: The pod this Toleration is attached to tolerates
                            any taint that matches the triple <key,value,effect> using
                            the matching operator <operator>.
                          properties:
                            effect:
                              description: Effect indicates the taint effect to match.
                                Empty means match all taint effects. When specified,
                                allowed values are NoSchedule, PreferNoSchedule and
                                NoExecute.
                              type: string
                            key:
                              description: Key is the taint key that the toleration
                                applies to. Empty means match all taint keys. If the
                                key is empty, operator must be Exists; this combination
                                means to match all values and all keys.
                              type: string
                            operator:
                              description: Operator represents a key's relationship
                                to the value. Valid operators are Exists and Equal.
                                Defaults to Equal. Exists is equivalent to wildcard
                                for value, so that a pod can tolerate all taints of
                                a particular category.
                              type: string
                            tolerationSeconds:
                              description: TolerationSeconds represents the period
                                of time the toleration (which must be of effect NoExecute,
                                otherwise this field is ignored) tolerates the taint.
                                By default, it is not set, which means tolerate the
                                taint forever (do not evict). Zero and negative values
                                will be treated as 0 (evict immediately) by the system.
                              format: int64
                              type: integer
                            value:
                              description: Value is the taint value the toleration
                                matches to. If the operator is Exists, the value should
                                be empty, otherwise just a regular string.
                              type: string
                          type: object
                        type: array
                    type: object
                  ingester:
                    description: Ingester defines the ingester component spec.
                    properties:
                      nodeSelector:
                        additionalProperties:
                          type: string
                        description: NodeSelector defines the labels required by a
                          node to schedule the component onto it.
                        type: object
                      replicas:
                        description: Replicas defines the number of replica pods of
                          the component.
                        format: int32
                        type: integer
                      tolerations:
                        description: Tolerations defines the tolerations required
                          by a node to schedule the component onto it.
                        items:
                          description: The pod this Toleration is attached to tolerates
                            any taint that matches the triple <key,value,effect> using
                            the matching operator <operator>.
                          properties:
                            effect:
                              description: Effect indicates the taint effect to match.
                                Empty means match all taint effects. When specified,
                                allowed values are NoSchedule, PreferNoSchedule and
                                NoExecute.
                              type: string
                            key:
                              description: Key is the taint key that the toleration
                                applies to. Empty means match all taint keys. If the
                                key is empty, operator must be Exists; this combination
                                means to match all values and all keys.
                              type: string
                            operator:
                              description: Operator represents a key's relationship
                                to the value. Valid operators are Exists and Equal.
                                Defaults to Equal. Exists is equivalent to wildcard
                                for value, so that a pod can tolerate all taints of
                                a particular category.
                              type: string
                            tolerationSeconds:
                              description: TolerationSeconds represents the period
                                of time the toleration (which must be of effect NoExecute,
                                otherwise this field is ignored) tolerates the taint.
                                By default, it is not set, which means tolerate the
                                taint forever (do not evict). Zero and negative values
                                will be treated as 0 (evict immediately) by the system.
                              format: int64
                              type: integer
                            value:
                              description: Value is the taint value the toleration
                                matches to. If the operator is Exists, the value should
                                be empty, otherwise just a regular string.
                              type: string
                          type: object
                        type: array
                    type: object
                  querier:
                    description: Querier defines the querier component spec.
                    properties:
                      nodeSelector:
                        additionalProperties:
                          type: string
                        description: NodeSelector defines the labels required by a
                          node to schedule the component onto it.
                        type: object
                      replicas:
                        description: Replicas defines the number of replica pods of
                          the component.
                        format: int32
                        type: integer
                      tolerations:
                        description: Tolerations defines the tolerations required
                          by a node to schedule the component onto it.
                        items:
                          description: The pod this Toleration is attached to tolerates
                            any taint that matches the triple <key,value,effect> using
                            the matching operator <operator>.
                          properties:
                            effect:
                              description: Effect indicates the taint effect to match.
                                Empty means match all taint effects. When specified,
                                allowed values are NoSchedule, PreferNoSchedule and
                                NoExecute.
                              type: string
                            key:
                              description: Key is the taint key that the toleration
                                applies to. Empty means match all taint keys. If the
                                key is empty, operator must be Exists; this combination
                                means to match all values and all keys.
                              type: string
                            operator:
                              description: Operator represents a key's relationship
                                to the value. Valid operators are Exists and Equal.
                                Defaults to Equal. Exists is equivalent to wildcard
                                for value, so that a pod can tolerate all taints of
                                a particular category.
                              type: string
                            tolerationSeconds:
                              description: TolerationSeconds represents the period
                                of time the toleration (which must be of effect NoExecute,
                                otherwise this field is ignored) tolerates the taint.
                                By default, it is not set, which means tolerate the
                                taint forever (do not evict). Zero and negative values
                                will be treated as 0 (evict immediately) by the system.
                              format: int64
                              type: integer
                            value:
                              description: Value is the taint value the toleration
                                matches to. If the operator is Exists, the value should
                                be empty, otherwise just a regular string.
                              type: string
                          type: object
                        type: array
                    type: object
                  queryFrontend:
                    description: QueryFrontend defines the query frontend component
                      spec.
                    properties:
                      nodeSelector:
                        additionalProperties:
                          type: string
                        description: NodeSelector defines the labels required by a
                          node to schedule the component onto it.
                        type: object
                      replicas:
                        description: Replicas defines the number of replica pods of
                          the component.
                        format: int32
                        type: integer
                      tolerations:
                        description: Tolerations defines the tolerations required
                          by a node to schedule the component onto it.
                        items:
                          description: The pod this Toleration is attached to tolerates
                            any taint that matches the triple <key,value,effect> using
                            the matching operator <operator>.
                          properties:
                            effect:
                              description: Effect indicates the taint effect to match.
                                Empty means match all taint effects. When specified,
                                allowed values are NoSchedule, PreferNoSchedule and
                                NoExecute.
                              type: string
                            key:
                              description: Key is the taint key that the toleration
                                applies to. Empty means match all taint keys. If the
                                key is empty, operator must be Exists; this combination
                                means to match all values and all keys.
                              type: string
                            operator:
                              description: Operator represents a key's relationship
                                to the value. Valid operators are Exists and Equal.
                                Defaults to Equal. Exists is equivalent to wildcard
                                for value, so that a pod can tolerate all taints of
                                a particular category.
                              type: string
                            tolerationSeconds:
                              description: TolerationSeconds represents the period
                                of time the toleration (which must be of effect NoExecute,
                                otherwise this field is ignored) tolerates the taint.
                                By default, it is not set, which means tolerate the
                                taint forever (do not evict). Zero and negative values
                                will be treated as 0 (evict immediately) by the system.
                              format: int64
                              type: integer
                            value:
                              description: Value is the taint value the toleration
                                matches to. If the operator is Exists, the value should
                                be empty, otherwise just a regular string.
                              type: string
                          type: object
                        type: array
                    type: object
                  ruler:
                    description: Ruler defines the ruler component spec.
                    properties:
                      nodeSelector:
                        additionalProperties:
                          type: string
                        description: NodeSelector defines the labels required by a
                          node to schedule the component onto it.
                        type: object
                      replicas:
                        description: Replicas defines the number of replica pods of
                          the component.
                        format: int32
                        type: integer
                      tolerations:
                        description: Tolerations defines the tolerations required
                          by a node to schedule the component onto it.
                        items:
                          description: The pod this Toleration is attached to tolerates
                            any taint that matches the triple <key,value,effect> using
                            the matching operator <operator>.
                          properties:
                            effect:
                              description: Effect indicates the taint effect to match.
                                Empty means match all taint effects. When specified,
                                allowed values are NoSchedule, PreferNoSchedule and
                                NoExecute.
                              type: string
                            key:
                              description: Key is the taint key that the toleration
                                applies to. Empty means match all taint keys. If the
                                key is empty, operator must be Exists; this combination
                                means to match all values and all keys.
                              type: string
                            operator:
                              description: Operator represents a key's relationship
                                to the value. Valid operators are Exists and Equal.
                                Defaults to Equal. Exists is equivalent to wildcard
                                for value, so that a pod can tolerate all taints of
                                a particular category.
                              type: string
                            tolerationSeconds:
                              description: TolerationSeconds represents the period
                                of time the toleration (which must be of effect NoExecute,
                                otherwise this field is ignored) tolerates the taint.
                                By default, it is not set, which means tolerate the
                                taint forever (do not evict). Zero and negative values
                                will be treated as 0 (evict immediately) by the system.
                              format: int64
                              type: integer
                            value:
                              description: Value is the taint value the toleration
                                matches to. If the operator is Exists, the value should
                                be empty, otherwise just a regular string.
                              type: string
                          type: object
                        type: array
                    type: object
                type: object
              tenants:
                description: Tenants defines the per-tenant authentication and authorization
                  spec for the lokistack-gateway component.
                properties:
                  authentication:
                    description: Authentication defines the lokistack-gateway component
                      authentication configuration spec per tenant.
                    items:
                      description: AuthenticationSpec defines the oidc configuration
                        per tenant for lokiStack Gateway component.
                      properties:
                        oidc:
                          description: OIDC defines the spec for the OIDC tenant's
                            authentication.
                          properties:
                            groupClaim:
                              description: Group claim field from ID Token
                              type: string
                            issuerURL:
                              description: IssuerURL defines the URL for issuer.
                              type: string
                            redirectURL:
                              description: RedirectURL defines the URL for redirect.
                              type: string
                            secret:
                              description: Secret defines the spec for the clientID,
                                clientSecret and issuerCAPath for tenant's authentication.
                              properties:
                                name:
                                  description: Name of a secret in the namespace configured
                                    for tenant secrets.
                                  type: string
                              required:
                              - name
                              type: object
                            usernameClaim:
                              description: User claim field from ID Token
                              type: string
                          required:
                          - issuerURL
                          - secret
                          type: object
                        tenantId:
                          description: TenantID defines the id of the tenant.
                          type: string
                        tenantName:
                          description: TenantName defines the name of the tenant.
                          type: string
                      required:
                      - oidc
                      - tenantId
                      - tenantName
                      type: object
                    type: array
                  authorization:
                    description: Authorization defines the lokistack-gateway component
                      authorization configuration spec per tenant.
                    properties:
                      opa:
                        description: OPA defines the spec for the third-party endpoint
                          for tenant's authorization.
                        properties:
                          url:
                            description: URL defines the third-party endpoint for
                              authorization.
                            type: string
                        required:
                        - url
                        type: object
                      roleBindings:
                        description: RoleBindings defines configuration to bind a
                          set of roles to a set of subjects.
                        items:
                          description: RoleBindingsSpec binds a set of roles to a
                            set of subjects.
                          properties:
                            name:
                              type: string
                            roles:
                              items:
                                type: string
                              type: array
                            subjects:
                              items:
                                description: Subject represents a subject that has
                                  been bound to a role.
                                properties:
                                  kind:
                                    description: SubjectKind is a kind of LokiStack
                                      Gateway RBAC subject.
                                    enum:
                                    - user
                                    - group
                                    type: string
                                  name:
                                    type: string
                                required:
                                - kind
                                - name
                                type: object
                              type: array
                          required:
                          - name
                          - roles
                          - subjects
                          type: object
                        type: array
                      roles:
                        description: Roles defines a set of permissions to interact
                          with a tenant.
                        items:
                          description: RoleSpec describes a set of permissions to
                            interact with a tenant.
                          properties:
                            name:
                              type: string
                            permissions:
                              items:
                                description: PermissionType is a LokiStack Gateway
                                  RBAC permission.
                                enum:
                                - read
                                - write
                                type: string
                              type: array
                            resources:
                              items:
                                type: string
                              type: array
                            tenants:
                              items:
                                type: string
                              type: array
                          required:
                          - name
                          - permissions
                          - resources
                          - tenants
                          type: object
                        type: array
                    type: object
                  mode:
                    default: openshift-logging
                    description: Mode defines the mode in which lokistack-gateway
                      component will be configured.
                    enum:
                    - static
                    - dynamic
                    - openshift-logging
                    type: string
                required:
                - mode
                type: object
            required:
            - size
            - storage
            - storageClassName
            type: object
          status:
            description: LokiStackStatus defines the observed state of LokiStack
            properties:
              components:
                description: Components provides summary of all Loki pod status grouped
                  per component.
                properties:
                  compactor:
                    additionalProperties:
                      items:
                        type: string
                      type: array
                    description: Compactor is a map to the pod status of the compactor
                      pod.
                    type: object
                  distributor:
                    additionalProperties:
                      items:
                        type: string
                      type: array
                    description: Distributor is a map to the per pod status of the
                      distributor deployment
                    type: object
                  gateway:
                    additionalProperties:
                      items:
                        type: string
                      type: array
                    description: Gateway is a map to the per pod status of the lokistack
                      gateway deployment.
                    type: object
                  indexGateway:
                    additionalProperties:
                      items:
                        type: string
                      type: array
                    description: IndexGateway is a map to the per pod status of the
                      index gateway statefulset
                    type: object
                  ingester:
                    additionalProperties:
                      items:
                        type: string
                      type: array
                    description: Ingester is a map to the per pod status of the ingester
                      statefulset
                    type: object
                  querier:
                    additionalProperties:
                      items:
                        type: string
                      type: array
                    description: Querier is a map to the per pod status of the querier
                      deployment
                    type: object
                  queryFrontend:
                    additionalProperties:
                      items:
                        type: string
                      type: array
                    description: QueryFrontend is a map to the per pod status of the
                      query frontend deployment
                    type: object
                  ruler:
                    additionalProperties:
                      items:
                        type: string
                      type: array
                    description: Ruler is a map to the per pod status of the lokistack
                      ruler statefulset.
                    type: object
                type: object
              conditions:
                description: Conditions of the Loki deployment health.
                items:
                  description: "Condition contains details for one aspect of the current
                    state of this API Resource. --- This struct is intended for direct
                    use as an array at the field path .status.conditions.  For example,
                    type FooStatus struct{ // Represents the observations of a foo's
                    current state. // Known .status.conditions.type are: \"Available\",
                    \"Progressing\", and \"Degraded\" // +patchMergeKey=type // +patchStrategy=merge
                    // +listType=map // +listMapKey=type Conditions []metav1.Condition
                    `json:\"conditions,omitempty\" patchStrategy:\"merge\" patchMergeKey:\"type\"
                    protobuf:\"bytes,1,rep,name=conditions\"` \n // other fields }"
                  properties:
                    lastTransitionTime:
                      description: lastTransitionTime is the last time the condition
                        transitioned from one status to another. This should be when
                        the underlying condition changed.  If that is not known, then
                        using the time when the API field changed is acceptable.
                      format: date-time
                      type: string
                    message:
                      description: message is a human readable message indicating
                        details about the transition. This may be an empty string.
                      maxLength: 32768
                      type: string
                    observedGeneration:
                      description: observedGeneration represents the .metadata.generation
                        that the condition was set based upon. For instance, if .metadata.generation
                        is currently 12, but the .status.conditions[x].observedGeneration
                        is 9, the condition is out of date with respect to the current
                        state of the instance.
                      format: int64
                      minimum: 0
                      type: integer
                    reason:
                      description: reason contains a programmatic identifier indicating
                        the reason for the condition's last transition. Producers
                        of specific condition types may define expected values and
                        meanings for this field, and whether the values are considered
                        a guaranteed API. The value should be a CamelCase string.
                        This field may not be empty.
                      maxLength: 1024
                      minLength: 1
                      pattern: ^[A-Za-z]([A-Za-z0-9_,:]*[A-Za-z0-9_])?$
                      type: string
                    status:
                      description: status of the condition, one of True, False, Unknown.
                      enum:
                      - "True"
                      - "False"
                      - Unknown
                      type: string
                    type:
                      description: type of condition in CamelCase or in foo.example.com/CamelCase.
                        --- Many .condition.type values are consistent across resources
                        like Available, but because arbitrary conditions can be useful
                        (see .node.status.conditions), the ability to deconflict is
                        important. The regex it matches is (dns1123SubdomainFmt/)?(qualifiedNameFmt)
                      maxLength: 316
                      pattern: ^([a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*/)?(([A-Za-z0-9][-A-Za-z0-9_.]*)?[A-Za-z0-9])$
                      type: string
                  required:
                  - lastTransitionTime
                  - message
                  - reason
                  - status
                  - type
                  type: object
                type: array
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
status:
  acceptedNames:
    kind: ""
    plural: ""
  conditions: []
  storedVersions: []
