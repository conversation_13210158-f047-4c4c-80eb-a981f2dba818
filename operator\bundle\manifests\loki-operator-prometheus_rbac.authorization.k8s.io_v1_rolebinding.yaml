apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  annotations:
    include.release.openshift.io/self-managed-high-availability: "true"
    include.release.openshift.io/single-node-developer: "true"
  creationTimestamp: null
  labels:
    app.kubernetes.io/instance: loki-operator-v0.0.1
    app.kubernetes.io/managed-by: operator-lifecycle-manager
    app.kubernetes.io/name: loki-operator
    app.kubernetes.io/part-of: cluster-logging
    app.kubernetes.io/version: 0.0.1
  name: loki-operator-prometheus
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: loki-operator-prometheus
subjects:
- kind: ServiceAccount
  name: prometheus-k8s
  namespace: openshift-monitoring
