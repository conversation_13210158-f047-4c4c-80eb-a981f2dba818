apiVersion: loki.grafana.com/v1beta1
kind: AlertingRule
metadata:
  name: lokistack-dev
  namespace: openshift-logging
  labels:
    loki.grafana.com/cluster-monitoring: "true"
spec:
  tenantID: "tenant-a"
  groups:
    - name: same-group-name
      rules:
        - alert: HighPercentageError
          expr: |
            broken expr
          for: "brokenFor"
    - name: same-group-name
      rules:
        - alert: http-credentials-leaked
          expr: 'broken expr'
          for: "brokenFor"
---
apiVersion: loki.grafana.com/v1beta1
kind: RecordingRule
metadata:
  name: lokistack-dev
  namespace: openshift-logging
  labels:
    loki.grafana.com/cluster-monitoring: "true"
spec:
  tenantID: "tenant-b"
  groups:
    - name: should_record
      interval: "broken"
      rules:
        - record: "broken%metric^name"
          expr: |
            broken expr
