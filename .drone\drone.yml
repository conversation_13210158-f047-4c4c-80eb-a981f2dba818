---
kind: pipeline
name: loki-build-image
steps:
- image: plugins/docker
  name: push-image
  settings:
    context: loki-build-image
    dockerfile: loki-build-image/Dockerfile
    dry_run: false
    password:
      from_secret: docker_password
    repo: grafana/loki-build-image
    tags:
    - 0.21.0
    username:
      from_secret: docker_username
  when:
    paths:
      include:
      - loki-build-image/**
    ref:
      include:
      - refs/heads/main
      - refs/heads/k???
      - refs/tags/v*
trigger:
  event:
  - push
  - pull_request
  - tag
workspace:
  base: /src
  path: loki
---
kind: pipeline
name: check
steps:
- commands:
  - make BUILD_IN_CONTAINER=false check-drone-drift
  depends_on:
  - clone
  environment: {}
  image: grafana/loki-build-image:0.21.0
  name: check-drone-drift
- commands:
  - make BUILD_IN_CONTAINER=false check-generated-files
  depends_on:
  - clone
  environment: {}
  image: grafana/loki-build-image:0.21.0
  name: check-generated-files
- commands:
  - cd ..
  - git clone $CI_REPO_REMOTE loki-main
  - cd -
  depends_on:
  - clone
  environment: {}
  image: grafana/loki-build-image:0.21.0
  name: clone-main
- commands:
  - make BUILD_IN_CONTAINER=false test
  depends_on:
  - clone
  - clone-main
  environment: {}
  image: grafana/loki-build-image:0.21.0
  name: test
- commands:
  - cd ../loki-main
  - BUILD_IN_CONTAINER=false make test
  depends_on:
  - clone-main
  environment: {}
  image: grafana/loki-build-image:0.21.0
  name: test-main
- commands:
  - make BUILD_IN_CONTAINER=false compare-coverage old=../loki-main/test_results.txt
    new=test_results.txt packages=ingester,distributor,querier,querier/queryrange,iter,storage,chunkenc,logql,loki
    > diff.txt
  depends_on:
  - test
  - test-main
  environment: {}
  image: grafana/loki-build-image:0.21.0
  name: compare-coverage
- commands:
  - pull=$(echo $CI_COMMIT_REF | awk -F '/' '{print $3}')
  - 'body=$(jq -Rs ''{body: . }'' diff.txt)'
  - 'curl -X POST -u $USER:$TOKEN -H "Accept: application/vnd.github.v3+json" https://api.github.com/repos/grafana/loki/issues/$pull/comments
    -d "$body" > /dev/null'
  depends_on:
  - compare-coverage
  environment:
    TOKEN:
      from_secret: github_token
    USER: grafanabot
  image: grafana/loki-build-image:0.21.0
  name: report-coverage
- commands:
  - make BUILD_IN_CONTAINER=false lint
  depends_on:
  - clone
  - check-generated-files
  environment: {}
  image: grafana/loki-build-image:0.21.0
  name: lint
- commands:
  - make BUILD_IN_CONTAINER=false check-mod
  depends_on:
  - clone
  - test
  - lint
  environment: {}
  image: grafana/loki-build-image:0.21.0
  name: check-mod
- commands:
  - apk add make bash && make lint-scripts
  image: koalaman/shellcheck-alpine:stable
  name: shellcheck
- commands:
  - make BUILD_IN_CONTAINER=false loki
  depends_on:
  - clone
  - check-generated-files
  environment: {}
  image: grafana/loki-build-image:0.21.0
  name: loki
- commands:
  - make BUILD_IN_CONTAINER=false validate-example-configs
  depends_on:
  - loki
  environment: {}
  image: grafana/loki-build-image:0.21.0
  name: validate-example-configs
- commands:
  - make BUILD_IN_CONTAINER=false check-example-config-doc
  depends_on:
  - clone
  environment: {}
  image: grafana/loki-build-image:0.21.0
  name: check-example-config-doc
trigger:
  event:
  - push
  - pull_request
  - tag
workspace:
  base: /src
  path: loki
---
kind: pipeline
name: mixins
steps:
- commands:
  - make BUILD_IN_CONTAINER=false lint-jsonnet
  depends_on:
  - clone
  environment: {}
  image: grafana/jsonnet-build:c8b75df
  name: lint-jsonnet
- commands:
  - make BUILD_IN_CONTAINER=false loki-mixin-check
  depends_on:
  - clone
  environment: {}
  image: grafana/loki-build-image:0.21.0
  name: loki-mixin-check
trigger:
  event:
  - push
  - pull_request
  - tag
workspace:
  base: /src
  path: loki
---
depends_on:
- check
kind: pipeline
name: docker-amd64
platform:
  arch: amd64
  os: linux
steps:
- commands:
  - apk add --no-cache bash git
  - git fetch origin --tags
  - echo $(./tools/image-tag)-amd64 > .tags
  image: alpine
  name: image-tag
- depends_on:
  - image-tag
  image: plugins/docker
  name: build-loki-image
  settings:
    dockerfile: cmd/loki/Dockerfile
    dry_run: true
    password:
      from_secret: docker_password
    repo: grafana/loki
    username:
      from_secret: docker_username
  when:
    ref:
      exclude:
      - refs/heads/main
      - refs/heads/k???
      - refs/tags/v*
- depends_on:
  - image-tag
  image: plugins/docker
  name: build-loki-canary-image
  settings:
    dockerfile: cmd/loki-canary/Dockerfile
    dry_run: true
    password:
      from_secret: docker_password
    repo: grafana/loki-canary
    username:
      from_secret: docker_username
  when:
    ref:
      exclude:
      - refs/heads/main
      - refs/heads/k???
      - refs/tags/v*
- depends_on:
  - image-tag
  image: plugins/docker
  name: build-logcli-image
  settings:
    dockerfile: cmd/logcli/Dockerfile
    dry_run: true
    password:
      from_secret: docker_password
    repo: grafana/logcli
    username:
      from_secret: docker_username
  when:
    ref:
      exclude:
      - refs/heads/main
      - refs/heads/k???
      - refs/tags/v*
- depends_on:
  - image-tag
  image: plugins/docker
  name: publish-loki-image
  settings:
    dockerfile: cmd/loki/Dockerfile
    dry_run: false
    password:
      from_secret: docker_password
    repo: grafana/loki
    username:
      from_secret: docker_username
  when:
    ref:
      include:
      - refs/heads/main
      - refs/heads/k???
      - refs/tags/v*
- depends_on:
  - image-tag
  image: plugins/docker
  name: publish-loki-canary-image
  settings:
    dockerfile: cmd/loki-canary/Dockerfile
    dry_run: false
    password:
      from_secret: docker_password
    repo: grafana/loki-canary
    username:
      from_secret: docker_username
  when:
    ref:
      include:
      - refs/heads/main
      - refs/heads/k???
      - refs/tags/v*
- depends_on:
  - image-tag
  image: plugins/docker
  name: publish-logcli-image
  settings:
    dockerfile: cmd/logcli/Dockerfile
    dry_run: false
    password:
      from_secret: docker_password
    repo: grafana/logcli
    username:
      from_secret: docker_username
  when:
    ref:
      include:
      - refs/heads/main
      - refs/heads/k???
      - refs/tags/v*
trigger:
  event:
  - push
  - pull_request
  - tag
---
depends_on:
- check
kind: pipeline
name: docker-arm64
platform:
  arch: arm64
  os: linux
steps:
- commands:
  - apk add --no-cache bash git
  - git fetch origin --tags
  - echo $(./tools/image-tag)-arm64 > .tags
  image: alpine
  name: image-tag
- depends_on:
  - image-tag
  image: plugins/docker
  name: build-loki-image
  settings:
    dockerfile: cmd/loki/Dockerfile
    dry_run: true
    password:
      from_secret: docker_password
    repo: grafana/loki
    username:
      from_secret: docker_username
  when:
    ref:
      exclude:
      - refs/heads/main
      - refs/heads/k???
      - refs/tags/v*
- depends_on:
  - image-tag
  image: plugins/docker
  name: build-loki-canary-image
  settings:
    dockerfile: cmd/loki-canary/Dockerfile
    dry_run: true
    password:
      from_secret: docker_password
    repo: grafana/loki-canary
    username:
      from_secret: docker_username
  when:
    ref:
      exclude:
      - refs/heads/main
      - refs/heads/k???
      - refs/tags/v*
- depends_on:
  - image-tag
  image: plugins/docker
  name: build-logcli-image
  settings:
    dockerfile: cmd/logcli/Dockerfile
    dry_run: true
    password:
      from_secret: docker_password
    repo: grafana/logcli
    username:
      from_secret: docker_username
  when:
    ref:
      exclude:
      - refs/heads/main
      - refs/heads/k???
      - refs/tags/v*
- depends_on:
  - image-tag
  image: plugins/docker
  name: publish-loki-image
  settings:
    dockerfile: cmd/loki/Dockerfile
    dry_run: false
    password:
      from_secret: docker_password
    repo: grafana/loki
    username:
      from_secret: docker_username
  when:
    ref:
      include:
      - refs/heads/main
      - refs/heads/k???
      - refs/tags/v*
- depends_on:
  - image-tag
  image: plugins/docker
  name: publish-loki-canary-image
  settings:
    dockerfile: cmd/loki-canary/Dockerfile
    dry_run: false
    password:
      from_secret: docker_password
    repo: grafana/loki-canary
    username:
      from_secret: docker_username
  when:
    ref:
      include:
      - refs/heads/main
      - refs/heads/k???
      - refs/tags/v*
- depends_on:
  - image-tag
  image: plugins/docker
  name: publish-logcli-image
  settings:
    dockerfile: cmd/logcli/Dockerfile
    dry_run: false
    password:
      from_secret: docker_password
    repo: grafana/logcli
    username:
      from_secret: docker_username
  when:
    ref:
      include:
      - refs/heads/main
      - refs/heads/k???
      - refs/tags/v*
trigger:
  event:
  - push
  - pull_request
  - tag
---
depends_on:
- check
kind: pipeline
name: docker-arm
platform:
  arch: arm
  os: linux
steps:
- commands:
  - apk add --no-cache bash git
  - git fetch origin --tags
  - echo $(./tools/image-tag)-arm > .tags
  image: alpine
  name: image-tag
- depends_on:
  - image-tag
  image: plugins/docker
  name: build-loki-image
  settings:
    dockerfile: cmd/loki/Dockerfile
    dry_run: true
    password:
      from_secret: docker_password
    repo: grafana/loki
    username:
      from_secret: docker_username
  when:
    ref:
      exclude:
      - refs/heads/main
      - refs/heads/k???
      - refs/tags/v*
- depends_on:
  - image-tag
  image: plugins/docker
  name: build-loki-canary-image
  settings:
    dockerfile: cmd/loki-canary/Dockerfile
    dry_run: true
    password:
      from_secret: docker_password
    repo: grafana/loki-canary
    username:
      from_secret: docker_username
  when:
    ref:
      exclude:
      - refs/heads/main
      - refs/heads/k???
      - refs/tags/v*
- depends_on:
  - image-tag
  image: plugins/docker
  name: build-logcli-image
  settings:
    dockerfile: cmd/logcli/Dockerfile
    dry_run: true
    password:
      from_secret: docker_password
    repo: grafana/logcli
    username:
      from_secret: docker_username
  when:
    ref:
      exclude:
      - refs/heads/main
      - refs/heads/k???
      - refs/tags/v*
- depends_on:
  - image-tag
  image: plugins/docker
  name: publish-loki-image
  settings:
    dockerfile: cmd/loki/Dockerfile
    dry_run: false
    password:
      from_secret: docker_password
    repo: grafana/loki
    username:
      from_secret: docker_username
  when:
    ref:
      include:
      - refs/heads/main
      - refs/heads/k???
      - refs/tags/v*
- depends_on:
  - image-tag
  image: plugins/docker
  name: publish-loki-canary-image
  settings:
    dockerfile: cmd/loki-canary/Dockerfile
    dry_run: false
    password:
      from_secret: docker_password
    repo: grafana/loki-canary
    username:
      from_secret: docker_username
  when:
    ref:
      include:
      - refs/heads/main
      - refs/heads/k???
      - refs/tags/v*
- depends_on:
  - image-tag
  image: plugins/docker
  name: publish-logcli-image
  settings:
    dockerfile: cmd/logcli/Dockerfile
    dry_run: false
    password:
      from_secret: docker_password
    repo: grafana/logcli
    username:
      from_secret: docker_username
  when:
    ref:
      include:
      - refs/heads/main
      - refs/heads/k???
      - refs/tags/v*
trigger:
  event:
  - push
  - pull_request
  - tag
---
depends_on:
- check
kind: pipeline
name: promtail-amd64
platform:
  arch: amd64
  os: linux
steps:
- commands:
  - apk add --no-cache bash git
  - git fetch origin --tags
  - echo $(./tools/image-tag)-amd64 > .tags
  image: alpine
  name: image-tag
- depends_on:
  - image-tag
  image: plugins/docker
  name: build-promtail-image
  settings:
    dockerfile: clients/cmd/promtail/Dockerfile
    dry_run: true
    password:
      from_secret: docker_password
    repo: grafana/promtail
    username:
      from_secret: docker_username
  when:
    ref:
      exclude:
      - refs/heads/main
      - refs/heads/k???
      - refs/tags/v*
- depends_on:
  - image-tag
  image: plugins/docker
  name: publish-promtail-image
  settings:
    dockerfile: clients/cmd/promtail/Dockerfile
    dry_run: false
    password:
      from_secret: docker_password
    repo: grafana/promtail
    username:
      from_secret: docker_username
  when:
    ref:
      include:
      - refs/heads/main
      - refs/heads/k???
      - refs/tags/v*
trigger:
  event:
  - push
  - pull_request
  - tag
---
depends_on:
- check
kind: pipeline
name: promtail-arm64
platform:
  arch: arm64
  os: linux
steps:
- commands:
  - apk add --no-cache bash git
  - git fetch origin --tags
  - echo $(./tools/image-tag)-arm64 > .tags
  image: alpine
  name: image-tag
- depends_on:
  - image-tag
  image: plugins/docker
  name: build-promtail-image
  settings:
    dockerfile: clients/cmd/promtail/Dockerfile
    dry_run: true
    password:
      from_secret: docker_password
    repo: grafana/promtail
    username:
      from_secret: docker_username
  when:
    ref:
      exclude:
      - refs/heads/main
      - refs/heads/k???
      - refs/tags/v*
- depends_on:
  - image-tag
  image: plugins/docker
  name: publish-promtail-image
  settings:
    dockerfile: clients/cmd/promtail/Dockerfile
    dry_run: false
    password:
      from_secret: docker_password
    repo: grafana/promtail
    username:
      from_secret: docker_username
  when:
    ref:
      include:
      - refs/heads/main
      - refs/heads/k???
      - refs/tags/v*
trigger:
  event:
  - push
  - pull_request
  - tag
---
depends_on:
- check
kind: pipeline
name: promtail-arm
platform:
  arch: arm
  os: linux
steps:
- commands:
  - apk add --no-cache bash git
  - git fetch origin --tags
  - echo $(./tools/image-tag)-arm > .tags
  image: alpine
  name: image-tag
- depends_on:
  - image-tag
  image: plugins/docker
  name: build-promtail-image
  settings:
    dockerfile: clients/cmd/promtail/Dockerfile.arm32
    dry_run: true
    password:
      from_secret: docker_password
    repo: grafana/promtail
    username:
      from_secret: docker_username
  when:
    ref:
      exclude:
      - refs/heads/main
      - refs/heads/k???
      - refs/tags/v*
- depends_on:
  - image-tag
  image: plugins/docker
  name: publish-promtail-image
  settings:
    dockerfile: clients/cmd/promtail/Dockerfile.arm32
    dry_run: false
    password:
      from_secret: docker_password
    repo: grafana/promtail
    username:
      from_secret: docker_username
  when:
    ref:
      include:
      - refs/heads/main
      - refs/heads/k???
      - refs/tags/v*
trigger:
  event:
  - push
  - pull_request
  - tag
---
depends_on:
- check
kind: pipeline
name: fluent-bit-amd64
platform:
  arch: amd64
  os: linux
steps:
- commands:
  - apk add --no-cache bash git
  - git fetch origin --tags
  - echo $(./tools/image-tag)-amd64 > .tags
  - echo ",main" >> .tags
  image: alpine
  name: image-tag
- depends_on:
  - image-tag
  image: plugins/docker
  name: build-fluent-bit-image
  settings:
    dockerfile: clients/cmd/fluent-bit/Dockerfile
    dry_run: true
    password:
      from_secret: docker_password
    repo: grafana/fluent-bit-plugin-loki
    username:
      from_secret: docker_username
  when:
    ref:
      exclude:
      - refs/heads/main
      - refs/heads/k???
      - refs/tags/v*
- depends_on:
  - image-tag
  image: plugins/docker
  name: publish-fluent-bit-image
  settings:
    dockerfile: clients/cmd/fluent-bit/Dockerfile
    dry_run: false
    password:
      from_secret: docker_password
    repo: grafana/fluent-bit-plugin-loki
    username:
      from_secret: docker_username
  when:
    ref:
      include:
      - refs/heads/main
      - refs/heads/k???
      - refs/tags/v*
trigger:
  event:
  - push
  - pull_request
  - tag
---
depends_on:
- check
kind: pipeline
name: fluentd-amd64
platform:
  arch: amd64
  os: linux
steps:
- commands:
  - apk add --no-cache bash git
  - git fetch origin --tags
  - echo $(./tools/image-tag)-amd64 > .tags
  - echo ",main" >> .tags
  image: alpine
  name: image-tag
- depends_on:
  - image-tag
  image: plugins/docker
  name: build-fluentd-image
  settings:
    dockerfile: clients/cmd/fluentd/Dockerfile
    dry_run: true
    password:
      from_secret: docker_password
    repo: grafana/fluent-plugin-loki
    username:
      from_secret: docker_username
  when:
    ref:
      exclude:
      - refs/heads/main
      - refs/heads/k???
      - refs/tags/v*
- depends_on:
  - image-tag
  image: plugins/docker
  name: publish-fluentd-image
  settings:
    dockerfile: clients/cmd/fluentd/Dockerfile
    dry_run: false
    password:
      from_secret: docker_password
    repo: grafana/fluent-plugin-loki
    username:
      from_secret: docker_username
  when:
    ref:
      include:
      - refs/heads/main
      - refs/heads/k???
      - refs/tags/v*
trigger:
  event:
  - push
  - pull_request
  - tag
---
depends_on:
- check
kind: pipeline
name: logstash-amd64
platform:
  arch: amd64
  os: linux
steps:
- commands:
  - apk add --no-cache bash git
  - git fetch origin --tags
  - echo $(./tools/image-tag)-amd64 > .tags
  - echo ",main" >> .tags
  image: alpine
  name: image-tag
- depends_on:
  - image-tag
  image: plugins/docker
  name: build-logstash-image
  settings:
    dockerfile: clients/cmd/logstash/Dockerfile
    dry_run: true
    password:
      from_secret: docker_password
    repo: grafana/logstash-output-loki
    username:
      from_secret: docker_username
  when:
    ref:
      exclude:
      - refs/heads/main
      - refs/heads/k???
      - refs/tags/v*
- depends_on:
  - image-tag
  image: plugins/docker
  name: publish-logstash-image
  settings:
    dockerfile: clients/cmd/logstash/Dockerfile
    dry_run: false
    password:
      from_secret: docker_password
    repo: grafana/logstash-output-loki
    username:
      from_secret: docker_username
  when:
    ref:
      include:
      - refs/heads/main
      - refs/heads/k???
      - refs/tags/v*
trigger:
  event:
  - push
  - pull_request
  - tag
---
depends_on:
- check
kind: pipeline
name: querytee-amd64
platform:
  arch: amd64
  os: linux
steps:
- commands:
  - apk add --no-cache bash git
  - git fetch origin --tags
  - echo $(./tools/image-tag)-amd64 > .tags
  - echo ",main" >> .tags
  image: alpine
  name: image-tag
- depends_on:
  - image-tag
  image: plugins/docker
  name: build-querytee-image
  settings:
    dockerfile: cmd/querytee/Dockerfile
    dry_run: true
    password:
      from_secret: docker_password
    repo: grafana/loki-query-tee
    username:
      from_secret: docker_username
  when:
    ref:
      exclude:
      - refs/heads/main
      - refs/heads/k???
      - refs/tags/v*
- depends_on:
  - image-tag
  image: plugins/docker
  name: publish-querytee-image
  settings:
    dockerfile: cmd/querytee/Dockerfile
    dry_run: false
    password:
      from_secret: docker_password
    repo: grafana/loki-query-tee
    username:
      from_secret: docker_username
  when:
    ref:
      include:
      - refs/heads/main
      - refs/heads/k???
      - refs/tags/v*
trigger:
  event:
  - push
  - pull_request
  - tag
---
depends_on:
- docker-amd64
- docker-arm64
- docker-arm
- promtail-amd64
- promtail-arm64
- promtail-arm
kind: pipeline
name: manifest
steps:
- depends_on:
  - clone
  image: plugins/manifest
  name: manifest-promtail
  settings:
    ignore_missing: false
    password:
      from_secret: docker_password
    spec: .drone/docker-manifest.tmpl
    target: promtail
    username:
      from_secret: docker_username
- depends_on:
  - clone
  - manifest-promtail
  image: plugins/manifest
  name: manifest-loki
  settings:
    ignore_missing: false
    password:
      from_secret: docker_password
    spec: .drone/docker-manifest.tmpl
    target: loki
    username:
      from_secret: docker_username
- depends_on:
  - clone
  - manifest-loki
  image: plugins/manifest
  name: manifest-loki-canary
  settings:
    ignore_missing: false
    password:
      from_secret: docker_password
    spec: .drone/docker-manifest.tmpl
    target: loki-canary
    username:
      from_secret: docker_username
trigger:
  event:
  - push
  - tag
  ref:
    include:
    - refs/heads/main
    - refs/heads/k???
    - refs/tags/v*
---
depends_on:
- manifest
image_pull_secrets:
- dockerconfigjson
kind: pipeline
name: deploy
steps:
- commands:
  - apk add --no-cache bash git
  - git fetch origin --tags
  - echo $(./tools/image-tag) > .tag
  depends_on:
  - clone
  image: alpine
  name: image-tag
- depends_on:
  - clone
  - image-tag
  image: us.gcr.io/kubernetes-dev/drone/plugins/deploy-image
  name: trigger
  settings:
    docker_tag_file: .tag
    github_token:
      from_secret: github_token
    images_json:
      from_secret: deploy_config
trigger:
  event:
  - push
  - tag
  ref:
    include:
    - refs/heads/main
    - refs/heads/k???
    - refs/tags/v*
---
kind: pipeline
name: promtail-windows
platform:
  arch: amd64
  os: windows
  version: "1809"
steps:
- commands:
  - Write-Output $env:DRONE_RUNNER_NAME
  image: golang:windowsservercore-1809
  name: identify-runner
- commands:
  - go test .\clients\pkg\promtail\targets\windows\... -v
  image: golang:windowsservercore-1809
  name: test
trigger:
  event:
  - push
  - pull_request
  - tag
---
depends_on:
- check
kind: pipeline
name: lambda-promtail
steps:
- commands:
  - apk add --no-cache bash git
  - git fetch origin --tags
  - echo $(./tools/image-tag)-amd64 > .tags
  - echo ",main" >> .tags
  image: alpine
  name: image-tag
- depends_on:
  - image-tag
  image: cstyan/ecr
  name: build-lambda-promtail-image
  privileged: true
  settings:
    access_key:
      from_secret: ecr_key
    dockerfile: tools/lambda-promtail/Dockerfile
    dry_run: true
    region: us-east-1
    registry: public.ecr.aws/grafana
    repo: public.ecr.aws/grafana/lambda-promtail
    secret_key:
      from_secret: ecr_secret_key
  when:
    ref:
      exclude:
      - refs/heads/main
      - refs/heads/k???
      - refs/tags/v*
- depends_on:
  - image-tag
  image: cstyan/ecr
  name: publish-lambda-promtail-image
  privileged: true
  settings:
    access_key:
      from_secret: ecr_key
    dockerfile: tools/lambda-promtail/Dockerfile
    dry_run: false
    region: us-east-1
    registry: public.ecr.aws/grafana
    repo: public.ecr.aws/grafana/lambda-promtail
    secret_key:
      from_secret: ecr_secret_key
  when:
    ref:
      include:
      - refs/heads/main
      - refs/heads/k???
      - refs/tags/v*
trigger:
  event:
  - push
  - pull_request
  - tag
---
get:
  name: pat
  path: infra/data/ci/github/grafanabot
kind: secret
name: github_token
---
get:
  name: .dockerconfigjson
  path: secret/data/common/gcr
kind: secret
name: dockerconfigjson
---
get:
  name: username
  path: infra/data/ci/docker_hub
kind: secret
name: docker_username
---
get:
  name: password
  path: infra/data/ci/docker_hub
kind: secret
name: docker_password
---
get:
  name: access_key_id
  path: infra/data/ci/loki/aws-credentials
kind: secret
name: ecr_key
---
get:
  name: secret_access_key
  path: infra/data/ci/loki/aws-credentials
kind: secret
name: ecr_secret_key
---
get:
  name: config.json
  path: secret/data/common/loki_ci_autodeploy
kind: secret
name: deploy_config
---
kind: signature
hmac: 81be9fce60976703815abf1fd2f1c9255a142d615b90d4b5278ff4a64fa25e92

...
