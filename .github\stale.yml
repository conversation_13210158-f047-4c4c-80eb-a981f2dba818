# Days without any activity until an issue is labeled as stale
daysUntilStale: 30

# Days after having the stale label that the issue will be closed
daysUntilClose: 7

# Labels that prevent issues from being marked as stale
exemptLabels:
  - keepalive
  - proposal

# Label to use to identify a stale issue
staleLabel: stale

# Comment to post when marking an issue as stale. Leave as false
# to disable.
markComment: |
  Hi! This issue has been automatically marked as stale because it has not had any
  activity in the past 30 days.

  We use a stalebot among other tools to help manage the state of issues in this project.
  A stalebot can be very useful in closing issues in a number of cases; the most common
  is closing issues or PRs where the original reporter has not responded.

  Stalebots are also emotionless and cruel and can close issues which are still very relevant.

  **If this issue is important to you, please add a comment to keep it open. More importantly, please add a thumbs-up to the original issue entry.**

  We regularly sort for closed issues which have a `stale` label sorted by thumbs up.

  We may also:
    * Mark issues as `revivable` if we think it's a valid issue but isn't something we are likely
  to prioritize in the future (the issue will still remain closed).
    * Add a `keepalive` label to silence the stalebot if the issue is very common/popular/important.

  We are doing our best to respond, organize, and prioritize all issues but it can be a challenging task,
  our sincere apologies if you find yourself at the mercy of the stalebot.


# Comment to post when closing a stale issue. Leave as
# false to disable.
closeComment: false
