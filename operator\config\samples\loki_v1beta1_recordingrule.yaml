apiVersion: loki.grafana.com/v1beta1
kind: RecordingRule
metadata:
  name: recordingrule-sample
spec:
  tenantID: test-tenant
  groups:
    - name: recording-rules-group
      interval: 10m
      rules:
        - record: "myservice:requests:rate10m"
          expr: |
            sum(rate({container="myservice"}[10m]))
        - record: "otherservice:requests:rate1m"
          expr: |
            sum(rate({container="otherservice"}[1m]))
