name: Run commands when issues are labeled
on:
  issues:
    types: [labeled]
jobs:
  main:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout Actions
        uses: actions/checkout@v3
        with:
          repository: "grafana/grafana-github-actions"
          path: ./actions
          ref: main
      - name: Install Actions
        run: npm install --production --prefix ./actions
      - name: Run Commands
        uses: ./actions/commands
        with:          
          token: ${{secrets.GH_BOT_ACCESS_TOKEN}}
          configPath: issue_commands