name: operator build

on:
  push:
    paths:
      - 'operator/**'
    branches: [ main ]
  pull_request:
    paths:
      - 'operator/**'

jobs:
  lint:
    name: lint
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        go: ['1.17']
    steps:
    - name: Install make
      run: sudo apt-get install make
    - name: Set up Go 1.x
      uses: actions/setup-go@v3
      with:
        go-version: ${{ matrix.go }}
      id: go
    - uses: actions/checkout@v3
    - name: Lint
      uses: golangci/golangci-lint-action@v3.2.0
      with:
        version: v1.38
        skip-go-installation: true
        args: --timeout=2m
        working-directory: ./operator
    - name: Check prometheus rules
      working-directory: ./operator
      run: make lint-prometheus

  build-manager:
    name: Build Manager
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        go: ['1.17']
    steps:
    - name: Install make
      run: sudo apt-get install make
    - name: Set up Go 1.x
      uses: actions/setup-go@v3
      with:
        go-version: ${{ matrix.go }}
      id: go
    - uses: actions/checkout@v3
    - name: Build Manager
      working-directory: ./operator
      run: |-
        make manager && git diff --exit-code

  build-broker:
    name: Build Broker
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        go: ['1.17']
    steps:
    - name: Install make
      run: sudo apt-get install make
    - name: Set up Go 1.x
      uses: actions/setup-go@v3
      with:
        go-version: ${{ matrix.go }}
      id: go
    - uses: actions/checkout@v3
    - name: Build Broker
      working-directory: ./operator
      run: |-
        make bin/loki-broker && git diff --exit-code

  test:
    name: test
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        go: ['1.17']
    steps:
    - name: Install make
      run: sudo apt-get install make
    - name: Set up Go 1.x
      uses: actions/setup-go@v3
      with:
        go-version: ${{ matrix.go }}
      id: go
    - uses: actions/checkout@v3
    - name: Run tests
      working-directory: ./operator
      run: go test -coverprofile=profile.cov ./...
    - name: Run prometheus rule tests
      working-directory: ./operator
      run: make test-unit-prometheus
    - name: Send coverage
      uses: shogo82148/actions-goveralls@v1
      with:
        working-directory: ./operator
        path-to-profile: profile.cov
        flag-name: Go-${{ matrix.go }}
        shallow: true
