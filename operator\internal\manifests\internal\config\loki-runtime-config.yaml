---
overrides:
  {{- range $tenant, $spec := .Stack.Limits.Tenants }}
  {{ $tenant }}:
  {{- if $l := $spec.IngestionLimits -}}
    {{ if $l.IngestionRate }}
    ingestion_rate_mb: {{ $l.IngestionRate }}
    {{- end -}}
    {{ if $l.IngestionBurstSize }}
    ingestion_burst_size_mb: {{ $l.IngestionBurstSize }}
    {{- end -}}
    {{ if $l.MaxLabelNameLength }}
    max_label_name_length: {{ $l.MaxLabelNameLength }}
    {{- end -}}
    {{ if $l.Max<PERSON>abel<PERSON>alueLength }}
    max_label_value_length: {{ $l.Max<PERSON>abel<PERSON>alueLength }}
    {{- end -}}
    {{ if $l.MaxLabelNamesPerSeries }}
    max_label_names_per_series: {{ $l.MaxLabelNamesPerSeries }}
    {{- end -}}
    {{ if $l.MaxLineSize }}
    max_line_size: {{ $l.MaxLineSize }}
    {{- end -}}
    {{ if $l.MaxGlobalStreamsPerTenant }}
    max_global_streams_per_user: {{ $l.MaxGlobalStreamsPerTenant }}
    {{- end -}}
  {{- end -}}
  {{- if $l := $spec.QueryLimits -}}
    {{ if $l.MaxEntriesLimitPerQuery }}
    max_entries_limit_per_query: {{ $spec.QueryLimits.MaxEntriesLimitPerQuery }}
    {{- end -}}
    {{ if $spec.QueryLimits.MaxChunksPerQuery }}
    max_chunks_per_query: {{ $spec.QueryLimits.MaxChunksPerQuery }}
    {{- end -}}
    {{ if $spec.QueryLimits.MaxQuerySeries }}
    max_query_series: {{ $spec.QueryLimits.MaxQuerySeries }}
    {{- end -}}
  {{- end -}}
  {{- end -}}
