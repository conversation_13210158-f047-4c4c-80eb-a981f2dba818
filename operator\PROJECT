domain: grafana.com
layout:
- go.kubebuilder.io/v3
plugins:
  manifests.sdk.operatorframework.io/v2: {}
  scorecard.sdk.operatorframework.io/v2: {}
projectName: loki-operator
repo: github.com/grafana/loki/operator
resources:
- api:
    crdVersion: v1
    namespaced: true
  controller: true
  domain: grafana.com
  group: loki
  kind: LokiStack
  path: github.com/grafana/loki/operator/api/v1beta1
  version: v1beta1
- api:
    crdVersion: v1
    namespaced: true
  controller: true
  domain: grafana.com
  group: loki
  kind: AlertingRule
  path: github.com/grafana/loki/operator/api/v1beta1
  version: v1beta1
  webhooks:
    validation: true
    webhookVersion: v1
- api:
    crdVersion: v1
    namespaced: true
  controller: true
  domain: grafana.com
  group: loki
  kind: RecordingRule
  path: github.com/grafana/loki/operator/api/v1beta1
  version: v1beta1
  webhooks:
    validation: true
    webhookVersion: v1
- api:
    crdVersion: v1
    namespaced: true
  controller: true
  domain: grafana.com
  group: loki
  kind: RulerConfig
  path: github.com/grafana/loki/operator/api/v1beta1
  version: v1beta1
version: "3"
