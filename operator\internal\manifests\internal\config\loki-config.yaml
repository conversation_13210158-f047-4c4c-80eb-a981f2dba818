---
auth_enabled: true
chunk_store_config:
  chunk_cache_config:
    enable_fifocache: true
    fifocache:
      max_size_bytes: 500MB
common:
  storage:
    {{ with .ObjectStorage.Azure }}
    azure:
      environment: {{ .Env }}
      container_name: {{ .Container }}
      account_name: {{ .AccountName }}
      account_key: {{ .AccountKey }}
    {{ end }}
    {{ with .ObjectStorage.GCS }}
    gcs:
      bucket_name: {{ .Bucket }}
    {{ end }}
    {{ with .ObjectStorage.S3 }}
    s3:
      s3: {{ .Endpoint }}
      bucketnames: {{ .Buckets }}
      region: {{ .Region }}
      access_key_id: {{ .AccessKeyID }}
      secret_access_key: {{ .AccessKeySecret }}
      s3forcepathstyle: true
    {{ end }}
    {{ with .ObjectStorage.Swift }}
    swift:
      auth_url: {{ .AuthURL }}
      username: {{ .Username }}
      user_domain_name: {{ .UserDomainName }}
      user_domain_id: {{ .UserDomainID }}
      user_id: {{ .UserID }}
      password: {{ .Password }}
      domain_id: {{ .DomainID }}
      domain_name: {{ .DomainName }}
      project_id: {{ .ProjectID }}
      project_name: {{ .ProjectName }}
      project_domain_id: {{ .ProjectDomainID }}
      project_domain_name: {{ .ProjectDomainName }}
      region_name: {{ .Region }}
      container_name: {{ .Container }}
    {{ end }}
compactor:
  compaction_interval: 2h
  working_directory: {{ .StorageDirectory }}/compactor
frontend:
  tail_proxy_url: http://{{ .Querier.FQDN }}:{{ .Querier.Port }}
  compress_responses: true
  max_outstanding_per_tenant: 256
  log_queries_longer_than: 5s
frontend_worker:
  frontend_address: {{ .FrontendWorker.FQDN }}:{{ .FrontendWorker.Port }}
  grpc_client_config:
    max_send_msg_size: 104857600
  match_max_concurrent: true
ingester:
  chunk_block_size: 262144
  chunk_encoding: snappy
  chunk_idle_period: 1h
  chunk_retain_period: 5m
  chunk_target_size: 2097152
  flush_op_timeout: 10m
  max_chunk_age: 2h
  lifecycler:
    final_sleep: 0s
    heartbeat_period: 5s
    interface_names:
      - eth0
    join_after: 30s
    num_tokens: 512
    ring:
      replication_factor: {{ .Stack.ReplicationFactor }}
      heartbeat_timeout: 1m
  max_transfer_retries: 0
  wal:
    enabled: true
    dir: {{ .WriteAheadLog.Directory }}
    replay_memory_ceiling: {{ .WriteAheadLog.ReplayMemoryCeiling }}
ingester_client:
  grpc_client_config:
    max_recv_msg_size: 67108864
  remote_timeout: 1s
# NOTE: Keep the order of keys as in Loki docs
# to enable easy diffs when vendoring newer
# Loki releases.
# (See https://grafana.com/docs/loki/latest/configuration/#limits_config)
#
# Values for not exposed fields are taken from the grafana/loki production
# configuration manifests.
# (See https://github.com/grafana/loki/blob/main/production/ksonnet/loki/config.libsonnet)
limits_config:
  ingestion_rate_strategy: global
  ingestion_rate_mb: {{ .Stack.Limits.Global.IngestionLimits.IngestionRate }}
  ingestion_burst_size_mb: {{ .Stack.Limits.Global.IngestionLimits.IngestionBurstSize }}
  max_label_name_length: {{ .Stack.Limits.Global.IngestionLimits.MaxLabelNameLength }}
  max_label_value_length: {{ .Stack.Limits.Global.IngestionLimits.MaxLabelValueLength }}
  max_label_names_per_series: {{ .Stack.Limits.Global.IngestionLimits.MaxLabelNamesPerSeries }}
  reject_old_samples: true
  reject_old_samples_max_age: 168h
  creation_grace_period: 10m
  enforce_metric_name: false
  # Keep max_streams_per_user always to 0 to default
  # using max_global_streams_per_user always.
  # (See https://github.com/grafana/loki/blob/main/pkg/ingester/limiter.go#L73)
  max_streams_per_user: 0
  max_line_size: {{ .Stack.Limits.Global.IngestionLimits.MaxLineSize }}
  max_entries_limit_per_query: {{ .Stack.Limits.Global.QueryLimits.MaxEntriesLimitPerQuery }}
  max_global_streams_per_user: {{ .Stack.Limits.Global.IngestionLimits.MaxGlobalStreamsPerTenant }}
  max_chunks_per_query: {{ .Stack.Limits.Global.QueryLimits.MaxChunksPerQuery }}
  max_query_length: 721h
  max_query_parallelism: 32
  max_query_series: {{ .Stack.Limits.Global.QueryLimits.MaxQuerySeries }}
  cardinality_limit: 100000
  max_streams_matchers_per_query: 1000
  max_cache_freshness_per_query: 10m
  per_stream_rate_limit: 3MB
  per_stream_rate_limit_burst: 15MB
  split_queries_by_interval: 30m
memberlist:
  abort_if_cluster_join_fails: true
  bind_port: {{ .GossipRing.Port }}
  join_members:
    - {{ .GossipRing.FQDN }}:{{ .GossipRing.Port }}
  max_join_backoff: 1m
  max_join_retries: 10
  min_join_backoff: 1s
querier:
  engine:
    max_look_back_period: 30s
    timeout: 3m
  extra_query_delay: 0s
  query_ingesters_within: 3h
  query_timeout: 1m
  tail_max_duration: 1h
  max_concurrent: {{ .MaxConcurrent.AvailableQuerierCPUCores }}
query_range:
  align_queries_with_step: true
  cache_results: true
  max_retries: 5
  results_cache:
    cache:
      enable_fifocache: true
      fifocache:
        max_size_bytes: 500MB
  parallelise_shardable_queries: true
schema_config:
  configs:
    - from: "2020-10-01"
      index:
        period: 24h
        prefix: index_
      object_store: {{ .ObjectStorage.SharedStore }}
      schema: v11
      store: boltdb-shipper

{{ if .Ruler.Enabled }}
ruler:
  enable_api: true
  enable_sharding: true
  {{ with .Ruler.EvaluationInterval }}
  evaluation_interval: {{ . }}
  {{ end }}
  {{ with .Ruler.PollInterval }}
  poll_interval: {{ . }}
  {{ end }}
  {{ with .Ruler.AlertManager }}
  external_url: {{ .ExternalURL }}
  {{ with .ExternalLabels }}
  external_labels:
    {{ range $name, $value := . }}
    {{ $name }}: {{ $value }}
    {{ end }}
  {{ end}}
  alertmanager_url: {{ .Hosts }}
  {{ if .EnableV2 }}
  enable_alertmanager_v2: {{ .EnableV2 }}
  {{ end }}
  {{ if .EnableDiscovery }}
  enable_alertmanager_discovery: {{ .EnableDiscovery }}
  alertmanager_refresh_interval: {{ .RefreshInterval }}
  {{ end }}
  {{ if .QueueCapacity }}
  notification_queue_capacity: {{ .QueueCapacity }}
  {{ end }}
  {{ if .Timeout }}
  notification_timeout: {{ .Timeout }}
  {{ end }}
  {{ if .ForOutageTolerance }}
  for_outage_tolerance: {{ .ForOutageTolerance }}
  {{ end }}
  {{ if .ForGracePeriod }}
  for_grace_period: {{ .ForGracePeriod }}
  {{ end }}
  {{ if .ResendDelay }}
  resend_delay: {{ .ResendDelay }}
  {{ end }}
  {{ end}}
  {{ with .Ruler.RemoteWrite }}
  {{ if .Enabled }}
  remote_write:
    enabled: {{ .Enabled }}
    config_refresh_period: {{ .RefreshPeriod }}
    client:
      {{  with .Client }}
      name: {{ .Name }}
      url: {{ .URL }}
      timeout: {{ .RemoteTimeout }}
      proxy_url: {{ .ProxyURL }}
      follow_redirects: {{ .FollowRedirects }}
      headers:
      {{ range $key, $val := .Headers }}
        "{{ $key }}": "{{ $val }}"
      {{ end }}
      {{ if and .BasicAuthUsername .BasicAuthPassword }}
      basic_auth:
        username: {{ .BasicAuthUsername }}
        password: {{ .BasicAuthPassword }}
      {{ end }}
      {{ if .BearerToken }}
      authorization:
        type: bearer
        credentials: {{ .BearerToken }}
      {{ end }}
      {{ end}}
      {{ with .RelabelConfigs }}
      write_relabel_configs:
        {{ range $k, $cfg := . }}
        - {{ if $cfg.SourceLabels}}
          source_labels: {{ $cfg.SourceLabelsString }}
          {{ end }}
          {{ if $cfg.Regex }}
          regex: {{ $cfg.Regex }}
          {{ end }}
          {{ if $cfg.Action }}
          action: {{ $cfg.Action }}
          {{ end }}
          separator: {{ $cfg.SeparatorString }}
          {{ if $cfg.Replacement }}
          replacement: {{ $cfg.Replacement }}
          {{ end }}
          {{ if $cfg.TargetLabel }}
          target_label: {{ $cfg.TargetLabel }}
          {{ end }}
          {{ if $cfg.Modulus }}
          modulus: {{ $cfg.Modulus }}
          {{ end }}
        {{ end}}
      {{end }}
      {{ with .Queue }}
      queue_config:
        {{ if .Capacity }}
        capacity: {{ .Capacity }}
        {{ end }}
        {{ if .MaxShards }}
        max_shards: {{ .MaxShards }}
        {{ end }}
        {{ if .MinShards }}
        min_shards: {{ .MinShards }}
        {{ end }}
        {{ if .MaxSamplesPerSend }}
        max_samples_per_send: {{ .MaxSamplesPerSend }}
        {{ end }}
        {{ if .BatchSendDeadline }}
        batch_send_deadline: {{ .BatchSendDeadline }}
        {{ end }}
        {{ if .MinBackOffPeriod }}
        min_backoff: {{ .MinBackOffPeriod }}
        {{ end }}
        {{ if .MaxBackOffPeriod }}
        max_backoff: {{ .MaxBackOffPeriod }}
        {{ end }}
      {{ end }}
  {{ end }}
  {{ end }}
  wal:
    dir: {{ .WriteAheadLog.Directory }}
    truncate_frequency: 60m
    min_age: 5m
    max_age: 4h
  rule_path: {{ .StorageDirectory }}
  storage:
    type: local
    local:
      directory: {{ .Ruler.RulesStorageDirectory }}
  ring:
    kvstore:
      store: memberlist
{{ end }}
server:
  graceful_shutdown_timeout: 5s
  grpc_server_min_time_between_pings: '10s'
  grpc_server_ping_without_stream_allowed: true
  grpc_server_max_concurrent_streams: 1000
  grpc_server_max_recv_msg_size: 104857600
  grpc_server_max_send_msg_size: 104857600
  http_listen_port: 3100
  http_server_idle_timeout: 120s
  http_server_write_timeout: 1m
  log_level: info
storage_config:
  boltdb_shipper:
    active_index_directory: {{ .StorageDirectory }}/index
    cache_location: {{ .StorageDirectory }}/index_cache
    cache_ttl: 24h
    resync_interval: 5m
    shared_store: {{ .ObjectStorage.SharedStore }}
    index_gateway_client:
      server_address: dns:///{{ .IndexGateway.FQDN }}:{{ .IndexGateway.Port }}
tracing:
  enabled: false
analytics:
  reporting_enabled: {{ .EnableRemoteReporting }}
