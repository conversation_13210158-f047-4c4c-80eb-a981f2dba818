apiVersion: v1
data:
  controller_manager_config.yaml: |
    apiVersion: controller-runtime.sigs.k8s.io/v1alpha1
    kind: ControllerManagerConfig
    health:
      healthProbeBindAddress: :8081
    metrics:
      bindAddress: 127.0.0.1:8080
    webhook:
      port: 9443
    leaderElection:
      leaderElect: true
      resourceName: e3716011.grafana.com
kind: ConfigMap
metadata:
  labels:
    app.kubernetes.io/instance: loki-operator-v0.0.1
    app.kubernetes.io/managed-by: operator-lifecycle-manager
    app.kubernetes.io/name: loki-operator
    app.kubernetes.io/part-of: cluster-logging
    app.kubernetes.io/version: 0.0.1
  name: loki-operator-manager-config
