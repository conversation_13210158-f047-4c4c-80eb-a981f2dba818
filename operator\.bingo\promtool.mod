module _ // Auto generated by https://github.com/bwplotka/bingo. DO NOT EDIT

go 1.17

exclude github.com/linode/linodego v1.0.0

exclude github.com/grpc-ecosystem/grpc-gateway v1.14.7

exclude google.golang.org/api v0.30.0

exclude (
	k8s.io/client-go v1.4.0
	k8s.io/client-go v1.4.0+incompatible
	k8s.io/client-go v1.5.0
	k8s.io/client-go v1.5.0+incompatible
	k8s.io/client-go v1.5.1
	k8s.io/client-go v1.5.1+incompatible
	k8s.io/client-go v10.0.0+incompatible
	k8s.io/client-go v11.0.0+incompatible
	k8s.io/client-go v2.0.0+incompatible
	k8s.io/client-go v2.0.0-alpha.1+incompatible
	k8s.io/client-go v3.0.0+incompatible
	k8s.io/client-go v3.0.0-beta.0+incompatible
	k8s.io/client-go v4.0.0+incompatible
	k8s.io/client-go v4.0.0-beta.0+incompatible
	k8s.io/client-go v5.0.0+incompatible
	k8s.io/client-go v5.0.1+incompatible
	k8s.io/client-go v6.0.0+incompatible
	k8s.io/client-go v7.0.0+incompatible
	k8s.io/client-go v8.0.0+incompatible
	k8s.io/client-go v9.0.0+incompatible
	k8s.io/client-go v9.0.0-invalid+incompatible
)

replace k8s.io/klog => github.com/simonpasquier/klog-gokit v0.3.0

replace k8s.io/klog/v2 => github.com/simonpasquier/klog-gokit/v3 v3.0.0

require github.com/prometheus/prometheus v1.8.2-0.20220211202545-56e14463bccf // cmd/promtool
