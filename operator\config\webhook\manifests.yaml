---
apiVersion: admissionregistration.k8s.io/v1
kind: ValidatingWebhookConfiguration
metadata:
  creationTimestamp: null
  name: validating-webhook-configuration
webhooks:
- admissionReviewVersions:
  - v1
  clientConfig:
    service:
      name: webhook-service
      namespace: system
      path: /validate-loki-grafana-com-v1beta1-alertingrule
  failurePolicy: Fail
  name: valertingrule.kb.io
  rules:
  - apiGroups:
    - loki.grafana.com
    apiVersions:
    - v1beta1
    operations:
    - CREATE
    - UPDATE
    resources:
    - alertingrules
  sideEffects: None
- admissionReviewVersions:
  - v1
  clientConfig:
    service:
      name: webhook-service
      namespace: system
      path: /validate-loki-grafana-com-v1beta1-recordingrule
  failurePolicy: Fail
  name: vrecordingrule.kb.io
  rules:
  - apiGroups:
    - loki.grafana.com
    apiVersions:
    - v1beta1
    operations:
    - CREATE
    - UPDATE
    resources:
    - recordingrules
  sideEffects: None
