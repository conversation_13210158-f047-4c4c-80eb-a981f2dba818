name: "publish-technical-documentation-next"

on:
  push:
    branches:
      - "main"
    paths:
      - "docs/sources/**"
  workflow_dispatch:
jobs:
  test:
    runs-on: "ubuntu-latest"
    steps:
      - name: "Check out code"
        uses: "actions/checkout@v3"
      - name: "Build website"
        # -e HUGO_REFLINKSERRORLEVEL=ERROR prevents merging broken refs with the downside
        # that no refs to external content can be used as these refs will not resolve in the
        # docs-base image.
        run: |
          docker run -v ${PWD}/docs/sources:/hugo/content/docs/loki/next -e HUGO_REFLINKSERRORLEVEL=ERROR --rm grafana/docs-base:latest /bin/bash -c 'make hugo'

  sync:
    runs-on: "ubuntu-latest"
    needs: "test"
    steps:
      - name: "Check out code"
        uses: "actions/checkout@v3"

      - name: "Clone website-sync Action"
        run: "git clone --single-branch --no-tags --depth 1 -b master https://grafanabot:${{ secrets.GH_BOT_ACCESS_TOKEN }}@github.com/grafana/website-sync ./.github/actions/website-sync"

      - name: "Publish to website repository (next)"
        uses: "./.github/actions/website-sync"
        id: "publish-next"
        with:
          repository: "grafana/website"
          branch: "master"
          host: "github.com"
          github_pat: "${{ secrets.GH_BOT_ACCESS_TOKEN }}"
          source_folder: "docs/sources"
          target_folder: "content/docs/loki/next"
