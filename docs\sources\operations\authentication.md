---
title: Authentication
weight: 10
---
# Authentication with <PERSON><PERSON> Loki

Grafana Loki does not come with any included authentication layer. Operators are
expected to run an authenticating reverse proxy in front of your services, such
as NGINX using basic auth or an OAuth2 proxy.

Note that when using <PERSON> in multi-tenant mode, Loki requires the HTTP header
`X-Scope-OrgID` to be set to a string identifying the tenant; the responsibility
of populating this value should be handled by the authenticating reverse proxy.
Read the [multi-tenancy](../multi-tenancy/) documentation for more information.

For information on authenticating Promtail, please see the docs for [how to
configure Promtail](../../clients/promtail/configuration/).
