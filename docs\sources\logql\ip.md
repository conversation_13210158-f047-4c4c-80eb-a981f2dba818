---
title: Matching IP addresses
weight: 40
---

# Matching IP addresses

LogQL supports matching IP addresses.

With logs such as

```
********** - - [17/May/2015:08:05:32 +0000] "GET /downloads/product_1 HTTP/1.1" 304 0 "-" "Debian APT-HTTP/1.3 (0.8.16~exp12ubuntu10.21)"
************ - - [17/May/2015:08:05:14 +0000] "GET /downloads/product_1 HTTP/1.1" 304 0 "-" "Debian APT-HTTP/1.3 (0.8.16~exp12ubuntu10.16)"
********** - - [17/May/2015:08:05:45 +0000] "GET /downloads/product_1 HTTP/1.1" 404 318 "-" "Debian APT-HTTP/1.3 (1.0.1ubuntu2)"
9********** - - [17/May/2015:08:05:26 +0000] "GET /downloads/product_1 HTTP/1.1" 404 324 "-" "Debian APT-HTTP/1.3 (0.8.16~exp12ubuntu10.21)"
```

the LogQL line filter is not sufficient.
A line filter such as

```logql
{job_name="myapp"} |= "**********"
```

also matches example IP addresses such as 9**********. A better choice uses a regexp: `|~"^**********"`. This regexp does not handle IPv6 addresses, and it does not match a range of IP addresses.

The LogQL support for matching IP addresses handles both IPv4 and IPv6 single addresses, as well as ranges within IP addresses
and CIDR patterns.

Match IP addresses with the syntax: `ip("<pattern>")`.
The `<pattern>` can be:

-  A single IP address. Examples: `ip("*********")`, `ip("::1")`
-  A range within the IP address. Examples: `ip("***********-*************")`, `ip("2001:db8::1-2001:db8::8")`
-  A CIDR specification. Examples: `ip("************/24")`, `ip("2001:db8::/32")`

The IP matching can be used in both line filter and label filter expressions.
When specifying line filter expressions, only the `|=` and `!=` operations are allowed.
When specifying label filter expressions, only the  `=` and `!=` operations are allowed.

- Line filter examples

    ```logql
    {job_name="myapp"} |= ip("***********/16")
    ```

    Return log lines that do not match with an IPv4 range:

    ```logql
    {job_name="myapp"} != ip("***********-***********0")
    ```

- Label filter examples

    ```logql
    {job_name="myapp"}
		| logfmt
		| remote_addr = ip("2001:db8::1-2001:db8::8")
		| level = "error"
    ```

    Filters can be chained. This example matches log lines with all IPv4 subnet values `***********/16` except IP address `***********`:

    ```logql
    {job_name="myapp"}
		| logfmt
		| addr = ip("***********/16")
		| addr != ip("***********")
    ```
