// Code generated by goyacc -p JSONExpr -o pkg/logql/log/jsonexpr/jsonexpr.y.go pkg/logql/log/jsonexpr/jsonexpr.y. DO NOT EDIT.

//line pkg/logql/log/jsonexpr/jsonexpr.y:4
package jsonexpr

import __yyfmt__ "fmt"

//line pkg/logql/log/jsonexpr/jsonexpr.y:4

func setScannerData(lex interface{}, data []interface{}) {
	lex.(*Scanner).data = data
}

//line pkg/logql/log/jsonexpr/jsonexpr.y:12
type JSONExprSymType struct {
	yys   int
	empty struct{}
	str   string
	field string
	list  []interface{}
	int   int
}

const DOT = 57346
const LSB = 57347
const RSB = 57348
const STRING = 57349
const FIELD = 57350
const INDEX = 57351

var JSONExprToknames = [...]string{
	"$end",
	"error",
	"$unk",
	"DOT",
	"LSB",
	"RSB",
	"STRING",
	"FIELD",
	"INDEX",
}

var JSONExprStatenames = [...]string{}

const JSONExprEofCode = 1
const JSONExprErrCode = 2
const JSONExprInitialStackSize = 16

//line yacctab:1
var JSONExprExca = [...]int{
	-1, 1,
	1, -1,
	-2, 0,
}

const JSONExprPrivate = 57344

const JSONExprLast = 19

var JSONExprAct = [...]int{
	3, 13, 7, 14, 6, 6, 17, 16, 10, 7,
	4, 15, 5, 8, 1, 9, 2, 11, 12,
}

var JSONExprPact = [...]int{
	-3, -1000, 4, -1000, -1000, -1000, -1000, -6, -1000, -1000,
	-4, 1, 0, -1000, -1000, -1000, -1000, -1000,
}

var JSONExprPgo = [...]int{
	0, 18, 12, 0, 17, 10, 16, 14,
}

var JSONExprR1 = [...]int{
	0, 7, 6, 6, 6, 6, 6, 6, 5, 2,
	3, 4, 1,
}

var JSONExprR2 = [...]int{
	0, 1, 1, 1, 1, 2, 2, 3, 3, 3,
	1, 1, 1,
}

var JSONExprChk = [...]int{
	-1000, -7, -6, -3, -5, -2, 8, 5, -5, -2,
	4, -4, -1, 7, 9, -3, 6, 6,
}

var JSONExprDef = [...]int{
	0, -2, 1, 2, 3, 4, 10, 0, 5, 6,
	0, 0, 0, 11, 12, 7, 8, 9,
}

var JSONExprTok1 = [...]int{
	1,
}

var JSONExprTok2 = [...]int{
	2, 3, 4, 5, 6, 7, 8, 9,
}

var JSONExprTok3 = [...]int{
	0,
}

var JSONExprErrorMessages = [...]struct {
	state int
	token int
	msg   string
}{}

//line yaccpar:1

/*	parser for yacc output	*/

var (
	JSONExprDebug        = 0
	JSONExprErrorVerbose = false
)

type JSONExprLexer interface {
	Lex(lval *JSONExprSymType) int
	Error(s string)
}

type JSONExprParser interface {
	Parse(JSONExprLexer) int
	Lookahead() int
}

type JSONExprParserImpl struct {
	lval  JSONExprSymType
	stack [JSONExprInitialStackSize]JSONExprSymType
	char  int
}

func (p *JSONExprParserImpl) Lookahead() int {
	return p.char
}

func JSONExprNewParser() JSONExprParser {
	return &JSONExprParserImpl{}
}

const JSONExprFlag = -1000

func JSONExprTokname(c int) string {
	if c >= 1 && c-1 < len(JSONExprToknames) {
		if JSONExprToknames[c-1] != "" {
			return JSONExprToknames[c-1]
		}
	}
	return __yyfmt__.Sprintf("tok-%v", c)
}

func JSONExprStatname(s int) string {
	if s >= 0 && s < len(JSONExprStatenames) {
		if JSONExprStatenames[s] != "" {
			return JSONExprStatenames[s]
		}
	}
	return __yyfmt__.Sprintf("state-%v", s)
}

func JSONExprErrorMessage(state, lookAhead int) string {
	const TOKSTART = 4

	if !JSONExprErrorVerbose {
		return "syntax error"
	}

	for _, e := range JSONExprErrorMessages {
		if e.state == state && e.token == lookAhead {
			return "syntax error: " + e.msg
		}
	}

	res := "syntax error: unexpected " + JSONExprTokname(lookAhead)

	// To match Bison, suggest at most four expected tokens.
	expected := make([]int, 0, 4)

	// Look for shiftable tokens.
	base := JSONExprPact[state]
	for tok := TOKSTART; tok-1 < len(JSONExprToknames); tok++ {
		if n := base + tok; n >= 0 && n < JSONExprLast && JSONExprChk[JSONExprAct[n]] == tok {
			if len(expected) == cap(expected) {
				return res
			}
			expected = append(expected, tok)
		}
	}

	if JSONExprDef[state] == -2 {
		i := 0
		for JSONExprExca[i] != -1 || JSONExprExca[i+1] != state {
			i += 2
		}

		// Look for tokens that we accept or reduce.
		for i += 2; JSONExprExca[i] >= 0; i += 2 {
			tok := JSONExprExca[i]
			if tok < TOKSTART || JSONExprExca[i+1] == 0 {
				continue
			}
			if len(expected) == cap(expected) {
				return res
			}
			expected = append(expected, tok)
		}

		// If the default action is to accept or reduce, give up.
		if JSONExprExca[i+1] != 0 {
			return res
		}
	}

	for i, tok := range expected {
		if i == 0 {
			res += ", expecting "
		} else {
			res += " or "
		}
		res += JSONExprTokname(tok)
	}
	return res
}

func JSONExprlex1(lex JSONExprLexer, lval *JSONExprSymType) (char, token int) {
	token = 0
	char = lex.Lex(lval)
	if char <= 0 {
		token = JSONExprTok1[0]
		goto out
	}
	if char < len(JSONExprTok1) {
		token = JSONExprTok1[char]
		goto out
	}
	if char >= JSONExprPrivate {
		if char < JSONExprPrivate+len(JSONExprTok2) {
			token = JSONExprTok2[char-JSONExprPrivate]
			goto out
		}
	}
	for i := 0; i < len(JSONExprTok3); i += 2 {
		token = JSONExprTok3[i+0]
		if token == char {
			token = JSONExprTok3[i+1]
			goto out
		}
	}

out:
	if token == 0 {
		token = JSONExprTok2[1] /* unknown char */
	}
	if JSONExprDebug >= 3 {
		__yyfmt__.Printf("lex %s(%d)\n", JSONExprTokname(token), uint(char))
	}
	return char, token
}

func JSONExprParse(JSONExprlex JSONExprLexer) int {
	return JSONExprNewParser().Parse(JSONExprlex)
}

func (JSONExprrcvr *JSONExprParserImpl) Parse(JSONExprlex JSONExprLexer) int {
	var JSONExprn int
	var JSONExprVAL JSONExprSymType
	var JSONExprDollar []JSONExprSymType
	_ = JSONExprDollar // silence set and not used
	JSONExprS := JSONExprrcvr.stack[:]

	Nerrs := 0   /* number of errors */
	Errflag := 0 /* error recovery flag */
	JSONExprstate := 0
	JSONExprrcvr.char = -1
	JSONExprtoken := -1 // JSONExprrcvr.char translated into internal numbering
	defer func() {
		// Make sure we report no lookahead when not parsing.
		JSONExprstate = -1
		JSONExprrcvr.char = -1
		JSONExprtoken = -1
	}()
	JSONExprp := -1
	goto JSONExprstack

ret0:
	return 0

ret1:
	return 1

JSONExprstack:
	/* put a state and value onto the stack */
	if JSONExprDebug >= 4 {
		__yyfmt__.Printf("char %v in %v\n", JSONExprTokname(JSONExprtoken), JSONExprStatname(JSONExprstate))
	}

	JSONExprp++
	if JSONExprp >= len(JSONExprS) {
		nyys := make([]JSONExprSymType, len(JSONExprS)*2)
		copy(nyys, JSONExprS)
		JSONExprS = nyys
	}
	JSONExprS[JSONExprp] = JSONExprVAL
	JSONExprS[JSONExprp].yys = JSONExprstate

JSONExprnewstate:
	JSONExprn = JSONExprPact[JSONExprstate]
	if JSONExprn <= JSONExprFlag {
		goto JSONExprdefault /* simple state */
	}
	if JSONExprrcvr.char < 0 {
		JSONExprrcvr.char, JSONExprtoken = JSONExprlex1(JSONExprlex, &JSONExprrcvr.lval)
	}
	JSONExprn += JSONExprtoken
	if JSONExprn < 0 || JSONExprn >= JSONExprLast {
		goto JSONExprdefault
	}
	JSONExprn = JSONExprAct[JSONExprn]
	if JSONExprChk[JSONExprn] == JSONExprtoken { /* valid shift */
		JSONExprrcvr.char = -1
		JSONExprtoken = -1
		JSONExprVAL = JSONExprrcvr.lval
		JSONExprstate = JSONExprn
		if Errflag > 0 {
			Errflag--
		}
		goto JSONExprstack
	}

JSONExprdefault:
	/* default state action */
	JSONExprn = JSONExprDef[JSONExprstate]
	if JSONExprn == -2 {
		if JSONExprrcvr.char < 0 {
			JSONExprrcvr.char, JSONExprtoken = JSONExprlex1(JSONExprlex, &JSONExprrcvr.lval)
		}

		/* look through exception table */
		xi := 0
		for {
			if JSONExprExca[xi+0] == -1 && JSONExprExca[xi+1] == JSONExprstate {
				break
			}
			xi += 2
		}
		for xi += 2; ; xi += 2 {
			JSONExprn = JSONExprExca[xi+0]
			if JSONExprn < 0 || JSONExprn == JSONExprtoken {
				break
			}
		}
		JSONExprn = JSONExprExca[xi+1]
		if JSONExprn < 0 {
			goto ret0
		}
	}
	if JSONExprn == 0 {
		/* error ... attempt to resume parsing */
		switch Errflag {
		case 0: /* brand new error */
			JSONExprlex.Error(JSONExprErrorMessage(JSONExprstate, JSONExprtoken))
			Nerrs++
			if JSONExprDebug >= 1 {
				__yyfmt__.Printf("%s", JSONExprStatname(JSONExprstate))
				__yyfmt__.Printf(" saw %s\n", JSONExprTokname(JSONExprtoken))
			}
			fallthrough

		case 1, 2: /* incompletely recovered error ... try again */
			Errflag = 3

			/* find a state where "error" is a legal shift action */
			for JSONExprp >= 0 {
				JSONExprn = JSONExprPact[JSONExprS[JSONExprp].yys] + JSONExprErrCode
				if JSONExprn >= 0 && JSONExprn < JSONExprLast {
					JSONExprstate = JSONExprAct[JSONExprn] /* simulate a shift of "error" */
					if JSONExprChk[JSONExprstate] == JSONExprErrCode {
						goto JSONExprstack
					}
				}

				/* the current p has no shift on "error", pop stack */
				if JSONExprDebug >= 2 {
					__yyfmt__.Printf("error recovery pops state %d\n", JSONExprS[JSONExprp].yys)
				}
				JSONExprp--
			}
			/* there is no state on the stack with an error shift ... abort */
			goto ret1

		case 3: /* no shift yet; clobber input char */
			if JSONExprDebug >= 2 {
				__yyfmt__.Printf("error recovery discards %s\n", JSONExprTokname(JSONExprtoken))
			}
			if JSONExprtoken == JSONExprEofCode {
				goto ret1
			}
			JSONExprrcvr.char = -1
			JSONExprtoken = -1
			goto JSONExprnewstate /* try again in the same state */
		}
	}

	/* reduction by production JSONExprn */
	if JSONExprDebug >= 2 {
		__yyfmt__.Printf("reduce %v in:\n\t%v\n", JSONExprn, JSONExprStatname(JSONExprstate))
	}

	JSONExprnt := JSONExprn
	JSONExprpt := JSONExprp
	_ = JSONExprpt // guard against "declared and not used"

	JSONExprp -= JSONExprR2[JSONExprn]
	// JSONExprp is now the index of $0. Perform the default action. Iff the
	// reduced production is ε, $1 is possibly out of range.
	if JSONExprp+1 >= len(JSONExprS) {
		nyys := make([]JSONExprSymType, len(JSONExprS)*2)
		copy(nyys, JSONExprS)
		JSONExprS = nyys
	}
	JSONExprVAL = JSONExprS[JSONExprp+1]

	/* consult goto table to find next state */
	JSONExprn = JSONExprR1[JSONExprn]
	JSONExprg := JSONExprPgo[JSONExprn]
	JSONExprj := JSONExprg + JSONExprS[JSONExprp].yys + 1

	if JSONExprj >= JSONExprLast {
		JSONExprstate = JSONExprAct[JSONExprg]
	} else {
		JSONExprstate = JSONExprAct[JSONExprj]
		if JSONExprChk[JSONExprstate] != -JSONExprn {
			JSONExprstate = JSONExprAct[JSONExprg]
		}
	}
	// dummy call; replaced with literal code
	switch JSONExprnt {

	case 1:
		JSONExprDollar = JSONExprS[JSONExprpt-1 : JSONExprpt+1]
//line pkg/logql/log/jsonexpr/jsonexpr.y:32
		{
			setScannerData(JSONExprlex, JSONExprDollar[1].list)
		}
	case 2:
		JSONExprDollar = JSONExprS[JSONExprpt-1 : JSONExprpt+1]
//line pkg/logql/log/jsonexpr/jsonexpr.y:35
		{
			JSONExprVAL.list = []interface{}{JSONExprDollar[1].str}
		}
	case 3:
		JSONExprDollar = JSONExprS[JSONExprpt-1 : JSONExprpt+1]
//line pkg/logql/log/jsonexpr/jsonexpr.y:36
		{
			JSONExprVAL.list = []interface{}{JSONExprDollar[1].str}
		}
	case 4:
		JSONExprDollar = JSONExprS[JSONExprpt-1 : JSONExprpt+1]
//line pkg/logql/log/jsonexpr/jsonexpr.y:37
		{
			JSONExprVAL.list = []interface{}{JSONExprDollar[1].int}
		}
	case 5:
		JSONExprDollar = JSONExprS[JSONExprpt-2 : JSONExprpt+1]
//line pkg/logql/log/jsonexpr/jsonexpr.y:38
		{
			JSONExprVAL.list = append(JSONExprDollar[1].list, JSONExprDollar[2].str)
		}
	case 6:
		JSONExprDollar = JSONExprS[JSONExprpt-2 : JSONExprpt+1]
//line pkg/logql/log/jsonexpr/jsonexpr.y:39
		{
			JSONExprVAL.list = append(JSONExprDollar[1].list, JSONExprDollar[2].int)
		}
	case 7:
		JSONExprDollar = JSONExprS[JSONExprpt-3 : JSONExprpt+1]
//line pkg/logql/log/jsonexpr/jsonexpr.y:40
		{
			JSONExprVAL.list = append(JSONExprDollar[1].list, JSONExprDollar[3].str)
		}
	case 8:
		JSONExprDollar = JSONExprS[JSONExprpt-3 : JSONExprpt+1]
//line pkg/logql/log/jsonexpr/jsonexpr.y:44
		{
			JSONExprVAL.str = JSONExprDollar[2].str
		}
	case 9:
		JSONExprDollar = JSONExprS[JSONExprpt-3 : JSONExprpt+1]
//line pkg/logql/log/jsonexpr/jsonexpr.y:47
		{
			JSONExprVAL.int = JSONExprDollar[2].int
		}
	case 10:
		JSONExprDollar = JSONExprS[JSONExprpt-1 : JSONExprpt+1]
//line pkg/logql/log/jsonexpr/jsonexpr.y:50
		{
			JSONExprVAL.str = JSONExprDollar[1].field
		}
	case 11:
		JSONExprDollar = JSONExprS[JSONExprpt-1 : JSONExprpt+1]
//line pkg/logql/log/jsonexpr/jsonexpr.y:53
		{
			JSONExprVAL.str = JSONExprDollar[1].str
		}
	case 12:
		JSONExprDollar = JSONExprS[JSONExprpt-1 : JSONExprpt+1]
//line pkg/logql/log/jsonexpr/jsonexpr.y:56
		{
			JSONExprVAL.int = JSONExprDollar[1].int
		}
	}
	goto JSONExprstack /* stack new state and value */
}
