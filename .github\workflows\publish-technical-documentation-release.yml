name: "publish-technical-documentation-release"

on:
  push:
    branches:
      - "release-*"
    tags:
      - "v[0-9]+.[0-9]+.[0-9]+"
    paths:
      - "docs/sources/**"
  workflow_dispatch:
jobs:
  test:
    runs-on: "ubuntu-latest"
    steps:
      - name: "Check out code"
        uses: "actions/checkout@v3"
      - name:
          "Build website"
          # -e HUGO_REFLINKSERRORLEVEL=ERROR prevents merging broken refs with the downside
          # that no refs to external content can be used as these refs will not resolve in the
          # docs-base image.
        run: |
          docker run -v ${PWD}/docs/sources:/hugo/content/docs/loki/release -e HUGO_REFLINKSERRORLEVEL=ERROR --rm grafana/docs-base:latest /bin/bash -c 'make hugo'

  sync:
    runs-on: "ubuntu-latest"
    needs: "test"
    steps:
      - name: "Checkout code and tags"
        uses: "actions/checkout@v3"
        with:
          fetch-depth: 0

      - name: "Checkout Actions library"
        uses: "actions/checkout@v3"
        with:
          repository: "grafana/grafana-github-actions"
          path: "./actions"

      - name: "Install Actions from library"
        run: "npm install --production --prefix ./actions"

      - name: "Determine if there is a matching release tag"
        id: "has-matching-release-tag"
        uses: "./actions/has-matching-release-tag"
        with:
          ref_name: "${{ github.ref_name }}"
          release_tag_regexp: "^v(0|[1-9]\\d*)\\.(0|[1-9]\\d*)\\.(0|[1-9]\\d*)$"
          release_branch_regexp: "^release-(0|[1-9]\\d*)\\.(0|[1-9]\\d*)\\.x$"

      - name: "Determine technical documentation version"
        if: "steps.has-matching-release-tag.outputs.bool == 'true'"
        uses: "./actions/docs-target"
        id: "target"
        with:
          ref_name: "${{ github.ref_name }}"

      - name: "Clone website-sync Action"
        if: "steps.has-matching-release-tag.outputs.bool == 'true'"
        run: "git clone --single-branch --no-tags --depth 1 -b master https://grafanabot:${{ secrets.GH_BOT_ACCESS_TOKEN }}@github.com/grafana/website-sync ./.github/actions/website-sync"

      - name: "Publish to website repository (release)"
        if: "steps.has-matching-release-tag.outputs.bool == 'true'"
        uses: "./.github/actions/website-sync"
        id: "publish-release"
        with:
          repository: "grafana/website"
          branch: "master"
          host: "github.com"
          github_pat: "${{ secrets.GH_BOT_ACCESS_TOKEN }}"
          source_folder: "docs/sources"
          target_folder: "content/docs/loki/${{ steps.target.outputs.target }}.x"
