name: Github repo and issue stats collection
on:
  schedule:
    - cron: "*/30 * * * *"

jobs:
  main:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout Actions
        uses: actions/checkout@v3
        with:
          repository: "grafana/grafana-github-actions"
          path: ./actions
          ref: main
      - name: Install Actions
        run: npm install --production --prefix ./actions
      - name: Run metrics collector
        uses: ./actions/metrics-collector
        with:
          metricsWriteAPIKey: ${{secrets.GRAFANA_MISC_STATS_API_KEY}}
          token: ${{secrets.GH_BOT_ACCESS_TOKEN}}
