# Fluent Bit output plugin

[Fluent Bit](https://fluentbit.io/) is a Fast and Lightweight Data Forwarder, it can be configured with the [Loki output plugin](https://fluentbit.io/documentation/0.12/output/) to ship logs to <PERSON>. You can define which log files you want to collect using the [`Tail`](https://fluentbit.io/documentation/0.12/input/tail.html) or [`Stdin`](https://docs.fluentbit.io/manual/pipeline/inputs/standard-input) [input plugin](https://fluentbit.io/documentation/0.12/getting_started/input.html). Additionally Fluent Bit supports multiple `Filter` and `Parser` plugins (`Kubernetes`, `JSON`, etc..) to structure and alter log lines.

This plugin is implemented with [Fluent Bit's Go plugin](https://github.com/fluent/fluent-bit-go) interface. It pushes logs to Loki using a GRPC connection.

> syslog and systemd input plugin have not been tested yet, feedback appreciated.

## Building

Prerequisites:

* Go 1.16+
* gcc (for cgo)

To build the output plugin library file (`out_grafana_loki.so`), you can use:

```bash
make fluent-bit-plugin
```

You can also build the docker image with the plugin pre-installed using:

```bash
make fluent-bit-image
```

Finally if you want to test you can use `make fluent-bit-test` to send some logs to your local Loki instance.
