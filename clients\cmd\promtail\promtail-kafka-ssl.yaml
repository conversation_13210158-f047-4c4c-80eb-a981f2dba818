server:
  http_listen_port: 9080
  grpc_listen_port: 0

clients:
  - url: http://localhost:3100/loki/api/v1/push

scrape_configs:
  - job_name: kafka-mtls
    kafka:
      use_incoming_timestamp: false
      brokers:
        - localhost:29092
      authentication:
        type: ssl
        tls_config:
          ca_file: ../../../tools/kafka/secrets/promtail-kafka-ca.pem
          cert_file: ../../../tools/kafka/secrets/kafka.consumer.keystore.cer.pem
          key_file: ../../../tools/kafka/secrets/kafka.consumer.keystore.key.pem
          server_name: localhost
          insecure_skip_verify: true
      group_id: kafka_mtls_group
      topics:
        - foo
        - ^promtail.*
      labels:
        job: kafka-mtls