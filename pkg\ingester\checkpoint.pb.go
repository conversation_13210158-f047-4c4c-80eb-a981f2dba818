// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: pkg/ingester/checkpoint.proto

package ingester

import (
	bytes "bytes"
	fmt "fmt"
	_ "github.com/gogo/protobuf/gogoproto"
	proto "github.com/gogo/protobuf/proto"
	_ "github.com/gogo/protobuf/types"
	github_com_gogo_protobuf_types "github.com/gogo/protobuf/types"
	_ "github.com/grafana/loki/pkg/logproto"
	github_com_grafana_loki_pkg_logproto "github.com/grafana/loki/pkg/logproto"
	io "io"
	math "math"
	math_bits "math/bits"
	reflect "reflect"
	strings "strings"
	time "time"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf
var _ = time.Kitchen

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion3 // please upgrade the proto package

// Chunk is a {de,}serializable intermediate type for chunkDesc which allows
// efficient loading/unloading to disk during WAL checkpoint recovery.
type Chunk struct {
	From        time.Time `protobuf:"bytes,1,opt,name=from,proto3,stdtime" json:"from"`
	To          time.Time `protobuf:"bytes,2,opt,name=to,proto3,stdtime" json:"to"`
	FlushedAt   time.Time `protobuf:"bytes,3,opt,name=flushedAt,proto3,stdtime" json:"flushedAt"`
	LastUpdated time.Time `protobuf:"bytes,4,opt,name=lastUpdated,proto3,stdtime" json:"lastUpdated"`
	Closed      bool      `protobuf:"varint,5,opt,name=closed,proto3" json:"closed,omitempty"`
	Synced      bool      `protobuf:"varint,6,opt,name=synced,proto3" json:"synced,omitempty"`
	// data to be unmarshaled into a MemChunk
	Data []byte `protobuf:"bytes,7,opt,name=data,proto3" json:"data,omitempty"`
	// data to be unmarshaled into a MemChunk's headBlock
	Head []byte `protobuf:"bytes,8,opt,name=head,proto3" json:"head,omitempty"`
}

func (m *Chunk) Reset()      { *m = Chunk{} }
func (*Chunk) ProtoMessage() {}
func (*Chunk) Descriptor() ([]byte, []int) {
	return fileDescriptor_00f4b7152db9bdb5, []int{0}
}
func (m *Chunk) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Chunk) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Chunk.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *Chunk) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Chunk.Merge(m, src)
}
func (m *Chunk) XXX_Size() int {
	return m.Size()
}
func (m *Chunk) XXX_DiscardUnknown() {
	xxx_messageInfo_Chunk.DiscardUnknown(m)
}

var xxx_messageInfo_Chunk proto.InternalMessageInfo

func (m *Chunk) GetFrom() time.Time {
	if m != nil {
		return m.From
	}
	return time.Time{}
}

func (m *Chunk) GetTo() time.Time {
	if m != nil {
		return m.To
	}
	return time.Time{}
}

func (m *Chunk) GetFlushedAt() time.Time {
	if m != nil {
		return m.FlushedAt
	}
	return time.Time{}
}

func (m *Chunk) GetLastUpdated() time.Time {
	if m != nil {
		return m.LastUpdated
	}
	return time.Time{}
}

func (m *Chunk) GetClosed() bool {
	if m != nil {
		return m.Closed
	}
	return false
}

func (m *Chunk) GetSynced() bool {
	if m != nil {
		return m.Synced
	}
	return false
}

func (m *Chunk) GetData() []byte {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *Chunk) GetHead() []byte {
	if m != nil {
		return m.Head
	}
	return nil
}

// Series is a {de,}serializable intermediate type for Series.
type Series struct {
	UserID string `protobuf:"bytes,1,opt,name=userID,proto3" json:"userID,omitempty"`
	// post mapped fingerprint is necessary because subsequent wal writes will reference it.
	Fingerprint uint64                                              `protobuf:"varint,2,opt,name=fingerprint,proto3" json:"fingerprint,omitempty"`
	Labels      []github_com_grafana_loki_pkg_logproto.LabelAdapter `protobuf:"bytes,3,rep,name=labels,proto3,customtype=github.com/grafana/loki/pkg/logproto.LabelAdapter" json:"labels"`
	Chunks      []Chunk                                             `protobuf:"bytes,4,rep,name=chunks,proto3" json:"chunks"`
	// most recently pushed timestamp.
	To time.Time `protobuf:"bytes,5,opt,name=to,proto3,stdtime" json:"to"`
	// most recently pushed line.
	LastLine string `protobuf:"bytes,6,opt,name=lastLine,proto3" json:"lastLine,omitempty"`
	// highest counter value for pushes to this stream.
	// Used to skip already applied entries during WAL replay.
	EntryCt int64 `protobuf:"varint,7,opt,name=entryCt,proto3" json:"entryCt,omitempty"`
	// highest timestamp pushed to this stream.
	HighestTs time.Time `protobuf:"bytes,8,opt,name=highestTs,proto3,stdtime" json:"highestTs"`
}

func (m *Series) Reset()      { *m = Series{} }
func (*Series) ProtoMessage() {}
func (*Series) Descriptor() ([]byte, []int) {
	return fileDescriptor_00f4b7152db9bdb5, []int{1}
}
func (m *Series) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Series) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Series.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *Series) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Series.Merge(m, src)
}
func (m *Series) XXX_Size() int {
	return m.Size()
}
func (m *Series) XXX_DiscardUnknown() {
	xxx_messageInfo_Series.DiscardUnknown(m)
}

var xxx_messageInfo_Series proto.InternalMessageInfo

func (m *Series) GetUserID() string {
	if m != nil {
		return m.UserID
	}
	return ""
}

func (m *Series) GetFingerprint() uint64 {
	if m != nil {
		return m.Fingerprint
	}
	return 0
}

func (m *Series) GetChunks() []Chunk {
	if m != nil {
		return m.Chunks
	}
	return nil
}

func (m *Series) GetTo() time.Time {
	if m != nil {
		return m.To
	}
	return time.Time{}
}

func (m *Series) GetLastLine() string {
	if m != nil {
		return m.LastLine
	}
	return ""
}

func (m *Series) GetEntryCt() int64 {
	if m != nil {
		return m.EntryCt
	}
	return 0
}

func (m *Series) GetHighestTs() time.Time {
	if m != nil {
		return m.HighestTs
	}
	return time.Time{}
}

func init() {
	proto.RegisterType((*Chunk)(nil), "loki_ingester.Chunk")
	proto.RegisterType((*Series)(nil), "loki_ingester.Series")
}

func init() { proto.RegisterFile("pkg/ingester/checkpoint.proto", fileDescriptor_00f4b7152db9bdb5) }

var fileDescriptor_00f4b7152db9bdb5 = []byte{
	// 521 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x94, 0x52, 0x31, 0x8f, 0xd3, 0x30,
	0x14, 0x8e, 0xdb, 0x34, 0xd7, 0xba, 0xb0, 0x58, 0x08, 0x99, 0x22, 0xdc, 0xea, 0xa6, 0x2e, 0x24,
	0xa2, 0x30, 0xc0, 0x82, 0x74, 0x3d, 0x84, 0x84, 0xd4, 0x01, 0x85, 0x63, 0x61, 0x41, 0x6e, 0xe2,
	0x26, 0x51, 0xd3, 0x38, 0xb2, 0x9d, 0xa1, 0x1b, 0x3f, 0xe1, 0x06, 0x7e, 0x04, 0x3f, 0xe5, 0xc6,
	0x8e, 0x27, 0x90, 0x0e, 0x9a, 0x2e, 0x8c, 0xf7, 0x13, 0x90, 0x9d, 0xa4, 0x57, 0xb6, 0xeb, 0xf6,
	0xbe, 0xe7, 0xf7, 0xbd, 0x4f, 0xfe, 0xde, 0x07, 0x9f, 0xe5, 0xcb, 0xc8, 0x4b, 0xb2, 0x88, 0x49,
	0xc5, 0x84, 0x17, 0xc4, 0x2c, 0x58, 0xe6, 0x3c, 0xc9, 0x94, 0x9b, 0x0b, 0xae, 0x38, 0x7a, 0x98,
	0xf2, 0x65, 0xf2, 0xb5, 0x79, 0x1f, 0x3c, 0x8f, 0x12, 0x15, 0x17, 0x73, 0x37, 0xe0, 0x2b, 0x2f,
	0xe2, 0x11, 0xf7, 0xcc, 0xd4, 0xbc, 0x58, 0x18, 0x64, 0x80, 0xa9, 0x2a, 0xf6, 0x60, 0x18, 0x71,
	0x1e, 0xa5, 0xec, 0x6e, 0x4a, 0x25, 0x2b, 0x26, 0x15, 0x5d, 0xe5, 0xf5, 0xc0, 0x53, 0xad, 0x9e,
	0xf2, 0xa8, 0x62, 0x36, 0x45, 0xf5, 0x78, 0xfa, 0xab, 0x05, 0x3b, 0xe7, 0x71, 0x91, 0x2d, 0xd1,
	0x6b, 0x68, 0x2f, 0x04, 0x5f, 0x61, 0x30, 0x02, 0xe3, 0xfe, 0x64, 0xe0, 0x56, 0x6b, 0xdd, 0x66,
	0xad, 0x7b, 0xd1, 0xac, 0x9d, 0x76, 0xaf, 0x6e, 0x86, 0xd6, 0xe5, 0xef, 0x21, 0xf0, 0x0d, 0x03,
	0xbd, 0x82, 0x2d, 0xc5, 0x71, 0xeb, 0x08, 0x5e, 0x4b, 0x71, 0x34, 0x85, 0xbd, 0x45, 0x5a, 0xc8,
	0x98, 0x85, 0x67, 0x0a, 0xb7, 0x8f, 0x20, 0xdf, 0xd1, 0xd0, 0x7b, 0xd8, 0x4f, 0xa9, 0x54, 0x9f,
	0xf3, 0x90, 0x2a, 0x16, 0x62, 0xfb, 0x88, 0x2d, 0x87, 0x44, 0xf4, 0x18, 0x3a, 0x41, 0xca, 0x25,
	0x0b, 0x71, 0x67, 0x04, 0xc6, 0x5d, 0xbf, 0x46, 0xba, 0x2f, 0xd7, 0x59, 0xc0, 0x42, 0xec, 0x54,
	0xfd, 0x0a, 0x21, 0x04, 0xed, 0x90, 0x2a, 0x8a, 0x4f, 0x46, 0x60, 0xfc, 0xc0, 0x37, 0xb5, 0xee,
	0xc5, 0x8c, 0x86, 0xb8, 0x5b, 0xf5, 0x74, 0x7d, 0xfa, 0xbd, 0x0d, 0x9d, 0x4f, 0x4c, 0x24, 0x4c,
	0xea, 0x55, 0x85, 0x64, 0xe2, 0xc3, 0x3b, 0x63, 0x70, 0xcf, 0xaf, 0x11, 0x1a, 0xc1, 0xfe, 0x42,
	0x9f, 0x5e, 0xe4, 0x22, 0xc9, 0x94, 0x71, 0xd1, 0xf6, 0x0f, 0x5b, 0x28, 0x85, 0x4e, 0x4a, 0xe7,
	0x2c, 0x95, 0xb8, 0x3d, 0x6a, 0x8f, 0xfb, 0x93, 0x27, 0xee, 0xfe, 0x86, 0x33, 0x16, 0xd1, 0x60,
	0x3d, 0xd3, 0xaf, 0x1f, 0x69, 0x22, 0xa6, 0x6f, 0xf4, 0xf7, 0x7e, 0xde, 0x0c, 0x5f, 0x1c, 0x46,
	0x48, 0xd0, 0x05, 0xcd, 0xa8, 0xa7, 0x43, 0xe6, 0x1d, 0x46, 0xc1, 0x35, 0xbc, 0xb3, 0x90, 0xe6,
	0x8a, 0x09, 0xbf, 0xd6, 0x40, 0x13, 0xe8, 0x04, 0x3a, 0x0f, 0x12, 0xdb, 0x46, 0xed, 0x91, 0xfb,
	0x5f, 0x3a, 0x5d, 0x13, 0x96, 0xa9, 0xad, 0x85, 0xfc, 0x7a, 0xb2, 0x0e, 0x40, 0xe7, 0xc8, 0x00,
	0x0c, 0x60, 0x57, 0xdf, 0x60, 0x96, 0x64, 0xcc, 0xd8, 0xdb, 0xf3, 0xf7, 0x18, 0x61, 0x78, 0xc2,
	0x32, 0x25, 0xd6, 0xe7, 0xca, 0x78, 0xdc, 0xf6, 0x1b, 0xa8, 0x63, 0x13, 0x27, 0x51, 0xcc, 0xa4,
	0xba, 0x90, 0xc6, 0xeb, 0x7b, 0xc7, 0x66, 0x4f, 0x9b, 0xbe, 0xdd, 0x6c, 0x89, 0x75, 0xbd, 0x25,
	0xd6, 0xed, 0x96, 0x80, 0x6f, 0x25, 0x01, 0x3f, 0x4a, 0x02, 0xae, 0x4a, 0x02, 0x36, 0x25, 0x01,
	0x7f, 0x4a, 0x02, 0xfe, 0x96, 0xc4, 0xba, 0x2d, 0x09, 0xb8, 0xdc, 0x11, 0x6b, 0xb3, 0x23, 0xd6,
	0xf5, 0x8e, 0x58, 0x5f, 0xba, 0x8d, 0x07, 0x73, 0xc7, 0x08, 0xbd, 0xfc, 0x17, 0x00, 0x00, 0xff,
	0xff, 0x9c, 0x7d, 0xef, 0x56, 0xd8, 0x03, 0x00, 0x00,
}

func (this *Chunk) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*Chunk)
	if !ok {
		that2, ok := that.(Chunk)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if !this.From.Equal(that1.From) {
		return false
	}
	if !this.To.Equal(that1.To) {
		return false
	}
	if !this.FlushedAt.Equal(that1.FlushedAt) {
		return false
	}
	if !this.LastUpdated.Equal(that1.LastUpdated) {
		return false
	}
	if this.Closed != that1.Closed {
		return false
	}
	if this.Synced != that1.Synced {
		return false
	}
	if !bytes.Equal(this.Data, that1.Data) {
		return false
	}
	if !bytes.Equal(this.Head, that1.Head) {
		return false
	}
	return true
}
func (this *Series) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*Series)
	if !ok {
		that2, ok := that.(Series)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.UserID != that1.UserID {
		return false
	}
	if this.Fingerprint != that1.Fingerprint {
		return false
	}
	if len(this.Labels) != len(that1.Labels) {
		return false
	}
	for i := range this.Labels {
		if !this.Labels[i].Equal(that1.Labels[i]) {
			return false
		}
	}
	if len(this.Chunks) != len(that1.Chunks) {
		return false
	}
	for i := range this.Chunks {
		if !this.Chunks[i].Equal(&that1.Chunks[i]) {
			return false
		}
	}
	if !this.To.Equal(that1.To) {
		return false
	}
	if this.LastLine != that1.LastLine {
		return false
	}
	if this.EntryCt != that1.EntryCt {
		return false
	}
	if !this.HighestTs.Equal(that1.HighestTs) {
		return false
	}
	return true
}
func (this *Chunk) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 12)
	s = append(s, "&ingester.Chunk{")
	s = append(s, "From: "+fmt.Sprintf("%#v", this.From)+",\n")
	s = append(s, "To: "+fmt.Sprintf("%#v", this.To)+",\n")
	s = append(s, "FlushedAt: "+fmt.Sprintf("%#v", this.FlushedAt)+",\n")
	s = append(s, "LastUpdated: "+fmt.Sprintf("%#v", this.LastUpdated)+",\n")
	s = append(s, "Closed: "+fmt.Sprintf("%#v", this.Closed)+",\n")
	s = append(s, "Synced: "+fmt.Sprintf("%#v", this.Synced)+",\n")
	s = append(s, "Data: "+fmt.Sprintf("%#v", this.Data)+",\n")
	s = append(s, "Head: "+fmt.Sprintf("%#v", this.Head)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *Series) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 12)
	s = append(s, "&ingester.Series{")
	s = append(s, "UserID: "+fmt.Sprintf("%#v", this.UserID)+",\n")
	s = append(s, "Fingerprint: "+fmt.Sprintf("%#v", this.Fingerprint)+",\n")
	s = append(s, "Labels: "+fmt.Sprintf("%#v", this.Labels)+",\n")
	if this.Chunks != nil {
		vs := make([]*Chunk, len(this.Chunks))
		for i := range vs {
			vs[i] = &this.Chunks[i]
		}
		s = append(s, "Chunks: "+fmt.Sprintf("%#v", vs)+",\n")
	}
	s = append(s, "To: "+fmt.Sprintf("%#v", this.To)+",\n")
	s = append(s, "LastLine: "+fmt.Sprintf("%#v", this.LastLine)+",\n")
	s = append(s, "EntryCt: "+fmt.Sprintf("%#v", this.EntryCt)+",\n")
	s = append(s, "HighestTs: "+fmt.Sprintf("%#v", this.HighestTs)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func valueToGoStringCheckpoint(v interface{}, typ string) string {
	rv := reflect.ValueOf(v)
	if rv.IsNil() {
		return "nil"
	}
	pv := reflect.Indirect(rv).Interface()
	return fmt.Sprintf("func(v %v) *%v { return &v } ( %#v )", typ, typ, pv)
}
func (m *Chunk) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Chunk) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Chunk) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Head) > 0 {
		i -= len(m.Head)
		copy(dAtA[i:], m.Head)
		i = encodeVarintCheckpoint(dAtA, i, uint64(len(m.Head)))
		i--
		dAtA[i] = 0x42
	}
	if len(m.Data) > 0 {
		i -= len(m.Data)
		copy(dAtA[i:], m.Data)
		i = encodeVarintCheckpoint(dAtA, i, uint64(len(m.Data)))
		i--
		dAtA[i] = 0x3a
	}
	if m.Synced {
		i--
		if m.Synced {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x30
	}
	if m.Closed {
		i--
		if m.Closed {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x28
	}
	n1, err1 := github_com_gogo_protobuf_types.StdTimeMarshalTo(m.LastUpdated, dAtA[i-github_com_gogo_protobuf_types.SizeOfStdTime(m.LastUpdated):])
	if err1 != nil {
		return 0, err1
	}
	i -= n1
	i = encodeVarintCheckpoint(dAtA, i, uint64(n1))
	i--
	dAtA[i] = 0x22
	n2, err2 := github_com_gogo_protobuf_types.StdTimeMarshalTo(m.FlushedAt, dAtA[i-github_com_gogo_protobuf_types.SizeOfStdTime(m.FlushedAt):])
	if err2 != nil {
		return 0, err2
	}
	i -= n2
	i = encodeVarintCheckpoint(dAtA, i, uint64(n2))
	i--
	dAtA[i] = 0x1a
	n3, err3 := github_com_gogo_protobuf_types.StdTimeMarshalTo(m.To, dAtA[i-github_com_gogo_protobuf_types.SizeOfStdTime(m.To):])
	if err3 != nil {
		return 0, err3
	}
	i -= n3
	i = encodeVarintCheckpoint(dAtA, i, uint64(n3))
	i--
	dAtA[i] = 0x12
	n4, err4 := github_com_gogo_protobuf_types.StdTimeMarshalTo(m.From, dAtA[i-github_com_gogo_protobuf_types.SizeOfStdTime(m.From):])
	if err4 != nil {
		return 0, err4
	}
	i -= n4
	i = encodeVarintCheckpoint(dAtA, i, uint64(n4))
	i--
	dAtA[i] = 0xa
	return len(dAtA) - i, nil
}

func (m *Series) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Series) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Series) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	n5, err5 := github_com_gogo_protobuf_types.StdTimeMarshalTo(m.HighestTs, dAtA[i-github_com_gogo_protobuf_types.SizeOfStdTime(m.HighestTs):])
	if err5 != nil {
		return 0, err5
	}
	i -= n5
	i = encodeVarintCheckpoint(dAtA, i, uint64(n5))
	i--
	dAtA[i] = 0x42
	if m.EntryCt != 0 {
		i = encodeVarintCheckpoint(dAtA, i, uint64(m.EntryCt))
		i--
		dAtA[i] = 0x38
	}
	if len(m.LastLine) > 0 {
		i -= len(m.LastLine)
		copy(dAtA[i:], m.LastLine)
		i = encodeVarintCheckpoint(dAtA, i, uint64(len(m.LastLine)))
		i--
		dAtA[i] = 0x32
	}
	n6, err6 := github_com_gogo_protobuf_types.StdTimeMarshalTo(m.To, dAtA[i-github_com_gogo_protobuf_types.SizeOfStdTime(m.To):])
	if err6 != nil {
		return 0, err6
	}
	i -= n6
	i = encodeVarintCheckpoint(dAtA, i, uint64(n6))
	i--
	dAtA[i] = 0x2a
	if len(m.Chunks) > 0 {
		for iNdEx := len(m.Chunks) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Chunks[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintCheckpoint(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x22
		}
	}
	if len(m.Labels) > 0 {
		for iNdEx := len(m.Labels) - 1; iNdEx >= 0; iNdEx-- {
			{
				size := m.Labels[iNdEx].Size()
				i -= size
				if _, err := m.Labels[iNdEx].MarshalTo(dAtA[i:]); err != nil {
					return 0, err
				}
				i = encodeVarintCheckpoint(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x1a
		}
	}
	if m.Fingerprint != 0 {
		i = encodeVarintCheckpoint(dAtA, i, uint64(m.Fingerprint))
		i--
		dAtA[i] = 0x10
	}
	if len(m.UserID) > 0 {
		i -= len(m.UserID)
		copy(dAtA[i:], m.UserID)
		i = encodeVarintCheckpoint(dAtA, i, uint64(len(m.UserID)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func encodeVarintCheckpoint(dAtA []byte, offset int, v uint64) int {
	offset -= sovCheckpoint(v)
	base := offset
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return base
}
func (m *Chunk) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = github_com_gogo_protobuf_types.SizeOfStdTime(m.From)
	n += 1 + l + sovCheckpoint(uint64(l))
	l = github_com_gogo_protobuf_types.SizeOfStdTime(m.To)
	n += 1 + l + sovCheckpoint(uint64(l))
	l = github_com_gogo_protobuf_types.SizeOfStdTime(m.FlushedAt)
	n += 1 + l + sovCheckpoint(uint64(l))
	l = github_com_gogo_protobuf_types.SizeOfStdTime(m.LastUpdated)
	n += 1 + l + sovCheckpoint(uint64(l))
	if m.Closed {
		n += 2
	}
	if m.Synced {
		n += 2
	}
	l = len(m.Data)
	if l > 0 {
		n += 1 + l + sovCheckpoint(uint64(l))
	}
	l = len(m.Head)
	if l > 0 {
		n += 1 + l + sovCheckpoint(uint64(l))
	}
	return n
}

func (m *Series) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.UserID)
	if l > 0 {
		n += 1 + l + sovCheckpoint(uint64(l))
	}
	if m.Fingerprint != 0 {
		n += 1 + sovCheckpoint(uint64(m.Fingerprint))
	}
	if len(m.Labels) > 0 {
		for _, e := range m.Labels {
			l = e.Size()
			n += 1 + l + sovCheckpoint(uint64(l))
		}
	}
	if len(m.Chunks) > 0 {
		for _, e := range m.Chunks {
			l = e.Size()
			n += 1 + l + sovCheckpoint(uint64(l))
		}
	}
	l = github_com_gogo_protobuf_types.SizeOfStdTime(m.To)
	n += 1 + l + sovCheckpoint(uint64(l))
	l = len(m.LastLine)
	if l > 0 {
		n += 1 + l + sovCheckpoint(uint64(l))
	}
	if m.EntryCt != 0 {
		n += 1 + sovCheckpoint(uint64(m.EntryCt))
	}
	l = github_com_gogo_protobuf_types.SizeOfStdTime(m.HighestTs)
	n += 1 + l + sovCheckpoint(uint64(l))
	return n
}

func sovCheckpoint(x uint64) (n int) {
	return (math_bits.Len64(x|1) + 6) / 7
}
func sozCheckpoint(x uint64) (n int) {
	return sovCheckpoint(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (this *Chunk) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&Chunk{`,
		`From:` + strings.Replace(strings.Replace(fmt.Sprintf("%v", this.From), "Timestamp", "types.Timestamp", 1), `&`, ``, 1) + `,`,
		`To:` + strings.Replace(strings.Replace(fmt.Sprintf("%v", this.To), "Timestamp", "types.Timestamp", 1), `&`, ``, 1) + `,`,
		`FlushedAt:` + strings.Replace(strings.Replace(fmt.Sprintf("%v", this.FlushedAt), "Timestamp", "types.Timestamp", 1), `&`, ``, 1) + `,`,
		`LastUpdated:` + strings.Replace(strings.Replace(fmt.Sprintf("%v", this.LastUpdated), "Timestamp", "types.Timestamp", 1), `&`, ``, 1) + `,`,
		`Closed:` + fmt.Sprintf("%v", this.Closed) + `,`,
		`Synced:` + fmt.Sprintf("%v", this.Synced) + `,`,
		`Data:` + fmt.Sprintf("%v", this.Data) + `,`,
		`Head:` + fmt.Sprintf("%v", this.Head) + `,`,
		`}`,
	}, "")
	return s
}
func (this *Series) String() string {
	if this == nil {
		return "nil"
	}
	repeatedStringForChunks := "[]Chunk{"
	for _, f := range this.Chunks {
		repeatedStringForChunks += strings.Replace(strings.Replace(f.String(), "Chunk", "Chunk", 1), `&`, ``, 1) + ","
	}
	repeatedStringForChunks += "}"
	s := strings.Join([]string{`&Series{`,
		`UserID:` + fmt.Sprintf("%v", this.UserID) + `,`,
		`Fingerprint:` + fmt.Sprintf("%v", this.Fingerprint) + `,`,
		`Labels:` + fmt.Sprintf("%v", this.Labels) + `,`,
		`Chunks:` + repeatedStringForChunks + `,`,
		`To:` + strings.Replace(strings.Replace(fmt.Sprintf("%v", this.To), "Timestamp", "types.Timestamp", 1), `&`, ``, 1) + `,`,
		`LastLine:` + fmt.Sprintf("%v", this.LastLine) + `,`,
		`EntryCt:` + fmt.Sprintf("%v", this.EntryCt) + `,`,
		`HighestTs:` + strings.Replace(strings.Replace(fmt.Sprintf("%v", this.HighestTs), "Timestamp", "types.Timestamp", 1), `&`, ``, 1) + `,`,
		`}`,
	}, "")
	return s
}
func valueToStringCheckpoint(v interface{}) string {
	rv := reflect.ValueOf(v)
	if rv.IsNil() {
		return "nil"
	}
	pv := reflect.Indirect(rv).Interface()
	return fmt.Sprintf("*%v", pv)
}
func (m *Chunk) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCheckpoint
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Chunk: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Chunk: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field From", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCheckpoint
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCheckpoint
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthCheckpoint
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := github_com_gogo_protobuf_types.StdTimeUnmarshal(&m.From, dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field To", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCheckpoint
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCheckpoint
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthCheckpoint
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := github_com_gogo_protobuf_types.StdTimeUnmarshal(&m.To, dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field FlushedAt", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCheckpoint
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCheckpoint
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthCheckpoint
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := github_com_gogo_protobuf_types.StdTimeUnmarshal(&m.FlushedAt, dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field LastUpdated", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCheckpoint
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCheckpoint
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthCheckpoint
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := github_com_gogo_protobuf_types.StdTimeUnmarshal(&m.LastUpdated, dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Closed", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCheckpoint
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Closed = bool(v != 0)
		case 6:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Synced", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCheckpoint
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Synced = bool(v != 0)
		case 7:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Data", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCheckpoint
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthCheckpoint
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthCheckpoint
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Data = append(m.Data[:0], dAtA[iNdEx:postIndex]...)
			if m.Data == nil {
				m.Data = []byte{}
			}
			iNdEx = postIndex
		case 8:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Head", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCheckpoint
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthCheckpoint
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthCheckpoint
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Head = append(m.Head[:0], dAtA[iNdEx:postIndex]...)
			if m.Head == nil {
				m.Head = []byte{}
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipCheckpoint(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCheckpoint
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthCheckpoint
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Series) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCheckpoint
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Series: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Series: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field UserID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCheckpoint
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCheckpoint
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthCheckpoint
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.UserID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Fingerprint", wireType)
			}
			m.Fingerprint = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCheckpoint
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Fingerprint |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Labels", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCheckpoint
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCheckpoint
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthCheckpoint
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Labels = append(m.Labels, github_com_grafana_loki_pkg_logproto.LabelAdapter{})
			if err := m.Labels[len(m.Labels)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Chunks", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCheckpoint
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCheckpoint
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthCheckpoint
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Chunks = append(m.Chunks, Chunk{})
			if err := m.Chunks[len(m.Chunks)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field To", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCheckpoint
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCheckpoint
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthCheckpoint
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := github_com_gogo_protobuf_types.StdTimeUnmarshal(&m.To, dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field LastLine", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCheckpoint
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCheckpoint
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthCheckpoint
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.LastLine = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field EntryCt", wireType)
			}
			m.EntryCt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCheckpoint
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.EntryCt |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field HighestTs", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCheckpoint
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCheckpoint
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthCheckpoint
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := github_com_gogo_protobuf_types.StdTimeUnmarshal(&m.HighestTs, dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipCheckpoint(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCheckpoint
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthCheckpoint
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipCheckpoint(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowCheckpoint
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowCheckpoint
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowCheckpoint
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if length < 0 {
				return 0, ErrInvalidLengthCheckpoint
			}
			iNdEx += length
			if iNdEx < 0 {
				return 0, ErrInvalidLengthCheckpoint
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start int = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowCheckpoint
					}
					if iNdEx >= l {
						return 0, io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipCheckpoint(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
				if iNdEx < 0 {
					return 0, ErrInvalidLengthCheckpoint
				}
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthCheckpoint = fmt.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowCheckpoint   = fmt.Errorf("proto: integer overflow")
)
