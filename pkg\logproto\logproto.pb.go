// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: pkg/logproto/logproto.proto

package logproto

import (
	bytes "bytes"
	context "context"
	encoding_binary "encoding/binary"
	fmt "fmt"
	_ "github.com/gogo/protobuf/gogoproto"
	proto "github.com/gogo/protobuf/proto"
	github_com_gogo_protobuf_sortkeys "github.com/gogo/protobuf/sortkeys"
	_ "github.com/gogo/protobuf/types"
	github_com_gogo_protobuf_types "github.com/gogo/protobuf/types"
	stats "github.com/grafana/loki/pkg/logqlmodel/stats"
	github_com_prometheus_common_model "github.com/prometheus/common/model"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	io "io"
	math "math"
	math_bits "math/bits"
	reflect "reflect"
	strconv "strconv"
	strings "strings"
	time "time"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf
var _ = time.Kitchen

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion3 // please upgrade the proto package

type Direction int32

const (
	FORWARD  Direction = 0
	BACKWARD Direction = 1
)

var Direction_name = map[int32]string{
	0: "FORWARD",
	1: "BACKWARD",
}

var Direction_value = map[string]int32{
	"FORWARD":  0,
	"BACKWARD": 1,
}

func (Direction) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_c28a5f14f1f4c79a, []int{0}
}

type PushRequest struct {
	Streams []Stream `protobuf:"bytes,1,rep,name=streams,proto3,customtype=Stream" json:"streams"`
}

func (m *PushRequest) Reset()      { *m = PushRequest{} }
func (*PushRequest) ProtoMessage() {}
func (*PushRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_c28a5f14f1f4c79a, []int{0}
}
func (m *PushRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *PushRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_PushRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *PushRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PushRequest.Merge(m, src)
}
func (m *PushRequest) XXX_Size() int {
	return m.Size()
}
func (m *PushRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_PushRequest.DiscardUnknown(m)
}

var xxx_messageInfo_PushRequest proto.InternalMessageInfo

type PushResponse struct {
}

func (m *PushResponse) Reset()      { *m = PushResponse{} }
func (*PushResponse) ProtoMessage() {}
func (*PushResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_c28a5f14f1f4c79a, []int{1}
}
func (m *PushResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *PushResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_PushResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *PushResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PushResponse.Merge(m, src)
}
func (m *PushResponse) XXX_Size() int {
	return m.Size()
}
func (m *PushResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_PushResponse.DiscardUnknown(m)
}

var xxx_messageInfo_PushResponse proto.InternalMessageInfo

type QueryRequest struct {
	Selector  string    `protobuf:"bytes,1,opt,name=selector,proto3" json:"selector,omitempty"`
	Limit     uint32    `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	Start     time.Time `protobuf:"bytes,3,opt,name=start,proto3,stdtime" json:"start"`
	End       time.Time `protobuf:"bytes,4,opt,name=end,proto3,stdtime" json:"end"`
	Direction Direction `protobuf:"varint,5,opt,name=direction,proto3,enum=logproto.Direction" json:"direction,omitempty"`
	Shards    []string  `protobuf:"bytes,7,rep,name=shards,proto3" json:"shards,omitempty"`
	Deletes   []*Delete `protobuf:"bytes,8,rep,name=deletes,proto3" json:"deletes,omitempty"`
}

func (m *QueryRequest) Reset()      { *m = QueryRequest{} }
func (*QueryRequest) ProtoMessage() {}
func (*QueryRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_c28a5f14f1f4c79a, []int{2}
}
func (m *QueryRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *QueryRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_QueryRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *QueryRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryRequest.Merge(m, src)
}
func (m *QueryRequest) XXX_Size() int {
	return m.Size()
}
func (m *QueryRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryRequest.DiscardUnknown(m)
}

var xxx_messageInfo_QueryRequest proto.InternalMessageInfo

func (m *QueryRequest) GetSelector() string {
	if m != nil {
		return m.Selector
	}
	return ""
}

func (m *QueryRequest) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *QueryRequest) GetStart() time.Time {
	if m != nil {
		return m.Start
	}
	return time.Time{}
}

func (m *QueryRequest) GetEnd() time.Time {
	if m != nil {
		return m.End
	}
	return time.Time{}
}

func (m *QueryRequest) GetDirection() Direction {
	if m != nil {
		return m.Direction
	}
	return FORWARD
}

func (m *QueryRequest) GetShards() []string {
	if m != nil {
		return m.Shards
	}
	return nil
}

func (m *QueryRequest) GetDeletes() []*Delete {
	if m != nil {
		return m.Deletes
	}
	return nil
}

type SampleQueryRequest struct {
	Selector string    `protobuf:"bytes,1,opt,name=selector,proto3" json:"selector,omitempty"`
	Start    time.Time `protobuf:"bytes,2,opt,name=start,proto3,stdtime" json:"start"`
	End      time.Time `protobuf:"bytes,3,opt,name=end,proto3,stdtime" json:"end"`
	Shards   []string  `protobuf:"bytes,4,rep,name=shards,proto3" json:"shards,omitempty"`
	Deletes  []*Delete `protobuf:"bytes,5,rep,name=deletes,proto3" json:"deletes,omitempty"`
}

func (m *SampleQueryRequest) Reset()      { *m = SampleQueryRequest{} }
func (*SampleQueryRequest) ProtoMessage() {}
func (*SampleQueryRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_c28a5f14f1f4c79a, []int{3}
}
func (m *SampleQueryRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *SampleQueryRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_SampleQueryRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *SampleQueryRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SampleQueryRequest.Merge(m, src)
}
func (m *SampleQueryRequest) XXX_Size() int {
	return m.Size()
}
func (m *SampleQueryRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SampleQueryRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SampleQueryRequest proto.InternalMessageInfo

func (m *SampleQueryRequest) GetSelector() string {
	if m != nil {
		return m.Selector
	}
	return ""
}

func (m *SampleQueryRequest) GetStart() time.Time {
	if m != nil {
		return m.Start
	}
	return time.Time{}
}

func (m *SampleQueryRequest) GetEnd() time.Time {
	if m != nil {
		return m.End
	}
	return time.Time{}
}

func (m *SampleQueryRequest) GetShards() []string {
	if m != nil {
		return m.Shards
	}
	return nil
}

func (m *SampleQueryRequest) GetDeletes() []*Delete {
	if m != nil {
		return m.Deletes
	}
	return nil
}

type Delete struct {
	Selector string `protobuf:"bytes,1,opt,name=selector,proto3" json:"selector,omitempty"`
	Start    int64  `protobuf:"varint,2,opt,name=start,proto3" json:"start,omitempty"`
	End      int64  `protobuf:"varint,3,opt,name=end,proto3" json:"end,omitempty"`
}

func (m *Delete) Reset()      { *m = Delete{} }
func (*Delete) ProtoMessage() {}
func (*Delete) Descriptor() ([]byte, []int) {
	return fileDescriptor_c28a5f14f1f4c79a, []int{4}
}
func (m *Delete) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Delete) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Delete.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *Delete) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Delete.Merge(m, src)
}
func (m *Delete) XXX_Size() int {
	return m.Size()
}
func (m *Delete) XXX_DiscardUnknown() {
	xxx_messageInfo_Delete.DiscardUnknown(m)
}

var xxx_messageInfo_Delete proto.InternalMessageInfo

func (m *Delete) GetSelector() string {
	if m != nil {
		return m.Selector
	}
	return ""
}

func (m *Delete) GetStart() int64 {
	if m != nil {
		return m.Start
	}
	return 0
}

func (m *Delete) GetEnd() int64 {
	if m != nil {
		return m.End
	}
	return 0
}

type QueryResponse struct {
	Streams []Stream       `protobuf:"bytes,1,rep,name=streams,proto3,customtype=Stream" json:"streams,omitempty"`
	Stats   stats.Ingester `protobuf:"bytes,2,opt,name=stats,proto3" json:"stats"`
}

func (m *QueryResponse) Reset()      { *m = QueryResponse{} }
func (*QueryResponse) ProtoMessage() {}
func (*QueryResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_c28a5f14f1f4c79a, []int{5}
}
func (m *QueryResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *QueryResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_QueryResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *QueryResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryResponse.Merge(m, src)
}
func (m *QueryResponse) XXX_Size() int {
	return m.Size()
}
func (m *QueryResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryResponse.DiscardUnknown(m)
}

var xxx_messageInfo_QueryResponse proto.InternalMessageInfo

func (m *QueryResponse) GetStats() stats.Ingester {
	if m != nil {
		return m.Stats
	}
	return stats.Ingester{}
}

type SampleQueryResponse struct {
	Series []Series       `protobuf:"bytes,1,rep,name=series,proto3,customtype=Series" json:"series,omitempty"`
	Stats  stats.Ingester `protobuf:"bytes,2,opt,name=stats,proto3" json:"stats"`
}

func (m *SampleQueryResponse) Reset()      { *m = SampleQueryResponse{} }
func (*SampleQueryResponse) ProtoMessage() {}
func (*SampleQueryResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_c28a5f14f1f4c79a, []int{6}
}
func (m *SampleQueryResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *SampleQueryResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_SampleQueryResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *SampleQueryResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SampleQueryResponse.Merge(m, src)
}
func (m *SampleQueryResponse) XXX_Size() int {
	return m.Size()
}
func (m *SampleQueryResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SampleQueryResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SampleQueryResponse proto.InternalMessageInfo

func (m *SampleQueryResponse) GetStats() stats.Ingester {
	if m != nil {
		return m.Stats
	}
	return stats.Ingester{}
}

type LabelRequest struct {
	Name   string     `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Values bool       `protobuf:"varint,2,opt,name=values,proto3" json:"values,omitempty"`
	Start  *time.Time `protobuf:"bytes,3,opt,name=start,proto3,stdtime" json:"start,omitempty"`
	End    *time.Time `protobuf:"bytes,4,opt,name=end,proto3,stdtime" json:"end,omitempty"`
}

func (m *LabelRequest) Reset()      { *m = LabelRequest{} }
func (*LabelRequest) ProtoMessage() {}
func (*LabelRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_c28a5f14f1f4c79a, []int{7}
}
func (m *LabelRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *LabelRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_LabelRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *LabelRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LabelRequest.Merge(m, src)
}
func (m *LabelRequest) XXX_Size() int {
	return m.Size()
}
func (m *LabelRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_LabelRequest.DiscardUnknown(m)
}

var xxx_messageInfo_LabelRequest proto.InternalMessageInfo

func (m *LabelRequest) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *LabelRequest) GetValues() bool {
	if m != nil {
		return m.Values
	}
	return false
}

func (m *LabelRequest) GetStart() *time.Time {
	if m != nil {
		return m.Start
	}
	return nil
}

func (m *LabelRequest) GetEnd() *time.Time {
	if m != nil {
		return m.End
	}
	return nil
}

type LabelResponse struct {
	Values []string `protobuf:"bytes,1,rep,name=values,proto3" json:"values,omitempty"`
}

func (m *LabelResponse) Reset()      { *m = LabelResponse{} }
func (*LabelResponse) ProtoMessage() {}
func (*LabelResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_c28a5f14f1f4c79a, []int{8}
}
func (m *LabelResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *LabelResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_LabelResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *LabelResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LabelResponse.Merge(m, src)
}
func (m *LabelResponse) XXX_Size() int {
	return m.Size()
}
func (m *LabelResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_LabelResponse.DiscardUnknown(m)
}

var xxx_messageInfo_LabelResponse proto.InternalMessageInfo

func (m *LabelResponse) GetValues() []string {
	if m != nil {
		return m.Values
	}
	return nil
}

type StreamAdapter struct {
	Labels  string         `protobuf:"bytes,1,opt,name=labels,proto3" json:"labels"`
	Entries []EntryAdapter `protobuf:"bytes,2,rep,name=entries,proto3" json:"entries"`
	// hash contains the original hash of the stream.
	Hash uint64 `protobuf:"varint,3,opt,name=hash,proto3" json:"-"`
}

func (m *StreamAdapter) Reset()      { *m = StreamAdapter{} }
func (*StreamAdapter) ProtoMessage() {}
func (*StreamAdapter) Descriptor() ([]byte, []int) {
	return fileDescriptor_c28a5f14f1f4c79a, []int{9}
}
func (m *StreamAdapter) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *StreamAdapter) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_StreamAdapter.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *StreamAdapter) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StreamAdapter.Merge(m, src)
}
func (m *StreamAdapter) XXX_Size() int {
	return m.Size()
}
func (m *StreamAdapter) XXX_DiscardUnknown() {
	xxx_messageInfo_StreamAdapter.DiscardUnknown(m)
}

var xxx_messageInfo_StreamAdapter proto.InternalMessageInfo

func (m *StreamAdapter) GetLabels() string {
	if m != nil {
		return m.Labels
	}
	return ""
}

func (m *StreamAdapter) GetEntries() []EntryAdapter {
	if m != nil {
		return m.Entries
	}
	return nil
}

func (m *StreamAdapter) GetHash() uint64 {
	if m != nil {
		return m.Hash
	}
	return 0
}

type EntryAdapter struct {
	Timestamp time.Time `protobuf:"bytes,1,opt,name=timestamp,proto3,stdtime" json:"ts"`
	Line      string    `protobuf:"bytes,2,opt,name=line,proto3" json:"line"`
}

func (m *EntryAdapter) Reset()      { *m = EntryAdapter{} }
func (*EntryAdapter) ProtoMessage() {}
func (*EntryAdapter) Descriptor() ([]byte, []int) {
	return fileDescriptor_c28a5f14f1f4c79a, []int{10}
}
func (m *EntryAdapter) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *EntryAdapter) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_EntryAdapter.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *EntryAdapter) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EntryAdapter.Merge(m, src)
}
func (m *EntryAdapter) XXX_Size() int {
	return m.Size()
}
func (m *EntryAdapter) XXX_DiscardUnknown() {
	xxx_messageInfo_EntryAdapter.DiscardUnknown(m)
}

var xxx_messageInfo_EntryAdapter proto.InternalMessageInfo

func (m *EntryAdapter) GetTimestamp() time.Time {
	if m != nil {
		return m.Timestamp
	}
	return time.Time{}
}

func (m *EntryAdapter) GetLine() string {
	if m != nil {
		return m.Line
	}
	return ""
}

type Sample struct {
	Timestamp int64   `protobuf:"varint,1,opt,name=timestamp,proto3" json:"ts"`
	Value     float64 `protobuf:"fixed64,2,opt,name=value,proto3" json:"value"`
	Hash      uint64  `protobuf:"varint,3,opt,name=hash,proto3" json:"hash"`
}

func (m *Sample) Reset()      { *m = Sample{} }
func (*Sample) ProtoMessage() {}
func (*Sample) Descriptor() ([]byte, []int) {
	return fileDescriptor_c28a5f14f1f4c79a, []int{11}
}
func (m *Sample) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Sample) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Sample.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *Sample) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Sample.Merge(m, src)
}
func (m *Sample) XXX_Size() int {
	return m.Size()
}
func (m *Sample) XXX_DiscardUnknown() {
	xxx_messageInfo_Sample.DiscardUnknown(m)
}

var xxx_messageInfo_Sample proto.InternalMessageInfo

func (m *Sample) GetTimestamp() int64 {
	if m != nil {
		return m.Timestamp
	}
	return 0
}

func (m *Sample) GetValue() float64 {
	if m != nil {
		return m.Value
	}
	return 0
}

func (m *Sample) GetHash() uint64 {
	if m != nil {
		return m.Hash
	}
	return 0
}

// LegacySample exists for backwards compatibility reasons and is deprecated. Do not use.
type LegacySample struct {
	Value       float64 `protobuf:"fixed64,1,opt,name=value,proto3" json:"value,omitempty"`
	TimestampMs int64   `protobuf:"varint,2,opt,name=timestamp_ms,json=timestampMs,proto3" json:"timestamp_ms,omitempty"`
}

func (m *LegacySample) Reset()      { *m = LegacySample{} }
func (*LegacySample) ProtoMessage() {}
func (*LegacySample) Descriptor() ([]byte, []int) {
	return fileDescriptor_c28a5f14f1f4c79a, []int{12}
}
func (m *LegacySample) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *LegacySample) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_LegacySample.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *LegacySample) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LegacySample.Merge(m, src)
}
func (m *LegacySample) XXX_Size() int {
	return m.Size()
}
func (m *LegacySample) XXX_DiscardUnknown() {
	xxx_messageInfo_LegacySample.DiscardUnknown(m)
}

var xxx_messageInfo_LegacySample proto.InternalMessageInfo

func (m *LegacySample) GetValue() float64 {
	if m != nil {
		return m.Value
	}
	return 0
}

func (m *LegacySample) GetTimestampMs() int64 {
	if m != nil {
		return m.TimestampMs
	}
	return 0
}

type Series struct {
	Labels     string   `protobuf:"bytes,1,opt,name=labels,proto3" json:"labels"`
	Samples    []Sample `protobuf:"bytes,2,rep,name=samples,proto3" json:"samples"`
	StreamHash uint64   `protobuf:"varint,3,opt,name=streamHash,proto3" json:"streamHash"`
}

func (m *Series) Reset()      { *m = Series{} }
func (*Series) ProtoMessage() {}
func (*Series) Descriptor() ([]byte, []int) {
	return fileDescriptor_c28a5f14f1f4c79a, []int{13}
}
func (m *Series) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Series) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Series.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *Series) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Series.Merge(m, src)
}
func (m *Series) XXX_Size() int {
	return m.Size()
}
func (m *Series) XXX_DiscardUnknown() {
	xxx_messageInfo_Series.DiscardUnknown(m)
}

var xxx_messageInfo_Series proto.InternalMessageInfo

func (m *Series) GetLabels() string {
	if m != nil {
		return m.Labels
	}
	return ""
}

func (m *Series) GetSamples() []Sample {
	if m != nil {
		return m.Samples
	}
	return nil
}

func (m *Series) GetStreamHash() uint64 {
	if m != nil {
		return m.StreamHash
	}
	return 0
}

type TailRequest struct {
	Query    string    `protobuf:"bytes,1,opt,name=query,proto3" json:"query,omitempty"`
	DelayFor uint32    `protobuf:"varint,3,opt,name=delayFor,proto3" json:"delayFor,omitempty"`
	Limit    uint32    `protobuf:"varint,4,opt,name=limit,proto3" json:"limit,omitempty"`
	Start    time.Time `protobuf:"bytes,5,opt,name=start,proto3,stdtime" json:"start"`
}

func (m *TailRequest) Reset()      { *m = TailRequest{} }
func (*TailRequest) ProtoMessage() {}
func (*TailRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_c28a5f14f1f4c79a, []int{14}
}
func (m *TailRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *TailRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_TailRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *TailRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TailRequest.Merge(m, src)
}
func (m *TailRequest) XXX_Size() int {
	return m.Size()
}
func (m *TailRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_TailRequest.DiscardUnknown(m)
}

var xxx_messageInfo_TailRequest proto.InternalMessageInfo

func (m *TailRequest) GetQuery() string {
	if m != nil {
		return m.Query
	}
	return ""
}

func (m *TailRequest) GetDelayFor() uint32 {
	if m != nil {
		return m.DelayFor
	}
	return 0
}

func (m *TailRequest) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *TailRequest) GetStart() time.Time {
	if m != nil {
		return m.Start
	}
	return time.Time{}
}

type TailResponse struct {
	Stream         *Stream          `protobuf:"bytes,1,opt,name=stream,proto3,customtype=Stream" json:"stream,omitempty"`
	DroppedStreams []*DroppedStream `protobuf:"bytes,2,rep,name=droppedStreams,proto3" json:"droppedStreams,omitempty"`
}

func (m *TailResponse) Reset()      { *m = TailResponse{} }
func (*TailResponse) ProtoMessage() {}
func (*TailResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_c28a5f14f1f4c79a, []int{15}
}
func (m *TailResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *TailResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_TailResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *TailResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TailResponse.Merge(m, src)
}
func (m *TailResponse) XXX_Size() int {
	return m.Size()
}
func (m *TailResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_TailResponse.DiscardUnknown(m)
}

var xxx_messageInfo_TailResponse proto.InternalMessageInfo

func (m *TailResponse) GetDroppedStreams() []*DroppedStream {
	if m != nil {
		return m.DroppedStreams
	}
	return nil
}

type SeriesRequest struct {
	Start  time.Time `protobuf:"bytes,1,opt,name=start,proto3,stdtime" json:"start"`
	End    time.Time `protobuf:"bytes,2,opt,name=end,proto3,stdtime" json:"end"`
	Groups []string  `protobuf:"bytes,3,rep,name=groups,proto3" json:"groups,omitempty"`
	Shards []string  `protobuf:"bytes,4,rep,name=shards,proto3" json:"shards,omitempty"`
}

func (m *SeriesRequest) Reset()      { *m = SeriesRequest{} }
func (*SeriesRequest) ProtoMessage() {}
func (*SeriesRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_c28a5f14f1f4c79a, []int{16}
}
func (m *SeriesRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *SeriesRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_SeriesRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *SeriesRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SeriesRequest.Merge(m, src)
}
func (m *SeriesRequest) XXX_Size() int {
	return m.Size()
}
func (m *SeriesRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SeriesRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SeriesRequest proto.InternalMessageInfo

func (m *SeriesRequest) GetStart() time.Time {
	if m != nil {
		return m.Start
	}
	return time.Time{}
}

func (m *SeriesRequest) GetEnd() time.Time {
	if m != nil {
		return m.End
	}
	return time.Time{}
}

func (m *SeriesRequest) GetGroups() []string {
	if m != nil {
		return m.Groups
	}
	return nil
}

func (m *SeriesRequest) GetShards() []string {
	if m != nil {
		return m.Shards
	}
	return nil
}

type SeriesResponse struct {
	Series []SeriesIdentifier `protobuf:"bytes,1,rep,name=series,proto3" json:"series"`
}

func (m *SeriesResponse) Reset()      { *m = SeriesResponse{} }
func (*SeriesResponse) ProtoMessage() {}
func (*SeriesResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_c28a5f14f1f4c79a, []int{17}
}
func (m *SeriesResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *SeriesResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_SeriesResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *SeriesResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SeriesResponse.Merge(m, src)
}
func (m *SeriesResponse) XXX_Size() int {
	return m.Size()
}
func (m *SeriesResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SeriesResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SeriesResponse proto.InternalMessageInfo

func (m *SeriesResponse) GetSeries() []SeriesIdentifier {
	if m != nil {
		return m.Series
	}
	return nil
}

type SeriesIdentifier struct {
	Labels map[string]string `protobuf:"bytes,1,rep,name=labels,proto3" json:"labels,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (m *SeriesIdentifier) Reset()      { *m = SeriesIdentifier{} }
func (*SeriesIdentifier) ProtoMessage() {}
func (*SeriesIdentifier) Descriptor() ([]byte, []int) {
	return fileDescriptor_c28a5f14f1f4c79a, []int{18}
}
func (m *SeriesIdentifier) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *SeriesIdentifier) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_SeriesIdentifier.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *SeriesIdentifier) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SeriesIdentifier.Merge(m, src)
}
func (m *SeriesIdentifier) XXX_Size() int {
	return m.Size()
}
func (m *SeriesIdentifier) XXX_DiscardUnknown() {
	xxx_messageInfo_SeriesIdentifier.DiscardUnknown(m)
}

var xxx_messageInfo_SeriesIdentifier proto.InternalMessageInfo

func (m *SeriesIdentifier) GetLabels() map[string]string {
	if m != nil {
		return m.Labels
	}
	return nil
}

type DroppedStream struct {
	From   time.Time `protobuf:"bytes,1,opt,name=from,proto3,stdtime" json:"from"`
	To     time.Time `protobuf:"bytes,2,opt,name=to,proto3,stdtime" json:"to"`
	Labels string    `protobuf:"bytes,3,opt,name=labels,proto3" json:"labels,omitempty"`
}

func (m *DroppedStream) Reset()      { *m = DroppedStream{} }
func (*DroppedStream) ProtoMessage() {}
func (*DroppedStream) Descriptor() ([]byte, []int) {
	return fileDescriptor_c28a5f14f1f4c79a, []int{19}
}
func (m *DroppedStream) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DroppedStream) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DroppedStream.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DroppedStream) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DroppedStream.Merge(m, src)
}
func (m *DroppedStream) XXX_Size() int {
	return m.Size()
}
func (m *DroppedStream) XXX_DiscardUnknown() {
	xxx_messageInfo_DroppedStream.DiscardUnknown(m)
}

var xxx_messageInfo_DroppedStream proto.InternalMessageInfo

func (m *DroppedStream) GetFrom() time.Time {
	if m != nil {
		return m.From
	}
	return time.Time{}
}

func (m *DroppedStream) GetTo() time.Time {
	if m != nil {
		return m.To
	}
	return time.Time{}
}

func (m *DroppedStream) GetLabels() string {
	if m != nil {
		return m.Labels
	}
	return ""
}

type TimeSeriesChunk struct {
	FromIngesterId string       `protobuf:"bytes,1,opt,name=from_ingester_id,json=fromIngesterId,proto3" json:"from_ingester_id,omitempty"`
	UserId         string       `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Labels         []*LabelPair `protobuf:"bytes,3,rep,name=labels,proto3" json:"labels,omitempty"`
	Chunks         []*Chunk     `protobuf:"bytes,4,rep,name=chunks,proto3" json:"chunks,omitempty"`
}

func (m *TimeSeriesChunk) Reset()      { *m = TimeSeriesChunk{} }
func (*TimeSeriesChunk) ProtoMessage() {}
func (*TimeSeriesChunk) Descriptor() ([]byte, []int) {
	return fileDescriptor_c28a5f14f1f4c79a, []int{20}
}
func (m *TimeSeriesChunk) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *TimeSeriesChunk) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_TimeSeriesChunk.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *TimeSeriesChunk) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TimeSeriesChunk.Merge(m, src)
}
func (m *TimeSeriesChunk) XXX_Size() int {
	return m.Size()
}
func (m *TimeSeriesChunk) XXX_DiscardUnknown() {
	xxx_messageInfo_TimeSeriesChunk.DiscardUnknown(m)
}

var xxx_messageInfo_TimeSeriesChunk proto.InternalMessageInfo

func (m *TimeSeriesChunk) GetFromIngesterId() string {
	if m != nil {
		return m.FromIngesterId
	}
	return ""
}

func (m *TimeSeriesChunk) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *TimeSeriesChunk) GetLabels() []*LabelPair {
	if m != nil {
		return m.Labels
	}
	return nil
}

func (m *TimeSeriesChunk) GetChunks() []*Chunk {
	if m != nil {
		return m.Chunks
	}
	return nil
}

type LabelPair struct {
	Name  string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Value string `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
}

func (m *LabelPair) Reset()      { *m = LabelPair{} }
func (*LabelPair) ProtoMessage() {}
func (*LabelPair) Descriptor() ([]byte, []int) {
	return fileDescriptor_c28a5f14f1f4c79a, []int{21}
}
func (m *LabelPair) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *LabelPair) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_LabelPair.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *LabelPair) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LabelPair.Merge(m, src)
}
func (m *LabelPair) XXX_Size() int {
	return m.Size()
}
func (m *LabelPair) XXX_DiscardUnknown() {
	xxx_messageInfo_LabelPair.DiscardUnknown(m)
}

var xxx_messageInfo_LabelPair proto.InternalMessageInfo

func (m *LabelPair) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *LabelPair) GetValue() string {
	if m != nil {
		return m.Value
	}
	return ""
}

// LegacyLabelPair exists for backwards compatibility reasons and is deprecated. Do not use.
type LegacyLabelPair struct {
	Name  []byte `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Value []byte `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
}

func (m *LegacyLabelPair) Reset()      { *m = LegacyLabelPair{} }
func (*LegacyLabelPair) ProtoMessage() {}
func (*LegacyLabelPair) Descriptor() ([]byte, []int) {
	return fileDescriptor_c28a5f14f1f4c79a, []int{22}
}
func (m *LegacyLabelPair) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *LegacyLabelPair) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_LegacyLabelPair.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *LegacyLabelPair) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LegacyLabelPair.Merge(m, src)
}
func (m *LegacyLabelPair) XXX_Size() int {
	return m.Size()
}
func (m *LegacyLabelPair) XXX_DiscardUnknown() {
	xxx_messageInfo_LegacyLabelPair.DiscardUnknown(m)
}

var xxx_messageInfo_LegacyLabelPair proto.InternalMessageInfo

func (m *LegacyLabelPair) GetName() []byte {
	if m != nil {
		return m.Name
	}
	return nil
}

func (m *LegacyLabelPair) GetValue() []byte {
	if m != nil {
		return m.Value
	}
	return nil
}

type Chunk struct {
	Data []byte `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
}

func (m *Chunk) Reset()      { *m = Chunk{} }
func (*Chunk) ProtoMessage() {}
func (*Chunk) Descriptor() ([]byte, []int) {
	return fileDescriptor_c28a5f14f1f4c79a, []int{23}
}
func (m *Chunk) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Chunk) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Chunk.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *Chunk) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Chunk.Merge(m, src)
}
func (m *Chunk) XXX_Size() int {
	return m.Size()
}
func (m *Chunk) XXX_DiscardUnknown() {
	xxx_messageInfo_Chunk.DiscardUnknown(m)
}

var xxx_messageInfo_Chunk proto.InternalMessageInfo

func (m *Chunk) GetData() []byte {
	if m != nil {
		return m.Data
	}
	return nil
}

type TransferChunksResponse struct {
}

func (m *TransferChunksResponse) Reset()      { *m = TransferChunksResponse{} }
func (*TransferChunksResponse) ProtoMessage() {}
func (*TransferChunksResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_c28a5f14f1f4c79a, []int{24}
}
func (m *TransferChunksResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *TransferChunksResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_TransferChunksResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *TransferChunksResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TransferChunksResponse.Merge(m, src)
}
func (m *TransferChunksResponse) XXX_Size() int {
	return m.Size()
}
func (m *TransferChunksResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_TransferChunksResponse.DiscardUnknown(m)
}

var xxx_messageInfo_TransferChunksResponse proto.InternalMessageInfo

type TailersCountRequest struct {
}

func (m *TailersCountRequest) Reset()      { *m = TailersCountRequest{} }
func (*TailersCountRequest) ProtoMessage() {}
func (*TailersCountRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_c28a5f14f1f4c79a, []int{25}
}
func (m *TailersCountRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *TailersCountRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_TailersCountRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *TailersCountRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TailersCountRequest.Merge(m, src)
}
func (m *TailersCountRequest) XXX_Size() int {
	return m.Size()
}
func (m *TailersCountRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_TailersCountRequest.DiscardUnknown(m)
}

var xxx_messageInfo_TailersCountRequest proto.InternalMessageInfo

type TailersCountResponse struct {
	Count uint32 `protobuf:"varint,1,opt,name=count,proto3" json:"count,omitempty"`
}

func (m *TailersCountResponse) Reset()      { *m = TailersCountResponse{} }
func (*TailersCountResponse) ProtoMessage() {}
func (*TailersCountResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_c28a5f14f1f4c79a, []int{26}
}
func (m *TailersCountResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *TailersCountResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_TailersCountResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *TailersCountResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TailersCountResponse.Merge(m, src)
}
func (m *TailersCountResponse) XXX_Size() int {
	return m.Size()
}
func (m *TailersCountResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_TailersCountResponse.DiscardUnknown(m)
}

var xxx_messageInfo_TailersCountResponse proto.InternalMessageInfo

func (m *TailersCountResponse) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type GetChunkIDsRequest struct {
	Matchers string    `protobuf:"bytes,1,opt,name=matchers,proto3" json:"matchers,omitempty"`
	Start    time.Time `protobuf:"bytes,2,opt,name=start,proto3,stdtime" json:"start"`
	End      time.Time `protobuf:"bytes,3,opt,name=end,proto3,stdtime" json:"end"`
}

func (m *GetChunkIDsRequest) Reset()      { *m = GetChunkIDsRequest{} }
func (*GetChunkIDsRequest) ProtoMessage() {}
func (*GetChunkIDsRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_c28a5f14f1f4c79a, []int{27}
}
func (m *GetChunkIDsRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetChunkIDsRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetChunkIDsRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *GetChunkIDsRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChunkIDsRequest.Merge(m, src)
}
func (m *GetChunkIDsRequest) XXX_Size() int {
	return m.Size()
}
func (m *GetChunkIDsRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChunkIDsRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetChunkIDsRequest proto.InternalMessageInfo

func (m *GetChunkIDsRequest) GetMatchers() string {
	if m != nil {
		return m.Matchers
	}
	return ""
}

func (m *GetChunkIDsRequest) GetStart() time.Time {
	if m != nil {
		return m.Start
	}
	return time.Time{}
}

func (m *GetChunkIDsRequest) GetEnd() time.Time {
	if m != nil {
		return m.End
	}
	return time.Time{}
}

type GetChunkIDsResponse struct {
	ChunkIDs []string `protobuf:"bytes,1,rep,name=chunkIDs,proto3" json:"chunkIDs,omitempty"`
}

func (m *GetChunkIDsResponse) Reset()      { *m = GetChunkIDsResponse{} }
func (*GetChunkIDsResponse) ProtoMessage() {}
func (*GetChunkIDsResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_c28a5f14f1f4c79a, []int{28}
}
func (m *GetChunkIDsResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetChunkIDsResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetChunkIDsResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *GetChunkIDsResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChunkIDsResponse.Merge(m, src)
}
func (m *GetChunkIDsResponse) XXX_Size() int {
	return m.Size()
}
func (m *GetChunkIDsResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChunkIDsResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetChunkIDsResponse proto.InternalMessageInfo

func (m *GetChunkIDsResponse) GetChunkIDs() []string {
	if m != nil {
		return m.ChunkIDs
	}
	return nil
}

// ChunkRef contains the metadata to reference a Chunk.
// It is embedded by the Chunk type itself and used to generate the Chunk
// checksum. So it is imported to take care of the JSON representation of the
// resulting Go struct.
type ChunkRef struct {
	Fingerprint uint64                                  `protobuf:"varint,1,opt,name=fingerprint,proto3" json:"fingerprint"`
	UserID      string                                  `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"userID"`
	From        github_com_prometheus_common_model.Time `protobuf:"varint,3,opt,name=from,proto3,customtype=github.com/prometheus/common/model.Time" json:"from"`
	Through     github_com_prometheus_common_model.Time `protobuf:"varint,4,opt,name=through,proto3,customtype=github.com/prometheus/common/model.Time" json:"through"`
	// The checksum is not written to the external storage. We use crc32,
	// Castagnoli table. See http://www.evanjones.ca/crc32c.html.
	Checksum uint32 `protobuf:"varint,5,opt,name=checksum,proto3" json:"-"`
}

func (m *ChunkRef) Reset()      { *m = ChunkRef{} }
func (*ChunkRef) ProtoMessage() {}
func (*ChunkRef) Descriptor() ([]byte, []int) {
	return fileDescriptor_c28a5f14f1f4c79a, []int{29}
}
func (m *ChunkRef) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ChunkRef) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ChunkRef.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ChunkRef) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChunkRef.Merge(m, src)
}
func (m *ChunkRef) XXX_Size() int {
	return m.Size()
}
func (m *ChunkRef) XXX_DiscardUnknown() {
	xxx_messageInfo_ChunkRef.DiscardUnknown(m)
}

var xxx_messageInfo_ChunkRef proto.InternalMessageInfo

func (m *ChunkRef) GetFingerprint() uint64 {
	if m != nil {
		return m.Fingerprint
	}
	return 0
}

func (m *ChunkRef) GetUserID() string {
	if m != nil {
		return m.UserID
	}
	return ""
}

func (m *ChunkRef) GetChecksum() uint32 {
	if m != nil {
		return m.Checksum
	}
	return 0
}

func init() {
	proto.RegisterEnum("logproto.Direction", Direction_name, Direction_value)
	proto.RegisterType((*PushRequest)(nil), "logproto.PushRequest")
	proto.RegisterType((*PushResponse)(nil), "logproto.PushResponse")
	proto.RegisterType((*QueryRequest)(nil), "logproto.QueryRequest")
	proto.RegisterType((*SampleQueryRequest)(nil), "logproto.SampleQueryRequest")
	proto.RegisterType((*Delete)(nil), "logproto.Delete")
	proto.RegisterType((*QueryResponse)(nil), "logproto.QueryResponse")
	proto.RegisterType((*SampleQueryResponse)(nil), "logproto.SampleQueryResponse")
	proto.RegisterType((*LabelRequest)(nil), "logproto.LabelRequest")
	proto.RegisterType((*LabelResponse)(nil), "logproto.LabelResponse")
	proto.RegisterType((*StreamAdapter)(nil), "logproto.StreamAdapter")
	proto.RegisterType((*EntryAdapter)(nil), "logproto.EntryAdapter")
	proto.RegisterType((*Sample)(nil), "logproto.Sample")
	proto.RegisterType((*LegacySample)(nil), "logproto.LegacySample")
	proto.RegisterType((*Series)(nil), "logproto.Series")
	proto.RegisterType((*TailRequest)(nil), "logproto.TailRequest")
	proto.RegisterType((*TailResponse)(nil), "logproto.TailResponse")
	proto.RegisterType((*SeriesRequest)(nil), "logproto.SeriesRequest")
	proto.RegisterType((*SeriesResponse)(nil), "logproto.SeriesResponse")
	proto.RegisterType((*SeriesIdentifier)(nil), "logproto.SeriesIdentifier")
	proto.RegisterMapType((map[string]string)(nil), "logproto.SeriesIdentifier.LabelsEntry")
	proto.RegisterType((*DroppedStream)(nil), "logproto.DroppedStream")
	proto.RegisterType((*TimeSeriesChunk)(nil), "logproto.TimeSeriesChunk")
	proto.RegisterType((*LabelPair)(nil), "logproto.LabelPair")
	proto.RegisterType((*LegacyLabelPair)(nil), "logproto.LegacyLabelPair")
	proto.RegisterType((*Chunk)(nil), "logproto.Chunk")
	proto.RegisterType((*TransferChunksResponse)(nil), "logproto.TransferChunksResponse")
	proto.RegisterType((*TailersCountRequest)(nil), "logproto.TailersCountRequest")
	proto.RegisterType((*TailersCountResponse)(nil), "logproto.TailersCountResponse")
	proto.RegisterType((*GetChunkIDsRequest)(nil), "logproto.GetChunkIDsRequest")
	proto.RegisterType((*GetChunkIDsResponse)(nil), "logproto.GetChunkIDsResponse")
	proto.RegisterType((*ChunkRef)(nil), "logproto.ChunkRef")
}

func init() { proto.RegisterFile("pkg/logproto/logproto.proto", fileDescriptor_c28a5f14f1f4c79a) }

var fileDescriptor_c28a5f14f1f4c79a = []byte{
	// 1653 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xc4, 0x58, 0x49, 0x6f, 0x1b, 0xc9,
	0x15, 0x66, 0x71, 0x69, 0x92, 0x8f, 0x8b, 0x88, 0x92, 0x2c, 0x71, 0x38, 0x33, 0x6c, 0x4e, 0x63,
	0x30, 0x26, 0xbc, 0x90, 0xb1, 0xb2, 0xd8, 0x96, 0xb3, 0x40, 0xb4, 0x62, 0x5b, 0xb6, 0x12, 0xdb,
	0x2d, 0x05, 0x06, 0x0c, 0x04, 0x46, 0x8b, 0x2c, 0x91, 0x0d, 0xb1, 0xd9, 0x74, 0x57, 0xd3, 0x80,
	0x80, 0x00, 0xc9, 0x0f, 0x48, 0x00, 0xe7, 0x14, 0xe4, 0x9e, 0x43, 0x90, 0x43, 0x0e, 0xf9, 0x13,
	0x71, 0x6e, 0x3e, 0x1a, 0x3e, 0x30, 0x31, 0x7d, 0x09, 0x88, 0x1c, 0xfc, 0x0b, 0x82, 0xa0, 0xb6,
	0x66, 0x91, 0x96, 0x62, 0xd3, 0x97, 0xb9, 0x88, 0xf5, 0x5e, 0xd5, 0xdb, 0xbe, 0x7a, 0x4b, 0xb5,
	0xe0, 0xf3, 0xe1, 0x71, 0xb7, 0xd9, 0xf7, 0xbb, 0xc3, 0xc0, 0x0f, 0xfd, 0x68, 0xd1, 0xe0, 0x7f,
	0x71, 0x46, 0xd1, 0x95, 0xcb, 0x5d, 0x37, 0xec, 0x8d, 0x0e, 0x1b, 0x6d, 0xdf, 0x6b, 0x76, 0xfd,
	0xae, 0xdf, 0xe4, 0xec, 0xc3, 0xd1, 0x11, 0xa7, 0x84, 0x30, 0x5b, 0x09, 0xc1, 0x8a, 0xd9, 0xf5,
	0xfd, 0x6e, 0x9f, 0xcc, 0x4e, 0x85, 0xae, 0x47, 0x68, 0xe8, 0x78, 0x43, 0x79, 0xa0, 0x26, 0xcd,
	0x3e, 0xed, 0x7b, 0x7e, 0x87, 0xf4, 0x9b, 0x34, 0x74, 0x42, 0x2a, 0xfe, 0x8a, 0x13, 0xd6, 0x23,
	0xc8, 0x3d, 0x18, 0xd1, 0x9e, 0x4d, 0x9e, 0x8e, 0x08, 0x0d, 0xf1, 0x1d, 0x48, 0xd3, 0x30, 0x20,
	0x8e, 0x47, 0xcb, 0xa8, 0x96, 0xa8, 0xe7, 0x36, 0x37, 0x1a, 0x91, 0xb3, 0xfb, 0x7c, 0x63, 0xbb,
	0xe3, 0x0c, 0x43, 0x12, 0xb4, 0xce, 0xbd, 0x1e, 0x9b, 0x86, 0x60, 0x4d, 0xc7, 0xa6, 0x92, 0xb2,
	0xd5, 0xc2, 0x2a, 0x42, 0x5e, 0x28, 0xa6, 0x43, 0x7f, 0x40, 0x89, 0xf5, 0xf7, 0x38, 0xe4, 0x1f,
	0x8e, 0x48, 0x70, 0xa2, 0x4c, 0x55, 0x20, 0x43, 0x49, 0x9f, 0xb4, 0x43, 0x3f, 0x28, 0xa3, 0x1a,
	0xaa, 0x67, 0xed, 0x88, 0xc6, 0x6b, 0x90, 0xea, 0xbb, 0x9e, 0x1b, 0x96, 0xe3, 0x35, 0x54, 0x2f,
	0xd8, 0x82, 0xc0, 0x5b, 0x90, 0xa2, 0xa1, 0x13, 0x84, 0xe5, 0x44, 0x0d, 0xd5, 0x73, 0x9b, 0x95,
	0x86, 0x08, 0xbf, 0xa1, 0xc2, 0x6f, 0x1c, 0xa8, 0xf0, 0x5b, 0x99, 0x17, 0x63, 0x33, 0xf6, 0xfc,
	0x9f, 0x26, 0xb2, 0x85, 0x08, 0xfe, 0x01, 0x24, 0xc8, 0xa0, 0x53, 0x4e, 0x2e, 0x21, 0xc9, 0x04,
	0xf0, 0x15, 0xc8, 0x76, 0xdc, 0x80, 0xb4, 0x43, 0xd7, 0x1f, 0x94, 0x53, 0x35, 0x54, 0x2f, 0x6e,
	0xae, 0xce, 0x20, 0xd9, 0x51, 0x5b, 0xf6, 0xec, 0x14, 0xbe, 0x04, 0x06, 0xed, 0x39, 0x41, 0x87,
	0x96, 0xd3, 0xb5, 0x44, 0x3d, 0xdb, 0x5a, 0x9b, 0x8e, 0xcd, 0x92, 0xe0, 0x5c, 0xf2, 0x3d, 0x37,
	0x24, 0xde, 0x30, 0x3c, 0xb1, 0xe5, 0x19, 0x7c, 0x01, 0xd2, 0x1d, 0xd2, 0x27, 0x21, 0xa1, 0xe5,
	0x0c, 0x47, 0xbc, 0xa4, 0xa9, 0xe7, 0x1b, 0xb6, 0x3a, 0x70, 0x37, 0x99, 0x31, 0x4a, 0x69, 0xeb,
	0xbf, 0x08, 0xf0, 0xbe, 0xe3, 0x0d, 0xfb, 0xe4, 0xa3, 0xf1, 0x8c, 0x90, 0x8b, 0x7f, 0x32, 0x72,
	0x89, 0x65, 0x91, 0x9b, 0xc1, 0x90, 0x5c, 0x0e, 0x86, 0xd4, 0x07, 0x60, 0xb0, 0xf6, 0xc0, 0x10,
	0xac, 0x0f, 0xe5, 0xd0, 0x2c, 0xe6, 0x84, 0x8a, 0xa6, 0x34, 0x8b, 0x26, 0xc1, 0xfd, 0xb4, 0x7e,
	0x0d, 0x05, 0x89, 0xa3, 0xc8, 0x54, 0xbc, 0xfd, 0xd1, 0x35, 0x50, 0x7c, 0x31, 0x36, 0xd1, 0xac,
	0x0e, 0xa2, 0xe4, 0xc7, 0x17, 0xb9, 0xed, 0x90, 0x4a, 0xbc, 0x57, 0x1a, 0xa2, 0xe4, 0x76, 0x07,
	0x5d, 0x42, 0x99, 0x60, 0x92, 0x41, 0x65, 0x8b, 0x33, 0xd6, 0xaf, 0x60, 0x75, 0xee, 0x3a, 0xa5,
	0x1b, 0xd7, 0xc0, 0xa0, 0x24, 0x70, 0x89, 0xf2, 0x42, 0x03, 0x64, 0x9f, 0xf3, 0x35, 0xf3, 0x9c,
	0xb6, 0xe5, 0xf9, 0xe5, 0xac, 0xff, 0x15, 0x41, 0x7e, 0xcf, 0x39, 0x24, 0x7d, 0x95, 0x47, 0x18,
	0x92, 0x03, 0xc7, 0x23, 0x12, 0x4f, 0xbe, 0xc6, 0xeb, 0x60, 0x3c, 0x73, 0xfa, 0x23, 0x22, 0x54,
	0x66, 0x6c, 0x49, 0x2d, 0x5b, 0x91, 0xe8, 0x93, 0x2b, 0x12, 0x45, 0x79, 0x65, 0x9d, 0x87, 0x82,
	0xf4, 0x57, 0x02, 0x35, 0x73, 0x8e, 0x01, 0x95, 0x55, 0xce, 0x59, 0xbf, 0x47, 0x50, 0x98, 0xbb,
	0x2f, 0x6c, 0x81, 0xd1, 0x67, 0xa2, 0x54, 0x04, 0xd7, 0x82, 0xe9, 0xd8, 0x94, 0x1c, 0x5b, 0xfe,
	0xb2, 0xdb, 0x27, 0x83, 0x90, 0xe3, 0x1e, 0xe7, 0xb8, 0xaf, 0xcf, 0x70, 0xff, 0xe9, 0x20, 0x0c,
	0x4e, 0xd4, 0xe5, 0xaf, 0x30, 0x14, 0x59, 0xeb, 0x93, 0xc7, 0x6d, 0xb5, 0xc0, 0x9f, 0x41, 0xb2,
	0xe7, 0xd0, 0x1e, 0x07, 0x25, 0xd9, 0x4a, 0x4d, 0xc7, 0x26, 0xba, 0x6c, 0x73, 0x96, 0xf5, 0x0c,
	0xf2, 0xba, 0x12, 0x7c, 0x07, 0xb2, 0x51, 0xcf, 0xe6, 0x4e, 0xfd, 0x7f, 0x28, 0x8a, 0xd2, 0x66,
	0x3c, 0xa4, 0x1c, 0x90, 0x99, 0x30, 0xfe, 0x02, 0x92, 0x7d, 0x77, 0x40, 0xf8, 0x05, 0x65, 0x5b,
	0x99, 0xe9, 0xd8, 0xe4, 0xb4, 0xcd, 0xff, 0x5a, 0x1e, 0x18, 0x22, 0xc7, 0xf0, 0xd7, 0x8b, 0x16,
	0x13, 0x2d, 0x43, 0x68, 0xd4, 0xb5, 0x99, 0x90, 0xe2, 0x28, 0x72, 0x75, 0xa8, 0x95, 0x9d, 0x8e,
	0x4d, 0xc1, 0xb0, 0xc5, 0x0f, 0x33, 0xa7, 0xc5, 0xc8, 0xcd, 0x31, 0x5a, 0x86, 0x79, 0x1b, 0xf2,
	0x7b, 0xa4, 0xeb, 0xb4, 0x4f, 0xa4, 0xd1, 0x35, 0xa5, 0x8e, 0x19, 0x44, 0x4a, 0xc7, 0x57, 0x90,
	0x8f, 0x2c, 0x3e, 0xf1, 0xa8, 0x2c, 0xd4, 0x5c, 0xc4, 0xfb, 0x19, 0xb5, 0xfe, 0x88, 0x40, 0x66,
	0xf7, 0x47, 0x5d, 0xde, 0x0d, 0x48, 0x53, 0x6e, 0x51, 0x5d, 0x9e, 0x5e, 0x34, 0x7c, 0x63, 0x76,
	0x6d, 0xf2, 0xa0, 0xad, 0x16, 0xb8, 0x01, 0x20, 0xea, 0xf7, 0xce, 0x2c, 0xb0, 0xe2, 0x74, 0x6c,
	0x6a, 0x5c, 0x5b, 0x5b, 0x5b, 0x7f, 0x40, 0x90, 0x3b, 0x70, 0xdc, 0xa8, 0x70, 0xd6, 0x20, 0xf5,
	0x94, 0x55, 0xb0, 0xac, 0x1c, 0x41, 0xb0, 0x16, 0xd5, 0x21, 0x7d, 0xe7, 0xe4, 0x96, 0x1f, 0x70,
	0x9d, 0x05, 0x3b, 0xa2, 0x67, 0x63, 0x2e, 0x79, 0xea, 0x98, 0x4b, 0x2d, 0xdd, 0xac, 0xef, 0x26,
	0x33, 0xf1, 0x52, 0xc2, 0xfa, 0x2d, 0x82, 0xbc, 0xf0, 0x4c, 0x96, 0xc8, 0x0d, 0x30, 0x84, 0xe3,
	0x32, 0xc7, 0xce, 0xec, 0x68, 0xa0, 0x75, 0x33, 0x29, 0x82, 0x7f, 0x02, 0xc5, 0x4e, 0xe0, 0x0f,
	0x87, 0xa4, 0xb3, 0x2f, 0xdb, 0x62, 0x7c, 0xb1, 0x2d, 0xee, 0xe8, 0xfb, 0xf6, 0xc2, 0x71, 0xeb,
	0x1f, 0xac, 0x10, 0x45, 0x8b, 0x92, 0x50, 0x45, 0x21, 0xa2, 0x4f, 0x9e, 0x47, 0xf1, 0x65, 0xe7,
	0xd1, 0x3a, 0x18, 0xdd, 0xc0, 0x1f, 0x0d, 0x69, 0x39, 0x21, 0xda, 0x84, 0xa0, 0x96, 0x9b, 0x53,
	0xd6, 0x5d, 0x28, 0xaa, 0x50, 0xce, 0xe8, 0xd3, 0x95, 0xc5, 0x3e, 0xbd, 0xdb, 0x21, 0x83, 0xd0,
	0x3d, 0x72, 0xa3, 0xce, 0x2b, 0xcf, 0x5b, 0xbf, 0x43, 0x50, 0x5a, 0x3c, 0x82, 0x7f, 0xac, 0xa5,
	0x39, 0x53, 0xf7, 0xcd, 0xd9, 0xea, 0x1a, 0xbc, 0x0f, 0x52, 0xde, 0x50, 0x54, 0x09, 0x54, 0xae,
	0x43, 0x4e, 0x63, 0xb3, 0x79, 0x77, 0x4c, 0x54, 0x4a, 0xb2, 0xe5, 0xac, 0x16, 0xe3, 0x22, 0x4d,
	0x39, 0xb1, 0x15, 0xbf, 0x86, 0x58, 0x42, 0x17, 0xe6, 0x6e, 0x12, 0x5f, 0x83, 0xe4, 0x51, 0xe0,
	0x7b, 0x4b, 0x5d, 0x13, 0x97, 0xc0, 0xdf, 0x83, 0x78, 0xe8, 0x2f, 0x75, 0x49, 0xf1, 0xd0, 0x67,
	0x77, 0x24, 0x83, 0x4f, 0x70, 0xe7, 0x24, 0x65, 0xfd, 0x05, 0xc1, 0x0a, 0x93, 0x11, 0x08, 0xdc,
	0xec, 0x8d, 0x06, 0xc7, 0xb8, 0x0e, 0x25, 0x66, 0xe9, 0x89, 0x2b, 0xc7, 0xda, 0x13, 0xb7, 0x23,
	0xc3, 0x2c, 0x32, 0xbe, 0x9a, 0x76, 0xbb, 0x1d, 0xbc, 0x01, 0xe9, 0x11, 0x15, 0x07, 0x44, 0xcc,
	0x06, 0x23, 0x77, 0x3b, 0xf8, 0xa2, 0x66, 0x8e, 0x61, 0xad, 0xbd, 0xec, 0x38, 0x86, 0x0f, 0x1c,
	0x37, 0x88, 0x7a, 0xcb, 0x79, 0x30, 0xda, 0xcc, 0xb0, 0xc8, 0x13, 0x36, 0x56, 0xa3, 0xc3, 0xdc,
	0x21, 0x5b, 0x6e, 0x5b, 0xdf, 0x87, 0x6c, 0x24, 0x7d, 0xea, 0x34, 0x3d, 0xf5, 0x06, 0xac, 0x1b,
	0xb0, 0x22, 0x7a, 0xe6, 0xe9, 0xc2, 0xf9, 0xd3, 0x84, 0xf3, 0x4a, 0xf8, 0x73, 0x48, 0x09, 0x54,
	0x30, 0x24, 0x3b, 0x4e, 0xe8, 0x28, 0x11, 0xb6, 0xb6, 0xca, 0xb0, 0x7e, 0x10, 0x38, 0x03, 0x7a,
	0x44, 0x02, 0x7e, 0x28, 0xca, 0x5d, 0xeb, 0x1c, 0xac, 0xb2, 0x3e, 0x41, 0x02, 0x7a, 0xd3, 0x1f,
	0x0d, 0x42, 0x59, 0x9e, 0xd6, 0x25, 0x58, 0x9b, 0x67, 0xcb, 0x54, 0x5f, 0x83, 0x54, 0x9b, 0x31,
	0xb8, 0xf6, 0x82, 0x2d, 0x08, 0xeb, 0x4f, 0x08, 0xf0, 0x6d, 0x12, 0x72, 0xd5, 0xbb, 0x3b, 0x54,
	0x7b, 0x8f, 0x7a, 0x4e, 0xd8, 0xee, 0x91, 0x80, 0xaa, 0xb7, 0x99, 0xa2, 0xbf, 0x8d, 0xf7, 0xa8,
	0x75, 0x05, 0x56, 0xe7, 0xbc, 0x94, 0x31, 0x55, 0x20, 0xd3, 0x96, 0x3c, 0xf9, 0x7e, 0x88, 0x68,
	0xeb, 0x6f, 0x71, 0xc8, 0x88, 0xbb, 0x25, 0x47, 0xf8, 0x0a, 0xe4, 0x8e, 0x58, 0xae, 0x05, 0xc3,
	0xc0, 0x95, 0x10, 0x24, 0x5b, 0x2b, 0xd3, 0xb1, 0xa9, 0xb3, 0x6d, 0x9d, 0xc0, 0x97, 0x17, 0x12,
	0xaf, 0xb5, 0x36, 0x19, 0x9b, 0xc6, 0x2f, 0x58, 0xf2, 0xed, 0xb0, 0xe9, 0xc5, 0xd3, 0x70, 0x27,
	0x4a, 0xc7, 0x7b, 0xb2, 0xda, 0xf8, 0xe3, 0xb4, 0x75, 0x95, 0xb9, 0xff, 0x7a, 0x6c, 0x9e, 0xd7,
	0xbe, 0x09, 0x87, 0x81, 0xef, 0x91, 0xb0, 0x47, 0x46, 0xb4, 0xd9, 0xf6, 0x3d, 0xcf, 0x1f, 0x34,
	0xf9, 0x77, 0x1d, 0x0f, 0x9a, 0x8d, 0x60, 0x26, 0x2e, 0x0b, 0xf0, 0x00, 0xd2, 0x61, 0x2f, 0xf0,
	0x47, 0xdd, 0x1e, 0x9f, 0x2e, 0x89, 0xd6, 0xd6, 0xf2, 0xfa, 0x94, 0x06, 0x5b, 0x2d, 0xf0, 0x57,
	0x0c, 0x2d, 0xd2, 0x3e, 0xa6, 0x23, 0x8f, 0x8f, 0xa7, 0x82, 0x7a, 0xde, 0x44, 0xec, 0x0b, 0xdf,
	0x40, 0x36, 0xfa, 0x2c, 0xc2, 0x39, 0x48, 0xdf, 0xba, 0x6f, 0x3f, 0xda, 0xb6, 0x77, 0x4a, 0x31,
	0x9c, 0x87, 0x4c, 0x6b, 0xfb, 0xe6, 0x3d, 0x4e, 0xa1, 0xcd, 0x6d, 0x30, 0xd8, 0x07, 0x22, 0x09,
	0xf0, 0x55, 0x48, 0xb2, 0x15, 0x3e, 0x37, 0xab, 0x28, 0xed, 0x9b, 0xb4, 0xb2, 0xbe, 0xc8, 0x96,
	0xc9, 0x1b, 0xdb, 0xfc, 0x4f, 0x02, 0xd2, 0xec, 0xd1, 0xcc, 0xfa, 0xe6, 0x0f, 0x21, 0xc5, 0xdf,
	0xcf, 0x58, 0x3b, 0xae, 0x7f, 0x1f, 0x55, 0x36, 0xde, 0xe3, 0x2b, 0x3d, 0xdf, 0x41, 0xf8, 0xe7,
	0x90, 0xe3, 0x4c, 0xf9, 0x5e, 0xf9, 0x62, 0xf1, 0xd9, 0x30, 0xa7, 0xe9, 0xcb, 0x33, 0x76, 0x35,
	0x7d, 0x5b, 0x90, 0xe2, 0x65, 0xac, 0x7b, 0xa3, 0xbf, 0xb2, 0x75, 0x6f, 0xe6, 0x5e, 0xb3, 0x56,
	0x0c, 0x5f, 0x87, 0x24, 0xab, 0x3e, 0x1d, 0x0e, 0xed, 0x99, 0xa1, 0xc3, 0xa1, 0xcf, 0x78, 0x6e,
	0xf6, 0x47, 0xd1, 0x6b, 0x69, 0x63, 0x71, 0x6c, 0x28, 0xf1, 0xf2, 0xfb, 0x1b, 0x91, 0xe5, 0xfb,
	0xe2, 0xd9, 0xa0, 0xea, 0x1e, 0x7f, 0x39, 0x6f, 0x6a, 0xa1, 0x4d, 0x54, 0xaa, 0x67, 0x6d, 0x47,
	0x0a, 0xf7, 0x20, 0xa7, 0xd5, 0x9c, 0x0e, 0xeb, 0xfb, 0x0d, 0x43, 0x87, 0xf5, 0x94, 0x42, 0xb5,
	0x62, 0x9b, 0xbf, 0x84, 0x8c, 0xea, 0xea, 0xf8, 0x21, 0x14, 0xe7, 0x7b, 0x1a, 0xfe, 0x4c, 0xf3,
	0x66, 0x7e, 0x54, 0x54, 0x6a, 0xda, 0xd6, 0xe9, 0x8d, 0x30, 0x56, 0x47, 0xad, 0xc7, 0x2f, 0xdf,
	0x54, 0x63, 0xaf, 0xde, 0x54, 0x63, 0xef, 0xde, 0x54, 0xd1, 0x6f, 0x26, 0x55, 0xf4, 0xe7, 0x49,
	0x15, 0xbd, 0x98, 0x54, 0xd1, 0xcb, 0x49, 0x15, 0xfd, 0x6b, 0x52, 0x45, 0xff, 0x9e, 0x54, 0x63,
	0xef, 0x26, 0x55, 0xf4, 0xfc, 0x6d, 0x35, 0xf6, 0xf2, 0x6d, 0x35, 0xf6, 0xea, 0x6d, 0x35, 0xf6,
	0xf8, 0x6b, 0xfd, 0x5f, 0x36, 0x81, 0x73, 0xe4, 0x0c, 0x9c, 0x66, 0xdf, 0x3f, 0x76, 0x9b, 0xfa,
	0x7f, 0x7c, 0x0e, 0x0d, 0xfe, 0xf3, 0xdd, 0xff, 0x05, 0x00, 0x00, 0xff, 0xff, 0xda, 0xa4, 0x4e,
	0xd8, 0x08, 0x12, 0x00, 0x00,
}

func (x Direction) String() string {
	s, ok := Direction_name[int32(x)]
	if ok {
		return s
	}
	return strconv.Itoa(int(x))
}
func (this *PushRequest) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*PushRequest)
	if !ok {
		that2, ok := that.(PushRequest)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if len(this.Streams) != len(that1.Streams) {
		return false
	}
	for i := range this.Streams {
		if !this.Streams[i].Equal(that1.Streams[i]) {
			return false
		}
	}
	return true
}
func (this *PushResponse) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*PushResponse)
	if !ok {
		that2, ok := that.(PushResponse)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	return true
}
func (this *QueryRequest) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*QueryRequest)
	if !ok {
		that2, ok := that.(QueryRequest)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.Selector != that1.Selector {
		return false
	}
	if this.Limit != that1.Limit {
		return false
	}
	if !this.Start.Equal(that1.Start) {
		return false
	}
	if !this.End.Equal(that1.End) {
		return false
	}
	if this.Direction != that1.Direction {
		return false
	}
	if len(this.Shards) != len(that1.Shards) {
		return false
	}
	for i := range this.Shards {
		if this.Shards[i] != that1.Shards[i] {
			return false
		}
	}
	if len(this.Deletes) != len(that1.Deletes) {
		return false
	}
	for i := range this.Deletes {
		if !this.Deletes[i].Equal(that1.Deletes[i]) {
			return false
		}
	}
	return true
}
func (this *SampleQueryRequest) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*SampleQueryRequest)
	if !ok {
		that2, ok := that.(SampleQueryRequest)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.Selector != that1.Selector {
		return false
	}
	if !this.Start.Equal(that1.Start) {
		return false
	}
	if !this.End.Equal(that1.End) {
		return false
	}
	if len(this.Shards) != len(that1.Shards) {
		return false
	}
	for i := range this.Shards {
		if this.Shards[i] != that1.Shards[i] {
			return false
		}
	}
	if len(this.Deletes) != len(that1.Deletes) {
		return false
	}
	for i := range this.Deletes {
		if !this.Deletes[i].Equal(that1.Deletes[i]) {
			return false
		}
	}
	return true
}
func (this *Delete) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*Delete)
	if !ok {
		that2, ok := that.(Delete)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.Selector != that1.Selector {
		return false
	}
	if this.Start != that1.Start {
		return false
	}
	if this.End != that1.End {
		return false
	}
	return true
}
func (this *QueryResponse) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*QueryResponse)
	if !ok {
		that2, ok := that.(QueryResponse)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if len(this.Streams) != len(that1.Streams) {
		return false
	}
	for i := range this.Streams {
		if !this.Streams[i].Equal(that1.Streams[i]) {
			return false
		}
	}
	if !this.Stats.Equal(&that1.Stats) {
		return false
	}
	return true
}
func (this *SampleQueryResponse) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*SampleQueryResponse)
	if !ok {
		that2, ok := that.(SampleQueryResponse)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if len(this.Series) != len(that1.Series) {
		return false
	}
	for i := range this.Series {
		if !this.Series[i].Equal(that1.Series[i]) {
			return false
		}
	}
	if !this.Stats.Equal(&that1.Stats) {
		return false
	}
	return true
}
func (this *LabelRequest) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*LabelRequest)
	if !ok {
		that2, ok := that.(LabelRequest)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.Name != that1.Name {
		return false
	}
	if this.Values != that1.Values {
		return false
	}
	if that1.Start == nil {
		if this.Start != nil {
			return false
		}
	} else if !this.Start.Equal(*that1.Start) {
		return false
	}
	if that1.End == nil {
		if this.End != nil {
			return false
		}
	} else if !this.End.Equal(*that1.End) {
		return false
	}
	return true
}
func (this *LabelResponse) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*LabelResponse)
	if !ok {
		that2, ok := that.(LabelResponse)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if len(this.Values) != len(that1.Values) {
		return false
	}
	for i := range this.Values {
		if this.Values[i] != that1.Values[i] {
			return false
		}
	}
	return true
}
func (this *StreamAdapter) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*StreamAdapter)
	if !ok {
		that2, ok := that.(StreamAdapter)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.Labels != that1.Labels {
		return false
	}
	if len(this.Entries) != len(that1.Entries) {
		return false
	}
	for i := range this.Entries {
		if !this.Entries[i].Equal(&that1.Entries[i]) {
			return false
		}
	}
	if this.Hash != that1.Hash {
		return false
	}
	return true
}
func (this *EntryAdapter) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*EntryAdapter)
	if !ok {
		that2, ok := that.(EntryAdapter)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if !this.Timestamp.Equal(that1.Timestamp) {
		return false
	}
	if this.Line != that1.Line {
		return false
	}
	return true
}
func (this *Sample) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*Sample)
	if !ok {
		that2, ok := that.(Sample)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.Timestamp != that1.Timestamp {
		return false
	}
	if this.Value != that1.Value {
		return false
	}
	if this.Hash != that1.Hash {
		return false
	}
	return true
}
func (this *LegacySample) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*LegacySample)
	if !ok {
		that2, ok := that.(LegacySample)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.Value != that1.Value {
		return false
	}
	if this.TimestampMs != that1.TimestampMs {
		return false
	}
	return true
}
func (this *Series) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*Series)
	if !ok {
		that2, ok := that.(Series)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.Labels != that1.Labels {
		return false
	}
	if len(this.Samples) != len(that1.Samples) {
		return false
	}
	for i := range this.Samples {
		if !this.Samples[i].Equal(&that1.Samples[i]) {
			return false
		}
	}
	if this.StreamHash != that1.StreamHash {
		return false
	}
	return true
}
func (this *TailRequest) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*TailRequest)
	if !ok {
		that2, ok := that.(TailRequest)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.Query != that1.Query {
		return false
	}
	if this.DelayFor != that1.DelayFor {
		return false
	}
	if this.Limit != that1.Limit {
		return false
	}
	if !this.Start.Equal(that1.Start) {
		return false
	}
	return true
}
func (this *TailResponse) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*TailResponse)
	if !ok {
		that2, ok := that.(TailResponse)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if that1.Stream == nil {
		if this.Stream != nil {
			return false
		}
	} else if !this.Stream.Equal(*that1.Stream) {
		return false
	}
	if len(this.DroppedStreams) != len(that1.DroppedStreams) {
		return false
	}
	for i := range this.DroppedStreams {
		if !this.DroppedStreams[i].Equal(that1.DroppedStreams[i]) {
			return false
		}
	}
	return true
}
func (this *SeriesRequest) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*SeriesRequest)
	if !ok {
		that2, ok := that.(SeriesRequest)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if !this.Start.Equal(that1.Start) {
		return false
	}
	if !this.End.Equal(that1.End) {
		return false
	}
	if len(this.Groups) != len(that1.Groups) {
		return false
	}
	for i := range this.Groups {
		if this.Groups[i] != that1.Groups[i] {
			return false
		}
	}
	if len(this.Shards) != len(that1.Shards) {
		return false
	}
	for i := range this.Shards {
		if this.Shards[i] != that1.Shards[i] {
			return false
		}
	}
	return true
}
func (this *SeriesResponse) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*SeriesResponse)
	if !ok {
		that2, ok := that.(SeriesResponse)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if len(this.Series) != len(that1.Series) {
		return false
	}
	for i := range this.Series {
		if !this.Series[i].Equal(&that1.Series[i]) {
			return false
		}
	}
	return true
}
func (this *SeriesIdentifier) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*SeriesIdentifier)
	if !ok {
		that2, ok := that.(SeriesIdentifier)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if len(this.Labels) != len(that1.Labels) {
		return false
	}
	for i := range this.Labels {
		if this.Labels[i] != that1.Labels[i] {
			return false
		}
	}
	return true
}
func (this *DroppedStream) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*DroppedStream)
	if !ok {
		that2, ok := that.(DroppedStream)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if !this.From.Equal(that1.From) {
		return false
	}
	if !this.To.Equal(that1.To) {
		return false
	}
	if this.Labels != that1.Labels {
		return false
	}
	return true
}
func (this *TimeSeriesChunk) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*TimeSeriesChunk)
	if !ok {
		that2, ok := that.(TimeSeriesChunk)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.FromIngesterId != that1.FromIngesterId {
		return false
	}
	if this.UserId != that1.UserId {
		return false
	}
	if len(this.Labels) != len(that1.Labels) {
		return false
	}
	for i := range this.Labels {
		if !this.Labels[i].Equal(that1.Labels[i]) {
			return false
		}
	}
	if len(this.Chunks) != len(that1.Chunks) {
		return false
	}
	for i := range this.Chunks {
		if !this.Chunks[i].Equal(that1.Chunks[i]) {
			return false
		}
	}
	return true
}
func (this *LabelPair) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*LabelPair)
	if !ok {
		that2, ok := that.(LabelPair)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.Name != that1.Name {
		return false
	}
	if this.Value != that1.Value {
		return false
	}
	return true
}
func (this *LegacyLabelPair) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*LegacyLabelPair)
	if !ok {
		that2, ok := that.(LegacyLabelPair)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if !bytes.Equal(this.Name, that1.Name) {
		return false
	}
	if !bytes.Equal(this.Value, that1.Value) {
		return false
	}
	return true
}
func (this *Chunk) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*Chunk)
	if !ok {
		that2, ok := that.(Chunk)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if !bytes.Equal(this.Data, that1.Data) {
		return false
	}
	return true
}
func (this *TransferChunksResponse) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*TransferChunksResponse)
	if !ok {
		that2, ok := that.(TransferChunksResponse)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	return true
}
func (this *TailersCountRequest) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*TailersCountRequest)
	if !ok {
		that2, ok := that.(TailersCountRequest)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	return true
}
func (this *TailersCountResponse) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*TailersCountResponse)
	if !ok {
		that2, ok := that.(TailersCountResponse)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.Count != that1.Count {
		return false
	}
	return true
}
func (this *GetChunkIDsRequest) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*GetChunkIDsRequest)
	if !ok {
		that2, ok := that.(GetChunkIDsRequest)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.Matchers != that1.Matchers {
		return false
	}
	if !this.Start.Equal(that1.Start) {
		return false
	}
	if !this.End.Equal(that1.End) {
		return false
	}
	return true
}
func (this *GetChunkIDsResponse) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*GetChunkIDsResponse)
	if !ok {
		that2, ok := that.(GetChunkIDsResponse)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if len(this.ChunkIDs) != len(that1.ChunkIDs) {
		return false
	}
	for i := range this.ChunkIDs {
		if this.ChunkIDs[i] != that1.ChunkIDs[i] {
			return false
		}
	}
	return true
}
func (this *ChunkRef) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*ChunkRef)
	if !ok {
		that2, ok := that.(ChunkRef)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.Fingerprint != that1.Fingerprint {
		return false
	}
	if this.UserID != that1.UserID {
		return false
	}
	if !this.From.Equal(that1.From) {
		return false
	}
	if !this.Through.Equal(that1.Through) {
		return false
	}
	if this.Checksum != that1.Checksum {
		return false
	}
	return true
}
func (this *PushRequest) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 5)
	s = append(s, "&logproto.PushRequest{")
	s = append(s, "Streams: "+fmt.Sprintf("%#v", this.Streams)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *PushResponse) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 4)
	s = append(s, "&logproto.PushResponse{")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *QueryRequest) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 11)
	s = append(s, "&logproto.QueryRequest{")
	s = append(s, "Selector: "+fmt.Sprintf("%#v", this.Selector)+",\n")
	s = append(s, "Limit: "+fmt.Sprintf("%#v", this.Limit)+",\n")
	s = append(s, "Start: "+fmt.Sprintf("%#v", this.Start)+",\n")
	s = append(s, "End: "+fmt.Sprintf("%#v", this.End)+",\n")
	s = append(s, "Direction: "+fmt.Sprintf("%#v", this.Direction)+",\n")
	s = append(s, "Shards: "+fmt.Sprintf("%#v", this.Shards)+",\n")
	if this.Deletes != nil {
		s = append(s, "Deletes: "+fmt.Sprintf("%#v", this.Deletes)+",\n")
	}
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *SampleQueryRequest) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 9)
	s = append(s, "&logproto.SampleQueryRequest{")
	s = append(s, "Selector: "+fmt.Sprintf("%#v", this.Selector)+",\n")
	s = append(s, "Start: "+fmt.Sprintf("%#v", this.Start)+",\n")
	s = append(s, "End: "+fmt.Sprintf("%#v", this.End)+",\n")
	s = append(s, "Shards: "+fmt.Sprintf("%#v", this.Shards)+",\n")
	if this.Deletes != nil {
		s = append(s, "Deletes: "+fmt.Sprintf("%#v", this.Deletes)+",\n")
	}
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *Delete) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 7)
	s = append(s, "&logproto.Delete{")
	s = append(s, "Selector: "+fmt.Sprintf("%#v", this.Selector)+",\n")
	s = append(s, "Start: "+fmt.Sprintf("%#v", this.Start)+",\n")
	s = append(s, "End: "+fmt.Sprintf("%#v", this.End)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *QueryResponse) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 6)
	s = append(s, "&logproto.QueryResponse{")
	s = append(s, "Streams: "+fmt.Sprintf("%#v", this.Streams)+",\n")
	s = append(s, "Stats: "+strings.Replace(this.Stats.GoString(), `&`, ``, 1)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *SampleQueryResponse) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 6)
	s = append(s, "&logproto.SampleQueryResponse{")
	s = append(s, "Series: "+fmt.Sprintf("%#v", this.Series)+",\n")
	s = append(s, "Stats: "+strings.Replace(this.Stats.GoString(), `&`, ``, 1)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *LabelRequest) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 8)
	s = append(s, "&logproto.LabelRequest{")
	s = append(s, "Name: "+fmt.Sprintf("%#v", this.Name)+",\n")
	s = append(s, "Values: "+fmt.Sprintf("%#v", this.Values)+",\n")
	s = append(s, "Start: "+fmt.Sprintf("%#v", this.Start)+",\n")
	s = append(s, "End: "+fmt.Sprintf("%#v", this.End)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *LabelResponse) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 5)
	s = append(s, "&logproto.LabelResponse{")
	s = append(s, "Values: "+fmt.Sprintf("%#v", this.Values)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *StreamAdapter) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 7)
	s = append(s, "&logproto.StreamAdapter{")
	s = append(s, "Labels: "+fmt.Sprintf("%#v", this.Labels)+",\n")
	if this.Entries != nil {
		vs := make([]*EntryAdapter, len(this.Entries))
		for i := range vs {
			vs[i] = &this.Entries[i]
		}
		s = append(s, "Entries: "+fmt.Sprintf("%#v", vs)+",\n")
	}
	s = append(s, "Hash: "+fmt.Sprintf("%#v", this.Hash)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *EntryAdapter) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 6)
	s = append(s, "&logproto.EntryAdapter{")
	s = append(s, "Timestamp: "+fmt.Sprintf("%#v", this.Timestamp)+",\n")
	s = append(s, "Line: "+fmt.Sprintf("%#v", this.Line)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *Sample) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 7)
	s = append(s, "&logproto.Sample{")
	s = append(s, "Timestamp: "+fmt.Sprintf("%#v", this.Timestamp)+",\n")
	s = append(s, "Value: "+fmt.Sprintf("%#v", this.Value)+",\n")
	s = append(s, "Hash: "+fmt.Sprintf("%#v", this.Hash)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *LegacySample) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 6)
	s = append(s, "&logproto.LegacySample{")
	s = append(s, "Value: "+fmt.Sprintf("%#v", this.Value)+",\n")
	s = append(s, "TimestampMs: "+fmt.Sprintf("%#v", this.TimestampMs)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *Series) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 7)
	s = append(s, "&logproto.Series{")
	s = append(s, "Labels: "+fmt.Sprintf("%#v", this.Labels)+",\n")
	if this.Samples != nil {
		vs := make([]*Sample, len(this.Samples))
		for i := range vs {
			vs[i] = &this.Samples[i]
		}
		s = append(s, "Samples: "+fmt.Sprintf("%#v", vs)+",\n")
	}
	s = append(s, "StreamHash: "+fmt.Sprintf("%#v", this.StreamHash)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *TailRequest) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 8)
	s = append(s, "&logproto.TailRequest{")
	s = append(s, "Query: "+fmt.Sprintf("%#v", this.Query)+",\n")
	s = append(s, "DelayFor: "+fmt.Sprintf("%#v", this.DelayFor)+",\n")
	s = append(s, "Limit: "+fmt.Sprintf("%#v", this.Limit)+",\n")
	s = append(s, "Start: "+fmt.Sprintf("%#v", this.Start)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *TailResponse) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 6)
	s = append(s, "&logproto.TailResponse{")
	s = append(s, "Stream: "+fmt.Sprintf("%#v", this.Stream)+",\n")
	if this.DroppedStreams != nil {
		s = append(s, "DroppedStreams: "+fmt.Sprintf("%#v", this.DroppedStreams)+",\n")
	}
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *SeriesRequest) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 8)
	s = append(s, "&logproto.SeriesRequest{")
	s = append(s, "Start: "+fmt.Sprintf("%#v", this.Start)+",\n")
	s = append(s, "End: "+fmt.Sprintf("%#v", this.End)+",\n")
	s = append(s, "Groups: "+fmt.Sprintf("%#v", this.Groups)+",\n")
	s = append(s, "Shards: "+fmt.Sprintf("%#v", this.Shards)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *SeriesResponse) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 5)
	s = append(s, "&logproto.SeriesResponse{")
	if this.Series != nil {
		vs := make([]*SeriesIdentifier, len(this.Series))
		for i := range vs {
			vs[i] = &this.Series[i]
		}
		s = append(s, "Series: "+fmt.Sprintf("%#v", vs)+",\n")
	}
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *SeriesIdentifier) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 5)
	s = append(s, "&logproto.SeriesIdentifier{")
	keysForLabels := make([]string, 0, len(this.Labels))
	for k, _ := range this.Labels {
		keysForLabels = append(keysForLabels, k)
	}
	github_com_gogo_protobuf_sortkeys.Strings(keysForLabels)
	mapStringForLabels := "map[string]string{"
	for _, k := range keysForLabels {
		mapStringForLabels += fmt.Sprintf("%#v: %#v,", k, this.Labels[k])
	}
	mapStringForLabels += "}"
	if this.Labels != nil {
		s = append(s, "Labels: "+mapStringForLabels+",\n")
	}
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *DroppedStream) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 7)
	s = append(s, "&logproto.DroppedStream{")
	s = append(s, "From: "+fmt.Sprintf("%#v", this.From)+",\n")
	s = append(s, "To: "+fmt.Sprintf("%#v", this.To)+",\n")
	s = append(s, "Labels: "+fmt.Sprintf("%#v", this.Labels)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *TimeSeriesChunk) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 8)
	s = append(s, "&logproto.TimeSeriesChunk{")
	s = append(s, "FromIngesterId: "+fmt.Sprintf("%#v", this.FromIngesterId)+",\n")
	s = append(s, "UserId: "+fmt.Sprintf("%#v", this.UserId)+",\n")
	if this.Labels != nil {
		s = append(s, "Labels: "+fmt.Sprintf("%#v", this.Labels)+",\n")
	}
	if this.Chunks != nil {
		s = append(s, "Chunks: "+fmt.Sprintf("%#v", this.Chunks)+",\n")
	}
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *LabelPair) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 6)
	s = append(s, "&logproto.LabelPair{")
	s = append(s, "Name: "+fmt.Sprintf("%#v", this.Name)+",\n")
	s = append(s, "Value: "+fmt.Sprintf("%#v", this.Value)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *LegacyLabelPair) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 6)
	s = append(s, "&logproto.LegacyLabelPair{")
	s = append(s, "Name: "+fmt.Sprintf("%#v", this.Name)+",\n")
	s = append(s, "Value: "+fmt.Sprintf("%#v", this.Value)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *Chunk) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 5)
	s = append(s, "&logproto.Chunk{")
	s = append(s, "Data: "+fmt.Sprintf("%#v", this.Data)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *TransferChunksResponse) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 4)
	s = append(s, "&logproto.TransferChunksResponse{")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *TailersCountRequest) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 4)
	s = append(s, "&logproto.TailersCountRequest{")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *TailersCountResponse) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 5)
	s = append(s, "&logproto.TailersCountResponse{")
	s = append(s, "Count: "+fmt.Sprintf("%#v", this.Count)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *GetChunkIDsRequest) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 7)
	s = append(s, "&logproto.GetChunkIDsRequest{")
	s = append(s, "Matchers: "+fmt.Sprintf("%#v", this.Matchers)+",\n")
	s = append(s, "Start: "+fmt.Sprintf("%#v", this.Start)+",\n")
	s = append(s, "End: "+fmt.Sprintf("%#v", this.End)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *GetChunkIDsResponse) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 5)
	s = append(s, "&logproto.GetChunkIDsResponse{")
	s = append(s, "ChunkIDs: "+fmt.Sprintf("%#v", this.ChunkIDs)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *ChunkRef) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 9)
	s = append(s, "&logproto.ChunkRef{")
	s = append(s, "Fingerprint: "+fmt.Sprintf("%#v", this.Fingerprint)+",\n")
	s = append(s, "UserID: "+fmt.Sprintf("%#v", this.UserID)+",\n")
	s = append(s, "From: "+fmt.Sprintf("%#v", this.From)+",\n")
	s = append(s, "Through: "+fmt.Sprintf("%#v", this.Through)+",\n")
	s = append(s, "Checksum: "+fmt.Sprintf("%#v", this.Checksum)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func valueToGoStringLogproto(v interface{}, typ string) string {
	rv := reflect.ValueOf(v)
	if rv.IsNil() {
		return "nil"
	}
	pv := reflect.Indirect(rv).Interface()
	return fmt.Sprintf("func(v %v) *%v { return &v } ( %#v )", typ, typ, pv)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// PusherClient is the client API for Pusher service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type PusherClient interface {
	Push(ctx context.Context, in *PushRequest, opts ...grpc.CallOption) (*PushResponse, error)
}

type pusherClient struct {
	cc *grpc.ClientConn
}

func NewPusherClient(cc *grpc.ClientConn) PusherClient {
	return &pusherClient{cc}
}

func (c *pusherClient) Push(ctx context.Context, in *PushRequest, opts ...grpc.CallOption) (*PushResponse, error) {
	out := new(PushResponse)
	err := c.cc.Invoke(ctx, "/logproto.Pusher/Push", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PusherServer is the server API for Pusher service.
type PusherServer interface {
	Push(context.Context, *PushRequest) (*PushResponse, error)
}

// UnimplementedPusherServer can be embedded to have forward compatible implementations.
type UnimplementedPusherServer struct {
}

func (*UnimplementedPusherServer) Push(ctx context.Context, req *PushRequest) (*PushResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Push not implemented")
}

func RegisterPusherServer(s *grpc.Server, srv PusherServer) {
	s.RegisterService(&_Pusher_serviceDesc, srv)
}

func _Pusher_Push_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PushRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PusherServer).Push(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logproto.Pusher/Push",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PusherServer).Push(ctx, req.(*PushRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _Pusher_serviceDesc = grpc.ServiceDesc{
	ServiceName: "logproto.Pusher",
	HandlerType: (*PusherServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Push",
			Handler:    _Pusher_Push_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "pkg/logproto/logproto.proto",
}

// QuerierClient is the client API for Querier service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type QuerierClient interface {
	Query(ctx context.Context, in *QueryRequest, opts ...grpc.CallOption) (Querier_QueryClient, error)
	QuerySample(ctx context.Context, in *SampleQueryRequest, opts ...grpc.CallOption) (Querier_QuerySampleClient, error)
	Label(ctx context.Context, in *LabelRequest, opts ...grpc.CallOption) (*LabelResponse, error)
	Tail(ctx context.Context, in *TailRequest, opts ...grpc.CallOption) (Querier_TailClient, error)
	Series(ctx context.Context, in *SeriesRequest, opts ...grpc.CallOption) (*SeriesResponse, error)
	TailersCount(ctx context.Context, in *TailersCountRequest, opts ...grpc.CallOption) (*TailersCountResponse, error)
	GetChunkIDs(ctx context.Context, in *GetChunkIDsRequest, opts ...grpc.CallOption) (*GetChunkIDsResponse, error)
}

type querierClient struct {
	cc *grpc.ClientConn
}

func NewQuerierClient(cc *grpc.ClientConn) QuerierClient {
	return &querierClient{cc}
}

func (c *querierClient) Query(ctx context.Context, in *QueryRequest, opts ...grpc.CallOption) (Querier_QueryClient, error) {
	stream, err := c.cc.NewStream(ctx, &_Querier_serviceDesc.Streams[0], "/logproto.Querier/Query", opts...)
	if err != nil {
		return nil, err
	}
	x := &querierQueryClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type Querier_QueryClient interface {
	Recv() (*QueryResponse, error)
	grpc.ClientStream
}

type querierQueryClient struct {
	grpc.ClientStream
}

func (x *querierQueryClient) Recv() (*QueryResponse, error) {
	m := new(QueryResponse)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *querierClient) QuerySample(ctx context.Context, in *SampleQueryRequest, opts ...grpc.CallOption) (Querier_QuerySampleClient, error) {
	stream, err := c.cc.NewStream(ctx, &_Querier_serviceDesc.Streams[1], "/logproto.Querier/QuerySample", opts...)
	if err != nil {
		return nil, err
	}
	x := &querierQuerySampleClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type Querier_QuerySampleClient interface {
	Recv() (*SampleQueryResponse, error)
	grpc.ClientStream
}

type querierQuerySampleClient struct {
	grpc.ClientStream
}

func (x *querierQuerySampleClient) Recv() (*SampleQueryResponse, error) {
	m := new(SampleQueryResponse)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *querierClient) Label(ctx context.Context, in *LabelRequest, opts ...grpc.CallOption) (*LabelResponse, error) {
	out := new(LabelResponse)
	err := c.cc.Invoke(ctx, "/logproto.Querier/Label", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *querierClient) Tail(ctx context.Context, in *TailRequest, opts ...grpc.CallOption) (Querier_TailClient, error) {
	stream, err := c.cc.NewStream(ctx, &_Querier_serviceDesc.Streams[2], "/logproto.Querier/Tail", opts...)
	if err != nil {
		return nil, err
	}
	x := &querierTailClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type Querier_TailClient interface {
	Recv() (*TailResponse, error)
	grpc.ClientStream
}

type querierTailClient struct {
	grpc.ClientStream
}

func (x *querierTailClient) Recv() (*TailResponse, error) {
	m := new(TailResponse)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *querierClient) Series(ctx context.Context, in *SeriesRequest, opts ...grpc.CallOption) (*SeriesResponse, error) {
	out := new(SeriesResponse)
	err := c.cc.Invoke(ctx, "/logproto.Querier/Series", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *querierClient) TailersCount(ctx context.Context, in *TailersCountRequest, opts ...grpc.CallOption) (*TailersCountResponse, error) {
	out := new(TailersCountResponse)
	err := c.cc.Invoke(ctx, "/logproto.Querier/TailersCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *querierClient) GetChunkIDs(ctx context.Context, in *GetChunkIDsRequest, opts ...grpc.CallOption) (*GetChunkIDsResponse, error) {
	out := new(GetChunkIDsResponse)
	err := c.cc.Invoke(ctx, "/logproto.Querier/GetChunkIDs", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// QuerierServer is the server API for Querier service.
type QuerierServer interface {
	Query(*QueryRequest, Querier_QueryServer) error
	QuerySample(*SampleQueryRequest, Querier_QuerySampleServer) error
	Label(context.Context, *LabelRequest) (*LabelResponse, error)
	Tail(*TailRequest, Querier_TailServer) error
	Series(context.Context, *SeriesRequest) (*SeriesResponse, error)
	TailersCount(context.Context, *TailersCountRequest) (*TailersCountResponse, error)
	GetChunkIDs(context.Context, *GetChunkIDsRequest) (*GetChunkIDsResponse, error)
}

// UnimplementedQuerierServer can be embedded to have forward compatible implementations.
type UnimplementedQuerierServer struct {
}

func (*UnimplementedQuerierServer) Query(req *QueryRequest, srv Querier_QueryServer) error {
	return status.Errorf(codes.Unimplemented, "method Query not implemented")
}
func (*UnimplementedQuerierServer) QuerySample(req *SampleQueryRequest, srv Querier_QuerySampleServer) error {
	return status.Errorf(codes.Unimplemented, "method QuerySample not implemented")
}
func (*UnimplementedQuerierServer) Label(ctx context.Context, req *LabelRequest) (*LabelResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Label not implemented")
}
func (*UnimplementedQuerierServer) Tail(req *TailRequest, srv Querier_TailServer) error {
	return status.Errorf(codes.Unimplemented, "method Tail not implemented")
}
func (*UnimplementedQuerierServer) Series(ctx context.Context, req *SeriesRequest) (*SeriesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Series not implemented")
}
func (*UnimplementedQuerierServer) TailersCount(ctx context.Context, req *TailersCountRequest) (*TailersCountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TailersCount not implemented")
}
func (*UnimplementedQuerierServer) GetChunkIDs(ctx context.Context, req *GetChunkIDsRequest) (*GetChunkIDsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetChunkIDs not implemented")
}

func RegisterQuerierServer(s *grpc.Server, srv QuerierServer) {
	s.RegisterService(&_Querier_serviceDesc, srv)
}

func _Querier_Query_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(QueryRequest)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(QuerierServer).Query(m, &querierQueryServer{stream})
}

type Querier_QueryServer interface {
	Send(*QueryResponse) error
	grpc.ServerStream
}

type querierQueryServer struct {
	grpc.ServerStream
}

func (x *querierQueryServer) Send(m *QueryResponse) error {
	return x.ServerStream.SendMsg(m)
}

func _Querier_QuerySample_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(SampleQueryRequest)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(QuerierServer).QuerySample(m, &querierQuerySampleServer{stream})
}

type Querier_QuerySampleServer interface {
	Send(*SampleQueryResponse) error
	grpc.ServerStream
}

type querierQuerySampleServer struct {
	grpc.ServerStream
}

func (x *querierQuerySampleServer) Send(m *SampleQueryResponse) error {
	return x.ServerStream.SendMsg(m)
}

func _Querier_Label_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LabelRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(QuerierServer).Label(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logproto.Querier/Label",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(QuerierServer).Label(ctx, req.(*LabelRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Querier_Tail_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(TailRequest)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(QuerierServer).Tail(m, &querierTailServer{stream})
}

type Querier_TailServer interface {
	Send(*TailResponse) error
	grpc.ServerStream
}

type querierTailServer struct {
	grpc.ServerStream
}

func (x *querierTailServer) Send(m *TailResponse) error {
	return x.ServerStream.SendMsg(m)
}

func _Querier_Series_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SeriesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(QuerierServer).Series(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logproto.Querier/Series",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(QuerierServer).Series(ctx, req.(*SeriesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Querier_TailersCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TailersCountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(QuerierServer).TailersCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logproto.Querier/TailersCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(QuerierServer).TailersCount(ctx, req.(*TailersCountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Querier_GetChunkIDs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChunkIDsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(QuerierServer).GetChunkIDs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logproto.Querier/GetChunkIDs",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(QuerierServer).GetChunkIDs(ctx, req.(*GetChunkIDsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _Querier_serviceDesc = grpc.ServiceDesc{
	ServiceName: "logproto.Querier",
	HandlerType: (*QuerierServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Label",
			Handler:    _Querier_Label_Handler,
		},
		{
			MethodName: "Series",
			Handler:    _Querier_Series_Handler,
		},
		{
			MethodName: "TailersCount",
			Handler:    _Querier_TailersCount_Handler,
		},
		{
			MethodName: "GetChunkIDs",
			Handler:    _Querier_GetChunkIDs_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "Query",
			Handler:       _Querier_Query_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "QuerySample",
			Handler:       _Querier_QuerySample_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "Tail",
			Handler:       _Querier_Tail_Handler,
			ServerStreams: true,
		},
	},
	Metadata: "pkg/logproto/logproto.proto",
}

// IngesterClient is the client API for Ingester service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type IngesterClient interface {
	TransferChunks(ctx context.Context, opts ...grpc.CallOption) (Ingester_TransferChunksClient, error)
}

type ingesterClient struct {
	cc *grpc.ClientConn
}

func NewIngesterClient(cc *grpc.ClientConn) IngesterClient {
	return &ingesterClient{cc}
}

func (c *ingesterClient) TransferChunks(ctx context.Context, opts ...grpc.CallOption) (Ingester_TransferChunksClient, error) {
	stream, err := c.cc.NewStream(ctx, &_Ingester_serviceDesc.Streams[0], "/logproto.Ingester/TransferChunks", opts...)
	if err != nil {
		return nil, err
	}
	x := &ingesterTransferChunksClient{stream}
	return x, nil
}

type Ingester_TransferChunksClient interface {
	Send(*TimeSeriesChunk) error
	CloseAndRecv() (*TransferChunksResponse, error)
	grpc.ClientStream
}

type ingesterTransferChunksClient struct {
	grpc.ClientStream
}

func (x *ingesterTransferChunksClient) Send(m *TimeSeriesChunk) error {
	return x.ClientStream.SendMsg(m)
}

func (x *ingesterTransferChunksClient) CloseAndRecv() (*TransferChunksResponse, error) {
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	m := new(TransferChunksResponse)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

// IngesterServer is the server API for Ingester service.
type IngesterServer interface {
	TransferChunks(Ingester_TransferChunksServer) error
}

// UnimplementedIngesterServer can be embedded to have forward compatible implementations.
type UnimplementedIngesterServer struct {
}

func (*UnimplementedIngesterServer) TransferChunks(srv Ingester_TransferChunksServer) error {
	return status.Errorf(codes.Unimplemented, "method TransferChunks not implemented")
}

func RegisterIngesterServer(s *grpc.Server, srv IngesterServer) {
	s.RegisterService(&_Ingester_serviceDesc, srv)
}

func _Ingester_TransferChunks_Handler(srv interface{}, stream grpc.ServerStream) error {
	return srv.(IngesterServer).TransferChunks(&ingesterTransferChunksServer{stream})
}

type Ingester_TransferChunksServer interface {
	SendAndClose(*TransferChunksResponse) error
	Recv() (*TimeSeriesChunk, error)
	grpc.ServerStream
}

type ingesterTransferChunksServer struct {
	grpc.ServerStream
}

func (x *ingesterTransferChunksServer) SendAndClose(m *TransferChunksResponse) error {
	return x.ServerStream.SendMsg(m)
}

func (x *ingesterTransferChunksServer) Recv() (*TimeSeriesChunk, error) {
	m := new(TimeSeriesChunk)
	if err := x.ServerStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

var _Ingester_serviceDesc = grpc.ServiceDesc{
	ServiceName: "logproto.Ingester",
	HandlerType: (*IngesterServer)(nil),
	Methods:     []grpc.MethodDesc{},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "TransferChunks",
			Handler:       _Ingester_TransferChunks_Handler,
			ClientStreams: true,
		},
	},
	Metadata: "pkg/logproto/logproto.proto",
}

func (m *PushRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PushRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *PushRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Streams) > 0 {
		for iNdEx := len(m.Streams) - 1; iNdEx >= 0; iNdEx-- {
			{
				size := m.Streams[iNdEx].Size()
				i -= size
				if _, err := m.Streams[iNdEx].MarshalTo(dAtA[i:]); err != nil {
					return 0, err
				}
				i = encodeVarintLogproto(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *PushResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PushResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *PushResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	return len(dAtA) - i, nil
}

func (m *QueryRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *QueryRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *QueryRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Deletes) > 0 {
		for iNdEx := len(m.Deletes) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Deletes[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintLogproto(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x42
		}
	}
	if len(m.Shards) > 0 {
		for iNdEx := len(m.Shards) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.Shards[iNdEx])
			copy(dAtA[i:], m.Shards[iNdEx])
			i = encodeVarintLogproto(dAtA, i, uint64(len(m.Shards[iNdEx])))
			i--
			dAtA[i] = 0x3a
		}
	}
	if m.Direction != 0 {
		i = encodeVarintLogproto(dAtA, i, uint64(m.Direction))
		i--
		dAtA[i] = 0x28
	}
	n1, err1 := github_com_gogo_protobuf_types.StdTimeMarshalTo(m.End, dAtA[i-github_com_gogo_protobuf_types.SizeOfStdTime(m.End):])
	if err1 != nil {
		return 0, err1
	}
	i -= n1
	i = encodeVarintLogproto(dAtA, i, uint64(n1))
	i--
	dAtA[i] = 0x22
	n2, err2 := github_com_gogo_protobuf_types.StdTimeMarshalTo(m.Start, dAtA[i-github_com_gogo_protobuf_types.SizeOfStdTime(m.Start):])
	if err2 != nil {
		return 0, err2
	}
	i -= n2
	i = encodeVarintLogproto(dAtA, i, uint64(n2))
	i--
	dAtA[i] = 0x1a
	if m.Limit != 0 {
		i = encodeVarintLogproto(dAtA, i, uint64(m.Limit))
		i--
		dAtA[i] = 0x10
	}
	if len(m.Selector) > 0 {
		i -= len(m.Selector)
		copy(dAtA[i:], m.Selector)
		i = encodeVarintLogproto(dAtA, i, uint64(len(m.Selector)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *SampleQueryRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SampleQueryRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *SampleQueryRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Deletes) > 0 {
		for iNdEx := len(m.Deletes) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Deletes[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintLogproto(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x2a
		}
	}
	if len(m.Shards) > 0 {
		for iNdEx := len(m.Shards) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.Shards[iNdEx])
			copy(dAtA[i:], m.Shards[iNdEx])
			i = encodeVarintLogproto(dAtA, i, uint64(len(m.Shards[iNdEx])))
			i--
			dAtA[i] = 0x22
		}
	}
	n3, err3 := github_com_gogo_protobuf_types.StdTimeMarshalTo(m.End, dAtA[i-github_com_gogo_protobuf_types.SizeOfStdTime(m.End):])
	if err3 != nil {
		return 0, err3
	}
	i -= n3
	i = encodeVarintLogproto(dAtA, i, uint64(n3))
	i--
	dAtA[i] = 0x1a
	n4, err4 := github_com_gogo_protobuf_types.StdTimeMarshalTo(m.Start, dAtA[i-github_com_gogo_protobuf_types.SizeOfStdTime(m.Start):])
	if err4 != nil {
		return 0, err4
	}
	i -= n4
	i = encodeVarintLogproto(dAtA, i, uint64(n4))
	i--
	dAtA[i] = 0x12
	if len(m.Selector) > 0 {
		i -= len(m.Selector)
		copy(dAtA[i:], m.Selector)
		i = encodeVarintLogproto(dAtA, i, uint64(len(m.Selector)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *Delete) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Delete) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Delete) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.End != 0 {
		i = encodeVarintLogproto(dAtA, i, uint64(m.End))
		i--
		dAtA[i] = 0x18
	}
	if m.Start != 0 {
		i = encodeVarintLogproto(dAtA, i, uint64(m.Start))
		i--
		dAtA[i] = 0x10
	}
	if len(m.Selector) > 0 {
		i -= len(m.Selector)
		copy(dAtA[i:], m.Selector)
		i = encodeVarintLogproto(dAtA, i, uint64(len(m.Selector)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *QueryResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *QueryResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *QueryResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	{
		size, err := m.Stats.MarshalToSizedBuffer(dAtA[:i])
		if err != nil {
			return 0, err
		}
		i -= size
		i = encodeVarintLogproto(dAtA, i, uint64(size))
	}
	i--
	dAtA[i] = 0x12
	if len(m.Streams) > 0 {
		for iNdEx := len(m.Streams) - 1; iNdEx >= 0; iNdEx-- {
			{
				size := m.Streams[iNdEx].Size()
				i -= size
				if _, err := m.Streams[iNdEx].MarshalTo(dAtA[i:]); err != nil {
					return 0, err
				}
				i = encodeVarintLogproto(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *SampleQueryResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SampleQueryResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *SampleQueryResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	{
		size, err := m.Stats.MarshalToSizedBuffer(dAtA[:i])
		if err != nil {
			return 0, err
		}
		i -= size
		i = encodeVarintLogproto(dAtA, i, uint64(size))
	}
	i--
	dAtA[i] = 0x12
	if len(m.Series) > 0 {
		for iNdEx := len(m.Series) - 1; iNdEx >= 0; iNdEx-- {
			{
				size := m.Series[iNdEx].Size()
				i -= size
				if _, err := m.Series[iNdEx].MarshalTo(dAtA[i:]); err != nil {
					return 0, err
				}
				i = encodeVarintLogproto(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *LabelRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *LabelRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *LabelRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.End != nil {
		n7, err7 := github_com_gogo_protobuf_types.StdTimeMarshalTo(*m.End, dAtA[i-github_com_gogo_protobuf_types.SizeOfStdTime(*m.End):])
		if err7 != nil {
			return 0, err7
		}
		i -= n7
		i = encodeVarintLogproto(dAtA, i, uint64(n7))
		i--
		dAtA[i] = 0x22
	}
	if m.Start != nil {
		n8, err8 := github_com_gogo_protobuf_types.StdTimeMarshalTo(*m.Start, dAtA[i-github_com_gogo_protobuf_types.SizeOfStdTime(*m.Start):])
		if err8 != nil {
			return 0, err8
		}
		i -= n8
		i = encodeVarintLogproto(dAtA, i, uint64(n8))
		i--
		dAtA[i] = 0x1a
	}
	if m.Values {
		i--
		if m.Values {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x10
	}
	if len(m.Name) > 0 {
		i -= len(m.Name)
		copy(dAtA[i:], m.Name)
		i = encodeVarintLogproto(dAtA, i, uint64(len(m.Name)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *LabelResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *LabelResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *LabelResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Values) > 0 {
		for iNdEx := len(m.Values) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.Values[iNdEx])
			copy(dAtA[i:], m.Values[iNdEx])
			i = encodeVarintLogproto(dAtA, i, uint64(len(m.Values[iNdEx])))
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *StreamAdapter) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *StreamAdapter) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *StreamAdapter) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Hash != 0 {
		i = encodeVarintLogproto(dAtA, i, uint64(m.Hash))
		i--
		dAtA[i] = 0x18
	}
	if len(m.Entries) > 0 {
		for iNdEx := len(m.Entries) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Entries[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintLogproto(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x12
		}
	}
	if len(m.Labels) > 0 {
		i -= len(m.Labels)
		copy(dAtA[i:], m.Labels)
		i = encodeVarintLogproto(dAtA, i, uint64(len(m.Labels)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *EntryAdapter) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *EntryAdapter) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *EntryAdapter) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Line) > 0 {
		i -= len(m.Line)
		copy(dAtA[i:], m.Line)
		i = encodeVarintLogproto(dAtA, i, uint64(len(m.Line)))
		i--
		dAtA[i] = 0x12
	}
	n9, err9 := github_com_gogo_protobuf_types.StdTimeMarshalTo(m.Timestamp, dAtA[i-github_com_gogo_protobuf_types.SizeOfStdTime(m.Timestamp):])
	if err9 != nil {
		return 0, err9
	}
	i -= n9
	i = encodeVarintLogproto(dAtA, i, uint64(n9))
	i--
	dAtA[i] = 0xa
	return len(dAtA) - i, nil
}

func (m *Sample) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Sample) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Sample) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Hash != 0 {
		i = encodeVarintLogproto(dAtA, i, uint64(m.Hash))
		i--
		dAtA[i] = 0x18
	}
	if m.Value != 0 {
		i -= 8
		encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(math.Float64bits(float64(m.Value))))
		i--
		dAtA[i] = 0x11
	}
	if m.Timestamp != 0 {
		i = encodeVarintLogproto(dAtA, i, uint64(m.Timestamp))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *LegacySample) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *LegacySample) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *LegacySample) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.TimestampMs != 0 {
		i = encodeVarintLogproto(dAtA, i, uint64(m.TimestampMs))
		i--
		dAtA[i] = 0x10
	}
	if m.Value != 0 {
		i -= 8
		encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(math.Float64bits(float64(m.Value))))
		i--
		dAtA[i] = 0x9
	}
	return len(dAtA) - i, nil
}

func (m *Series) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Series) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Series) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.StreamHash != 0 {
		i = encodeVarintLogproto(dAtA, i, uint64(m.StreamHash))
		i--
		dAtA[i] = 0x18
	}
	if len(m.Samples) > 0 {
		for iNdEx := len(m.Samples) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Samples[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintLogproto(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x12
		}
	}
	if len(m.Labels) > 0 {
		i -= len(m.Labels)
		copy(dAtA[i:], m.Labels)
		i = encodeVarintLogproto(dAtA, i, uint64(len(m.Labels)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *TailRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TailRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *TailRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	n10, err10 := github_com_gogo_protobuf_types.StdTimeMarshalTo(m.Start, dAtA[i-github_com_gogo_protobuf_types.SizeOfStdTime(m.Start):])
	if err10 != nil {
		return 0, err10
	}
	i -= n10
	i = encodeVarintLogproto(dAtA, i, uint64(n10))
	i--
	dAtA[i] = 0x2a
	if m.Limit != 0 {
		i = encodeVarintLogproto(dAtA, i, uint64(m.Limit))
		i--
		dAtA[i] = 0x20
	}
	if m.DelayFor != 0 {
		i = encodeVarintLogproto(dAtA, i, uint64(m.DelayFor))
		i--
		dAtA[i] = 0x18
	}
	if len(m.Query) > 0 {
		i -= len(m.Query)
		copy(dAtA[i:], m.Query)
		i = encodeVarintLogproto(dAtA, i, uint64(len(m.Query)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *TailResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TailResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *TailResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.DroppedStreams) > 0 {
		for iNdEx := len(m.DroppedStreams) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.DroppedStreams[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintLogproto(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x12
		}
	}
	if m.Stream != nil {
		{
			size := m.Stream.Size()
			i -= size
			if _, err := m.Stream.MarshalTo(dAtA[i:]); err != nil {
				return 0, err
			}
			i = encodeVarintLogproto(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *SeriesRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SeriesRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *SeriesRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Shards) > 0 {
		for iNdEx := len(m.Shards) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.Shards[iNdEx])
			copy(dAtA[i:], m.Shards[iNdEx])
			i = encodeVarintLogproto(dAtA, i, uint64(len(m.Shards[iNdEx])))
			i--
			dAtA[i] = 0x22
		}
	}
	if len(m.Groups) > 0 {
		for iNdEx := len(m.Groups) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.Groups[iNdEx])
			copy(dAtA[i:], m.Groups[iNdEx])
			i = encodeVarintLogproto(dAtA, i, uint64(len(m.Groups[iNdEx])))
			i--
			dAtA[i] = 0x1a
		}
	}
	n12, err12 := github_com_gogo_protobuf_types.StdTimeMarshalTo(m.End, dAtA[i-github_com_gogo_protobuf_types.SizeOfStdTime(m.End):])
	if err12 != nil {
		return 0, err12
	}
	i -= n12
	i = encodeVarintLogproto(dAtA, i, uint64(n12))
	i--
	dAtA[i] = 0x12
	n13, err13 := github_com_gogo_protobuf_types.StdTimeMarshalTo(m.Start, dAtA[i-github_com_gogo_protobuf_types.SizeOfStdTime(m.Start):])
	if err13 != nil {
		return 0, err13
	}
	i -= n13
	i = encodeVarintLogproto(dAtA, i, uint64(n13))
	i--
	dAtA[i] = 0xa
	return len(dAtA) - i, nil
}

func (m *SeriesResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SeriesResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *SeriesResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Series) > 0 {
		for iNdEx := len(m.Series) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Series[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintLogproto(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *SeriesIdentifier) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SeriesIdentifier) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *SeriesIdentifier) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Labels) > 0 {
		for k := range m.Labels {
			v := m.Labels[k]
			baseI := i
			i -= len(v)
			copy(dAtA[i:], v)
			i = encodeVarintLogproto(dAtA, i, uint64(len(v)))
			i--
			dAtA[i] = 0x12
			i -= len(k)
			copy(dAtA[i:], k)
			i = encodeVarintLogproto(dAtA, i, uint64(len(k)))
			i--
			dAtA[i] = 0xa
			i = encodeVarintLogproto(dAtA, i, uint64(baseI-i))
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *DroppedStream) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DroppedStream) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DroppedStream) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Labels) > 0 {
		i -= len(m.Labels)
		copy(dAtA[i:], m.Labels)
		i = encodeVarintLogproto(dAtA, i, uint64(len(m.Labels)))
		i--
		dAtA[i] = 0x1a
	}
	n14, err14 := github_com_gogo_protobuf_types.StdTimeMarshalTo(m.To, dAtA[i-github_com_gogo_protobuf_types.SizeOfStdTime(m.To):])
	if err14 != nil {
		return 0, err14
	}
	i -= n14
	i = encodeVarintLogproto(dAtA, i, uint64(n14))
	i--
	dAtA[i] = 0x12
	n15, err15 := github_com_gogo_protobuf_types.StdTimeMarshalTo(m.From, dAtA[i-github_com_gogo_protobuf_types.SizeOfStdTime(m.From):])
	if err15 != nil {
		return 0, err15
	}
	i -= n15
	i = encodeVarintLogproto(dAtA, i, uint64(n15))
	i--
	dAtA[i] = 0xa
	return len(dAtA) - i, nil
}

func (m *TimeSeriesChunk) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TimeSeriesChunk) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *TimeSeriesChunk) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Chunks) > 0 {
		for iNdEx := len(m.Chunks) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Chunks[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintLogproto(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x22
		}
	}
	if len(m.Labels) > 0 {
		for iNdEx := len(m.Labels) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Labels[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintLogproto(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x1a
		}
	}
	if len(m.UserId) > 0 {
		i -= len(m.UserId)
		copy(dAtA[i:], m.UserId)
		i = encodeVarintLogproto(dAtA, i, uint64(len(m.UserId)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.FromIngesterId) > 0 {
		i -= len(m.FromIngesterId)
		copy(dAtA[i:], m.FromIngesterId)
		i = encodeVarintLogproto(dAtA, i, uint64(len(m.FromIngesterId)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *LabelPair) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *LabelPair) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *LabelPair) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Value) > 0 {
		i -= len(m.Value)
		copy(dAtA[i:], m.Value)
		i = encodeVarintLogproto(dAtA, i, uint64(len(m.Value)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.Name) > 0 {
		i -= len(m.Name)
		copy(dAtA[i:], m.Name)
		i = encodeVarintLogproto(dAtA, i, uint64(len(m.Name)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *LegacyLabelPair) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *LegacyLabelPair) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *LegacyLabelPair) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Value) > 0 {
		i -= len(m.Value)
		copy(dAtA[i:], m.Value)
		i = encodeVarintLogproto(dAtA, i, uint64(len(m.Value)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.Name) > 0 {
		i -= len(m.Name)
		copy(dAtA[i:], m.Name)
		i = encodeVarintLogproto(dAtA, i, uint64(len(m.Name)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *Chunk) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Chunk) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Chunk) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Data) > 0 {
		i -= len(m.Data)
		copy(dAtA[i:], m.Data)
		i = encodeVarintLogproto(dAtA, i, uint64(len(m.Data)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *TransferChunksResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TransferChunksResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *TransferChunksResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	return len(dAtA) - i, nil
}

func (m *TailersCountRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TailersCountRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *TailersCountRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	return len(dAtA) - i, nil
}

func (m *TailersCountResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TailersCountResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *TailersCountResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Count != 0 {
		i = encodeVarintLogproto(dAtA, i, uint64(m.Count))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *GetChunkIDsRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetChunkIDsRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GetChunkIDsRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	n16, err16 := github_com_gogo_protobuf_types.StdTimeMarshalTo(m.End, dAtA[i-github_com_gogo_protobuf_types.SizeOfStdTime(m.End):])
	if err16 != nil {
		return 0, err16
	}
	i -= n16
	i = encodeVarintLogproto(dAtA, i, uint64(n16))
	i--
	dAtA[i] = 0x1a
	n17, err17 := github_com_gogo_protobuf_types.StdTimeMarshalTo(m.Start, dAtA[i-github_com_gogo_protobuf_types.SizeOfStdTime(m.Start):])
	if err17 != nil {
		return 0, err17
	}
	i -= n17
	i = encodeVarintLogproto(dAtA, i, uint64(n17))
	i--
	dAtA[i] = 0x12
	if len(m.Matchers) > 0 {
		i -= len(m.Matchers)
		copy(dAtA[i:], m.Matchers)
		i = encodeVarintLogproto(dAtA, i, uint64(len(m.Matchers)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *GetChunkIDsResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetChunkIDsResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GetChunkIDsResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.ChunkIDs) > 0 {
		for iNdEx := len(m.ChunkIDs) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.ChunkIDs[iNdEx])
			copy(dAtA[i:], m.ChunkIDs[iNdEx])
			i = encodeVarintLogproto(dAtA, i, uint64(len(m.ChunkIDs[iNdEx])))
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *ChunkRef) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ChunkRef) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ChunkRef) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Checksum != 0 {
		i = encodeVarintLogproto(dAtA, i, uint64(m.Checksum))
		i--
		dAtA[i] = 0x28
	}
	if m.Through != 0 {
		i = encodeVarintLogproto(dAtA, i, uint64(m.Through))
		i--
		dAtA[i] = 0x20
	}
	if m.From != 0 {
		i = encodeVarintLogproto(dAtA, i, uint64(m.From))
		i--
		dAtA[i] = 0x18
	}
	if len(m.UserID) > 0 {
		i -= len(m.UserID)
		copy(dAtA[i:], m.UserID)
		i = encodeVarintLogproto(dAtA, i, uint64(len(m.UserID)))
		i--
		dAtA[i] = 0x12
	}
	if m.Fingerprint != 0 {
		i = encodeVarintLogproto(dAtA, i, uint64(m.Fingerprint))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func encodeVarintLogproto(dAtA []byte, offset int, v uint64) int {
	offset -= sovLogproto(v)
	base := offset
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return base
}
func (m *PushRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Streams) > 0 {
		for _, e := range m.Streams {
			l = e.Size()
			n += 1 + l + sovLogproto(uint64(l))
		}
	}
	return n
}

func (m *PushResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	return n
}

func (m *QueryRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Selector)
	if l > 0 {
		n += 1 + l + sovLogproto(uint64(l))
	}
	if m.Limit != 0 {
		n += 1 + sovLogproto(uint64(m.Limit))
	}
	l = github_com_gogo_protobuf_types.SizeOfStdTime(m.Start)
	n += 1 + l + sovLogproto(uint64(l))
	l = github_com_gogo_protobuf_types.SizeOfStdTime(m.End)
	n += 1 + l + sovLogproto(uint64(l))
	if m.Direction != 0 {
		n += 1 + sovLogproto(uint64(m.Direction))
	}
	if len(m.Shards) > 0 {
		for _, s := range m.Shards {
			l = len(s)
			n += 1 + l + sovLogproto(uint64(l))
		}
	}
	if len(m.Deletes) > 0 {
		for _, e := range m.Deletes {
			l = e.Size()
			n += 1 + l + sovLogproto(uint64(l))
		}
	}
	return n
}

func (m *SampleQueryRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Selector)
	if l > 0 {
		n += 1 + l + sovLogproto(uint64(l))
	}
	l = github_com_gogo_protobuf_types.SizeOfStdTime(m.Start)
	n += 1 + l + sovLogproto(uint64(l))
	l = github_com_gogo_protobuf_types.SizeOfStdTime(m.End)
	n += 1 + l + sovLogproto(uint64(l))
	if len(m.Shards) > 0 {
		for _, s := range m.Shards {
			l = len(s)
			n += 1 + l + sovLogproto(uint64(l))
		}
	}
	if len(m.Deletes) > 0 {
		for _, e := range m.Deletes {
			l = e.Size()
			n += 1 + l + sovLogproto(uint64(l))
		}
	}
	return n
}

func (m *Delete) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Selector)
	if l > 0 {
		n += 1 + l + sovLogproto(uint64(l))
	}
	if m.Start != 0 {
		n += 1 + sovLogproto(uint64(m.Start))
	}
	if m.End != 0 {
		n += 1 + sovLogproto(uint64(m.End))
	}
	return n
}

func (m *QueryResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Streams) > 0 {
		for _, e := range m.Streams {
			l = e.Size()
			n += 1 + l + sovLogproto(uint64(l))
		}
	}
	l = m.Stats.Size()
	n += 1 + l + sovLogproto(uint64(l))
	return n
}

func (m *SampleQueryResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Series) > 0 {
		for _, e := range m.Series {
			l = e.Size()
			n += 1 + l + sovLogproto(uint64(l))
		}
	}
	l = m.Stats.Size()
	n += 1 + l + sovLogproto(uint64(l))
	return n
}

func (m *LabelRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Name)
	if l > 0 {
		n += 1 + l + sovLogproto(uint64(l))
	}
	if m.Values {
		n += 2
	}
	if m.Start != nil {
		l = github_com_gogo_protobuf_types.SizeOfStdTime(*m.Start)
		n += 1 + l + sovLogproto(uint64(l))
	}
	if m.End != nil {
		l = github_com_gogo_protobuf_types.SizeOfStdTime(*m.End)
		n += 1 + l + sovLogproto(uint64(l))
	}
	return n
}

func (m *LabelResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Values) > 0 {
		for _, s := range m.Values {
			l = len(s)
			n += 1 + l + sovLogproto(uint64(l))
		}
	}
	return n
}

func (m *StreamAdapter) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Labels)
	if l > 0 {
		n += 1 + l + sovLogproto(uint64(l))
	}
	if len(m.Entries) > 0 {
		for _, e := range m.Entries {
			l = e.Size()
			n += 1 + l + sovLogproto(uint64(l))
		}
	}
	if m.Hash != 0 {
		n += 1 + sovLogproto(uint64(m.Hash))
	}
	return n
}

func (m *EntryAdapter) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = github_com_gogo_protobuf_types.SizeOfStdTime(m.Timestamp)
	n += 1 + l + sovLogproto(uint64(l))
	l = len(m.Line)
	if l > 0 {
		n += 1 + l + sovLogproto(uint64(l))
	}
	return n
}

func (m *Sample) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Timestamp != 0 {
		n += 1 + sovLogproto(uint64(m.Timestamp))
	}
	if m.Value != 0 {
		n += 9
	}
	if m.Hash != 0 {
		n += 1 + sovLogproto(uint64(m.Hash))
	}
	return n
}

func (m *LegacySample) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Value != 0 {
		n += 9
	}
	if m.TimestampMs != 0 {
		n += 1 + sovLogproto(uint64(m.TimestampMs))
	}
	return n
}

func (m *Series) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Labels)
	if l > 0 {
		n += 1 + l + sovLogproto(uint64(l))
	}
	if len(m.Samples) > 0 {
		for _, e := range m.Samples {
			l = e.Size()
			n += 1 + l + sovLogproto(uint64(l))
		}
	}
	if m.StreamHash != 0 {
		n += 1 + sovLogproto(uint64(m.StreamHash))
	}
	return n
}

func (m *TailRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Query)
	if l > 0 {
		n += 1 + l + sovLogproto(uint64(l))
	}
	if m.DelayFor != 0 {
		n += 1 + sovLogproto(uint64(m.DelayFor))
	}
	if m.Limit != 0 {
		n += 1 + sovLogproto(uint64(m.Limit))
	}
	l = github_com_gogo_protobuf_types.SizeOfStdTime(m.Start)
	n += 1 + l + sovLogproto(uint64(l))
	return n
}

func (m *TailResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Stream != nil {
		l = m.Stream.Size()
		n += 1 + l + sovLogproto(uint64(l))
	}
	if len(m.DroppedStreams) > 0 {
		for _, e := range m.DroppedStreams {
			l = e.Size()
			n += 1 + l + sovLogproto(uint64(l))
		}
	}
	return n
}

func (m *SeriesRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = github_com_gogo_protobuf_types.SizeOfStdTime(m.Start)
	n += 1 + l + sovLogproto(uint64(l))
	l = github_com_gogo_protobuf_types.SizeOfStdTime(m.End)
	n += 1 + l + sovLogproto(uint64(l))
	if len(m.Groups) > 0 {
		for _, s := range m.Groups {
			l = len(s)
			n += 1 + l + sovLogproto(uint64(l))
		}
	}
	if len(m.Shards) > 0 {
		for _, s := range m.Shards {
			l = len(s)
			n += 1 + l + sovLogproto(uint64(l))
		}
	}
	return n
}

func (m *SeriesResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Series) > 0 {
		for _, e := range m.Series {
			l = e.Size()
			n += 1 + l + sovLogproto(uint64(l))
		}
	}
	return n
}

func (m *SeriesIdentifier) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Labels) > 0 {
		for k, v := range m.Labels {
			_ = k
			_ = v
			mapEntrySize := 1 + len(k) + sovLogproto(uint64(len(k))) + 1 + len(v) + sovLogproto(uint64(len(v)))
			n += mapEntrySize + 1 + sovLogproto(uint64(mapEntrySize))
		}
	}
	return n
}

func (m *DroppedStream) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = github_com_gogo_protobuf_types.SizeOfStdTime(m.From)
	n += 1 + l + sovLogproto(uint64(l))
	l = github_com_gogo_protobuf_types.SizeOfStdTime(m.To)
	n += 1 + l + sovLogproto(uint64(l))
	l = len(m.Labels)
	if l > 0 {
		n += 1 + l + sovLogproto(uint64(l))
	}
	return n
}

func (m *TimeSeriesChunk) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.FromIngesterId)
	if l > 0 {
		n += 1 + l + sovLogproto(uint64(l))
	}
	l = len(m.UserId)
	if l > 0 {
		n += 1 + l + sovLogproto(uint64(l))
	}
	if len(m.Labels) > 0 {
		for _, e := range m.Labels {
			l = e.Size()
			n += 1 + l + sovLogproto(uint64(l))
		}
	}
	if len(m.Chunks) > 0 {
		for _, e := range m.Chunks {
			l = e.Size()
			n += 1 + l + sovLogproto(uint64(l))
		}
	}
	return n
}

func (m *LabelPair) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Name)
	if l > 0 {
		n += 1 + l + sovLogproto(uint64(l))
	}
	l = len(m.Value)
	if l > 0 {
		n += 1 + l + sovLogproto(uint64(l))
	}
	return n
}

func (m *LegacyLabelPair) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Name)
	if l > 0 {
		n += 1 + l + sovLogproto(uint64(l))
	}
	l = len(m.Value)
	if l > 0 {
		n += 1 + l + sovLogproto(uint64(l))
	}
	return n
}

func (m *Chunk) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Data)
	if l > 0 {
		n += 1 + l + sovLogproto(uint64(l))
	}
	return n
}

func (m *TransferChunksResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	return n
}

func (m *TailersCountRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	return n
}

func (m *TailersCountResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Count != 0 {
		n += 1 + sovLogproto(uint64(m.Count))
	}
	return n
}

func (m *GetChunkIDsRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Matchers)
	if l > 0 {
		n += 1 + l + sovLogproto(uint64(l))
	}
	l = github_com_gogo_protobuf_types.SizeOfStdTime(m.Start)
	n += 1 + l + sovLogproto(uint64(l))
	l = github_com_gogo_protobuf_types.SizeOfStdTime(m.End)
	n += 1 + l + sovLogproto(uint64(l))
	return n
}

func (m *GetChunkIDsResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.ChunkIDs) > 0 {
		for _, s := range m.ChunkIDs {
			l = len(s)
			n += 1 + l + sovLogproto(uint64(l))
		}
	}
	return n
}

func (m *ChunkRef) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Fingerprint != 0 {
		n += 1 + sovLogproto(uint64(m.Fingerprint))
	}
	l = len(m.UserID)
	if l > 0 {
		n += 1 + l + sovLogproto(uint64(l))
	}
	if m.From != 0 {
		n += 1 + sovLogproto(uint64(m.From))
	}
	if m.Through != 0 {
		n += 1 + sovLogproto(uint64(m.Through))
	}
	if m.Checksum != 0 {
		n += 1 + sovLogproto(uint64(m.Checksum))
	}
	return n
}

func sovLogproto(x uint64) (n int) {
	return (math_bits.Len64(x|1) + 6) / 7
}
func sozLogproto(x uint64) (n int) {
	return sovLogproto(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (this *PushRequest) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&PushRequest{`,
		`Streams:` + fmt.Sprintf("%v", this.Streams) + `,`,
		`}`,
	}, "")
	return s
}
func (this *PushResponse) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&PushResponse{`,
		`}`,
	}, "")
	return s
}
func (this *QueryRequest) String() string {
	if this == nil {
		return "nil"
	}
	repeatedStringForDeletes := "[]*Delete{"
	for _, f := range this.Deletes {
		repeatedStringForDeletes += strings.Replace(f.String(), "Delete", "Delete", 1) + ","
	}
	repeatedStringForDeletes += "}"
	s := strings.Join([]string{`&QueryRequest{`,
		`Selector:` + fmt.Sprintf("%v", this.Selector) + `,`,
		`Limit:` + fmt.Sprintf("%v", this.Limit) + `,`,
		`Start:` + strings.Replace(strings.Replace(fmt.Sprintf("%v", this.Start), "Timestamp", "types.Timestamp", 1), `&`, ``, 1) + `,`,
		`End:` + strings.Replace(strings.Replace(fmt.Sprintf("%v", this.End), "Timestamp", "types.Timestamp", 1), `&`, ``, 1) + `,`,
		`Direction:` + fmt.Sprintf("%v", this.Direction) + `,`,
		`Shards:` + fmt.Sprintf("%v", this.Shards) + `,`,
		`Deletes:` + repeatedStringForDeletes + `,`,
		`}`,
	}, "")
	return s
}
func (this *SampleQueryRequest) String() string {
	if this == nil {
		return "nil"
	}
	repeatedStringForDeletes := "[]*Delete{"
	for _, f := range this.Deletes {
		repeatedStringForDeletes += strings.Replace(f.String(), "Delete", "Delete", 1) + ","
	}
	repeatedStringForDeletes += "}"
	s := strings.Join([]string{`&SampleQueryRequest{`,
		`Selector:` + fmt.Sprintf("%v", this.Selector) + `,`,
		`Start:` + strings.Replace(strings.Replace(fmt.Sprintf("%v", this.Start), "Timestamp", "types.Timestamp", 1), `&`, ``, 1) + `,`,
		`End:` + strings.Replace(strings.Replace(fmt.Sprintf("%v", this.End), "Timestamp", "types.Timestamp", 1), `&`, ``, 1) + `,`,
		`Shards:` + fmt.Sprintf("%v", this.Shards) + `,`,
		`Deletes:` + repeatedStringForDeletes + `,`,
		`}`,
	}, "")
	return s
}
func (this *Delete) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&Delete{`,
		`Selector:` + fmt.Sprintf("%v", this.Selector) + `,`,
		`Start:` + fmt.Sprintf("%v", this.Start) + `,`,
		`End:` + fmt.Sprintf("%v", this.End) + `,`,
		`}`,
	}, "")
	return s
}
func (this *QueryResponse) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&QueryResponse{`,
		`Streams:` + fmt.Sprintf("%v", this.Streams) + `,`,
		`Stats:` + strings.Replace(strings.Replace(fmt.Sprintf("%v", this.Stats), "Ingester", "stats.Ingester", 1), `&`, ``, 1) + `,`,
		`}`,
	}, "")
	return s
}
func (this *SampleQueryResponse) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&SampleQueryResponse{`,
		`Series:` + fmt.Sprintf("%v", this.Series) + `,`,
		`Stats:` + strings.Replace(strings.Replace(fmt.Sprintf("%v", this.Stats), "Ingester", "stats.Ingester", 1), `&`, ``, 1) + `,`,
		`}`,
	}, "")
	return s
}
func (this *LabelRequest) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&LabelRequest{`,
		`Name:` + fmt.Sprintf("%v", this.Name) + `,`,
		`Values:` + fmt.Sprintf("%v", this.Values) + `,`,
		`Start:` + strings.Replace(fmt.Sprintf("%v", this.Start), "Timestamp", "types.Timestamp", 1) + `,`,
		`End:` + strings.Replace(fmt.Sprintf("%v", this.End), "Timestamp", "types.Timestamp", 1) + `,`,
		`}`,
	}, "")
	return s
}
func (this *LabelResponse) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&LabelResponse{`,
		`Values:` + fmt.Sprintf("%v", this.Values) + `,`,
		`}`,
	}, "")
	return s
}
func (this *StreamAdapter) String() string {
	if this == nil {
		return "nil"
	}
	repeatedStringForEntries := "[]EntryAdapter{"
	for _, f := range this.Entries {
		repeatedStringForEntries += strings.Replace(strings.Replace(f.String(), "EntryAdapter", "EntryAdapter", 1), `&`, ``, 1) + ","
	}
	repeatedStringForEntries += "}"
	s := strings.Join([]string{`&StreamAdapter{`,
		`Labels:` + fmt.Sprintf("%v", this.Labels) + `,`,
		`Entries:` + repeatedStringForEntries + `,`,
		`Hash:` + fmt.Sprintf("%v", this.Hash) + `,`,
		`}`,
	}, "")
	return s
}
func (this *EntryAdapter) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&EntryAdapter{`,
		`Timestamp:` + strings.Replace(strings.Replace(fmt.Sprintf("%v", this.Timestamp), "Timestamp", "types.Timestamp", 1), `&`, ``, 1) + `,`,
		`Line:` + fmt.Sprintf("%v", this.Line) + `,`,
		`}`,
	}, "")
	return s
}
func (this *Sample) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&Sample{`,
		`Timestamp:` + fmt.Sprintf("%v", this.Timestamp) + `,`,
		`Value:` + fmt.Sprintf("%v", this.Value) + `,`,
		`Hash:` + fmt.Sprintf("%v", this.Hash) + `,`,
		`}`,
	}, "")
	return s
}
func (this *LegacySample) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&LegacySample{`,
		`Value:` + fmt.Sprintf("%v", this.Value) + `,`,
		`TimestampMs:` + fmt.Sprintf("%v", this.TimestampMs) + `,`,
		`}`,
	}, "")
	return s
}
func (this *Series) String() string {
	if this == nil {
		return "nil"
	}
	repeatedStringForSamples := "[]Sample{"
	for _, f := range this.Samples {
		repeatedStringForSamples += strings.Replace(strings.Replace(f.String(), "Sample", "Sample", 1), `&`, ``, 1) + ","
	}
	repeatedStringForSamples += "}"
	s := strings.Join([]string{`&Series{`,
		`Labels:` + fmt.Sprintf("%v", this.Labels) + `,`,
		`Samples:` + repeatedStringForSamples + `,`,
		`StreamHash:` + fmt.Sprintf("%v", this.StreamHash) + `,`,
		`}`,
	}, "")
	return s
}
func (this *TailRequest) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&TailRequest{`,
		`Query:` + fmt.Sprintf("%v", this.Query) + `,`,
		`DelayFor:` + fmt.Sprintf("%v", this.DelayFor) + `,`,
		`Limit:` + fmt.Sprintf("%v", this.Limit) + `,`,
		`Start:` + strings.Replace(strings.Replace(fmt.Sprintf("%v", this.Start), "Timestamp", "types.Timestamp", 1), `&`, ``, 1) + `,`,
		`}`,
	}, "")
	return s
}
func (this *TailResponse) String() string {
	if this == nil {
		return "nil"
	}
	repeatedStringForDroppedStreams := "[]*DroppedStream{"
	for _, f := range this.DroppedStreams {
		repeatedStringForDroppedStreams += strings.Replace(f.String(), "DroppedStream", "DroppedStream", 1) + ","
	}
	repeatedStringForDroppedStreams += "}"
	s := strings.Join([]string{`&TailResponse{`,
		`Stream:` + fmt.Sprintf("%v", this.Stream) + `,`,
		`DroppedStreams:` + repeatedStringForDroppedStreams + `,`,
		`}`,
	}, "")
	return s
}
func (this *SeriesRequest) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&SeriesRequest{`,
		`Start:` + strings.Replace(strings.Replace(fmt.Sprintf("%v", this.Start), "Timestamp", "types.Timestamp", 1), `&`, ``, 1) + `,`,
		`End:` + strings.Replace(strings.Replace(fmt.Sprintf("%v", this.End), "Timestamp", "types.Timestamp", 1), `&`, ``, 1) + `,`,
		`Groups:` + fmt.Sprintf("%v", this.Groups) + `,`,
		`Shards:` + fmt.Sprintf("%v", this.Shards) + `,`,
		`}`,
	}, "")
	return s
}
func (this *SeriesResponse) String() string {
	if this == nil {
		return "nil"
	}
	repeatedStringForSeries := "[]SeriesIdentifier{"
	for _, f := range this.Series {
		repeatedStringForSeries += strings.Replace(strings.Replace(f.String(), "SeriesIdentifier", "SeriesIdentifier", 1), `&`, ``, 1) + ","
	}
	repeatedStringForSeries += "}"
	s := strings.Join([]string{`&SeriesResponse{`,
		`Series:` + repeatedStringForSeries + `,`,
		`}`,
	}, "")
	return s
}
func (this *SeriesIdentifier) String() string {
	if this == nil {
		return "nil"
	}
	keysForLabels := make([]string, 0, len(this.Labels))
	for k, _ := range this.Labels {
		keysForLabels = append(keysForLabels, k)
	}
	github_com_gogo_protobuf_sortkeys.Strings(keysForLabels)
	mapStringForLabels := "map[string]string{"
	for _, k := range keysForLabels {
		mapStringForLabels += fmt.Sprintf("%v: %v,", k, this.Labels[k])
	}
	mapStringForLabels += "}"
	s := strings.Join([]string{`&SeriesIdentifier{`,
		`Labels:` + mapStringForLabels + `,`,
		`}`,
	}, "")
	return s
}
func (this *DroppedStream) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&DroppedStream{`,
		`From:` + strings.Replace(strings.Replace(fmt.Sprintf("%v", this.From), "Timestamp", "types.Timestamp", 1), `&`, ``, 1) + `,`,
		`To:` + strings.Replace(strings.Replace(fmt.Sprintf("%v", this.To), "Timestamp", "types.Timestamp", 1), `&`, ``, 1) + `,`,
		`Labels:` + fmt.Sprintf("%v", this.Labels) + `,`,
		`}`,
	}, "")
	return s
}
func (this *TimeSeriesChunk) String() string {
	if this == nil {
		return "nil"
	}
	repeatedStringForLabels := "[]*LabelPair{"
	for _, f := range this.Labels {
		repeatedStringForLabels += strings.Replace(f.String(), "LabelPair", "LabelPair", 1) + ","
	}
	repeatedStringForLabels += "}"
	repeatedStringForChunks := "[]*Chunk{"
	for _, f := range this.Chunks {
		repeatedStringForChunks += strings.Replace(f.String(), "Chunk", "Chunk", 1) + ","
	}
	repeatedStringForChunks += "}"
	s := strings.Join([]string{`&TimeSeriesChunk{`,
		`FromIngesterId:` + fmt.Sprintf("%v", this.FromIngesterId) + `,`,
		`UserId:` + fmt.Sprintf("%v", this.UserId) + `,`,
		`Labels:` + repeatedStringForLabels + `,`,
		`Chunks:` + repeatedStringForChunks + `,`,
		`}`,
	}, "")
	return s
}
func (this *LabelPair) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&LabelPair{`,
		`Name:` + fmt.Sprintf("%v", this.Name) + `,`,
		`Value:` + fmt.Sprintf("%v", this.Value) + `,`,
		`}`,
	}, "")
	return s
}
func (this *LegacyLabelPair) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&LegacyLabelPair{`,
		`Name:` + fmt.Sprintf("%v", this.Name) + `,`,
		`Value:` + fmt.Sprintf("%v", this.Value) + `,`,
		`}`,
	}, "")
	return s
}
func (this *Chunk) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&Chunk{`,
		`Data:` + fmt.Sprintf("%v", this.Data) + `,`,
		`}`,
	}, "")
	return s
}
func (this *TransferChunksResponse) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&TransferChunksResponse{`,
		`}`,
	}, "")
	return s
}
func (this *TailersCountRequest) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&TailersCountRequest{`,
		`}`,
	}, "")
	return s
}
func (this *TailersCountResponse) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&TailersCountResponse{`,
		`Count:` + fmt.Sprintf("%v", this.Count) + `,`,
		`}`,
	}, "")
	return s
}
func (this *GetChunkIDsRequest) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&GetChunkIDsRequest{`,
		`Matchers:` + fmt.Sprintf("%v", this.Matchers) + `,`,
		`Start:` + strings.Replace(strings.Replace(fmt.Sprintf("%v", this.Start), "Timestamp", "types.Timestamp", 1), `&`, ``, 1) + `,`,
		`End:` + strings.Replace(strings.Replace(fmt.Sprintf("%v", this.End), "Timestamp", "types.Timestamp", 1), `&`, ``, 1) + `,`,
		`}`,
	}, "")
	return s
}
func (this *GetChunkIDsResponse) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&GetChunkIDsResponse{`,
		`ChunkIDs:` + fmt.Sprintf("%v", this.ChunkIDs) + `,`,
		`}`,
	}, "")
	return s
}
func (this *ChunkRef) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&ChunkRef{`,
		`Fingerprint:` + fmt.Sprintf("%v", this.Fingerprint) + `,`,
		`UserID:` + fmt.Sprintf("%v", this.UserID) + `,`,
		`From:` + fmt.Sprintf("%v", this.From) + `,`,
		`Through:` + fmt.Sprintf("%v", this.Through) + `,`,
		`Checksum:` + fmt.Sprintf("%v", this.Checksum) + `,`,
		`}`,
	}, "")
	return s
}
func valueToStringLogproto(v interface{}) string {
	rv := reflect.ValueOf(v)
	if rv.IsNil() {
		return "nil"
	}
	pv := reflect.Indirect(rv).Interface()
	return fmt.Sprintf("*%v", pv)
}
func (m *PushRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowLogproto
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: PushRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: PushRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Streams", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLogproto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthLogproto
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthLogproto
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Streams = append(m.Streams, Stream{})
			if err := m.Streams[len(m.Streams)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipLogproto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthLogproto
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthLogproto
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *PushResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowLogproto
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: PushResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: PushResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipLogproto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthLogproto
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthLogproto
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *QueryRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowLogproto
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: QueryRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: QueryRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Selector", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLogproto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthLogproto
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthLogproto
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Selector = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Limit", wireType)
			}
			m.Limit = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLogproto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Limit |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Start", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLogproto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthLogproto
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthLogproto
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := github_com_gogo_protobuf_types.StdTimeUnmarshal(&m.Start, dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field End", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLogproto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthLogproto
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthLogproto
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := github_com_gogo_protobuf_types.StdTimeUnmarshal(&m.End, dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Direction", wireType)
			}
			m.Direction = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLogproto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Direction |= Direction(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Shards", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLogproto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthLogproto
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthLogproto
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Shards = append(m.Shards, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 8:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Deletes", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLogproto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthLogproto
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthLogproto
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Deletes = append(m.Deletes, &Delete{})
			if err := m.Deletes[len(m.Deletes)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipLogproto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthLogproto
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthLogproto
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *SampleQueryRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowLogproto
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: SampleQueryRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: SampleQueryRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Selector", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLogproto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthLogproto
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthLogproto
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Selector = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Start", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLogproto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthLogproto
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthLogproto
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := github_com_gogo_protobuf_types.StdTimeUnmarshal(&m.Start, dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field End", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLogproto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthLogproto
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthLogproto
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := github_com_gogo_protobuf_types.StdTimeUnmarshal(&m.End, dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Shards", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLogproto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthLogproto
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthLogproto
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Shards = append(m.Shards, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Deletes", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLogproto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthLogproto
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthLogproto
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Deletes = append(m.Deletes, &Delete{})
			if err := m.Deletes[len(m.Deletes)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipLogproto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthLogproto
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthLogproto
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Delete) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowLogproto
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Delete: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Delete: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Selector", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLogproto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthLogproto
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthLogproto
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Selector = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Start", wireType)
			}
			m.Start = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLogproto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Start |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field End", wireType)
			}
			m.End = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLogproto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.End |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipLogproto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthLogproto
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthLogproto
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *QueryResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowLogproto
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: QueryResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: QueryResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Streams", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLogproto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthLogproto
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthLogproto
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Streams = append(m.Streams, Stream{})
			if err := m.Streams[len(m.Streams)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Stats", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLogproto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthLogproto
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthLogproto
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := m.Stats.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipLogproto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthLogproto
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthLogproto
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *SampleQueryResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowLogproto
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: SampleQueryResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: SampleQueryResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Series", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLogproto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthLogproto
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthLogproto
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Series = append(m.Series, Series{})
			if err := m.Series[len(m.Series)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Stats", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLogproto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthLogproto
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthLogproto
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := m.Stats.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipLogproto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthLogproto
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthLogproto
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *LabelRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowLogproto
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: LabelRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: LabelRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLogproto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthLogproto
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthLogproto
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Values", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLogproto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Values = bool(v != 0)
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Start", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLogproto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthLogproto
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthLogproto
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Start == nil {
				m.Start = new(time.Time)
			}
			if err := github_com_gogo_protobuf_types.StdTimeUnmarshal(m.Start, dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field End", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLogproto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthLogproto
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthLogproto
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.End == nil {
				m.End = new(time.Time)
			}
			if err := github_com_gogo_protobuf_types.StdTimeUnmarshal(m.End, dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipLogproto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthLogproto
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthLogproto
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *LabelResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowLogproto
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: LabelResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: LabelResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Values", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLogproto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthLogproto
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthLogproto
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Values = append(m.Values, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipLogproto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthLogproto
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthLogproto
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *StreamAdapter) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowLogproto
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: StreamAdapter: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: StreamAdapter: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Labels", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLogproto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthLogproto
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthLogproto
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Labels = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Entries", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLogproto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthLogproto
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthLogproto
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Entries = append(m.Entries, EntryAdapter{})
			if err := m.Entries[len(m.Entries)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Hash", wireType)
			}
			m.Hash = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLogproto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Hash |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipLogproto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthLogproto
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthLogproto
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *EntryAdapter) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowLogproto
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: EntryAdapter: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: EntryAdapter: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Timestamp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLogproto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthLogproto
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthLogproto
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := github_com_gogo_protobuf_types.StdTimeUnmarshal(&m.Timestamp, dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Line", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLogproto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthLogproto
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthLogproto
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Line = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipLogproto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthLogproto
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthLogproto
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Sample) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowLogproto
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Sample: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Sample: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Timestamp", wireType)
			}
			m.Timestamp = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLogproto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Timestamp |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 1 {
				return fmt.Errorf("proto: wrong wireType = %d for field Value", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io.ErrUnexpectedEOF
			}
			v = uint64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
			iNdEx += 8
			m.Value = float64(math.Float64frombits(v))
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Hash", wireType)
			}
			m.Hash = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLogproto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Hash |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipLogproto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthLogproto
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthLogproto
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *LegacySample) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowLogproto
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: LegacySample: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: LegacySample: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 1 {
				return fmt.Errorf("proto: wrong wireType = %d for field Value", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io.ErrUnexpectedEOF
			}
			v = uint64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
			iNdEx += 8
			m.Value = float64(math.Float64frombits(v))
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field TimestampMs", wireType)
			}
			m.TimestampMs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLogproto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TimestampMs |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipLogproto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthLogproto
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthLogproto
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Series) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowLogproto
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Series: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Series: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Labels", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLogproto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthLogproto
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthLogproto
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Labels = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Samples", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLogproto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthLogproto
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthLogproto
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Samples = append(m.Samples, Sample{})
			if err := m.Samples[len(m.Samples)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field StreamHash", wireType)
			}
			m.StreamHash = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLogproto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StreamHash |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipLogproto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthLogproto
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthLogproto
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *TailRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowLogproto
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: TailRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: TailRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Query", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLogproto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthLogproto
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthLogproto
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Query = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field DelayFor", wireType)
			}
			m.DelayFor = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLogproto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DelayFor |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Limit", wireType)
			}
			m.Limit = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLogproto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Limit |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Start", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLogproto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthLogproto
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthLogproto
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := github_com_gogo_protobuf_types.StdTimeUnmarshal(&m.Start, dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipLogproto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthLogproto
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthLogproto
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *TailResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowLogproto
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: TailResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: TailResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Stream", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLogproto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthLogproto
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthLogproto
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Stream == nil {
				m.Stream = &Stream{}
			}
			if err := m.Stream.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field DroppedStreams", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLogproto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthLogproto
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthLogproto
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.DroppedStreams = append(m.DroppedStreams, &DroppedStream{})
			if err := m.DroppedStreams[len(m.DroppedStreams)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipLogproto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthLogproto
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthLogproto
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *SeriesRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowLogproto
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: SeriesRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: SeriesRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Start", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLogproto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthLogproto
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthLogproto
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := github_com_gogo_protobuf_types.StdTimeUnmarshal(&m.Start, dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field End", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLogproto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthLogproto
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthLogproto
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := github_com_gogo_protobuf_types.StdTimeUnmarshal(&m.End, dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Groups", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLogproto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthLogproto
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthLogproto
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Groups = append(m.Groups, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Shards", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLogproto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthLogproto
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthLogproto
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Shards = append(m.Shards, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipLogproto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthLogproto
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthLogproto
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *SeriesResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowLogproto
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: SeriesResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: SeriesResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Series", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLogproto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthLogproto
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthLogproto
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Series = append(m.Series, SeriesIdentifier{})
			if err := m.Series[len(m.Series)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipLogproto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthLogproto
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthLogproto
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *SeriesIdentifier) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowLogproto
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: SeriesIdentifier: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: SeriesIdentifier: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Labels", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLogproto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthLogproto
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthLogproto
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Labels == nil {
				m.Labels = make(map[string]string)
			}
			var mapkey string
			var mapvalue string
			for iNdEx < postIndex {
				entryPreIndex := iNdEx
				var wire uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowLogproto
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					wire |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				fieldNum := int32(wire >> 3)
				if fieldNum == 1 {
					var stringLenmapkey uint64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowLogproto
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						stringLenmapkey |= uint64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					intStringLenmapkey := int(stringLenmapkey)
					if intStringLenmapkey < 0 {
						return ErrInvalidLengthLogproto
					}
					postStringIndexmapkey := iNdEx + intStringLenmapkey
					if postStringIndexmapkey < 0 {
						return ErrInvalidLengthLogproto
					}
					if postStringIndexmapkey > l {
						return io.ErrUnexpectedEOF
					}
					mapkey = string(dAtA[iNdEx:postStringIndexmapkey])
					iNdEx = postStringIndexmapkey
				} else if fieldNum == 2 {
					var stringLenmapvalue uint64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowLogproto
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						stringLenmapvalue |= uint64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					intStringLenmapvalue := int(stringLenmapvalue)
					if intStringLenmapvalue < 0 {
						return ErrInvalidLengthLogproto
					}
					postStringIndexmapvalue := iNdEx + intStringLenmapvalue
					if postStringIndexmapvalue < 0 {
						return ErrInvalidLengthLogproto
					}
					if postStringIndexmapvalue > l {
						return io.ErrUnexpectedEOF
					}
					mapvalue = string(dAtA[iNdEx:postStringIndexmapvalue])
					iNdEx = postStringIndexmapvalue
				} else {
					iNdEx = entryPreIndex
					skippy, err := skipLogproto(dAtA[iNdEx:])
					if err != nil {
						return err
					}
					if skippy < 0 {
						return ErrInvalidLengthLogproto
					}
					if (iNdEx + skippy) > postIndex {
						return io.ErrUnexpectedEOF
					}
					iNdEx += skippy
				}
			}
			m.Labels[mapkey] = mapvalue
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipLogproto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthLogproto
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthLogproto
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DroppedStream) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowLogproto
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: DroppedStream: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: DroppedStream: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field From", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLogproto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthLogproto
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthLogproto
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := github_com_gogo_protobuf_types.StdTimeUnmarshal(&m.From, dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field To", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLogproto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthLogproto
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthLogproto
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := github_com_gogo_protobuf_types.StdTimeUnmarshal(&m.To, dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Labels", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLogproto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthLogproto
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthLogproto
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Labels = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipLogproto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthLogproto
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthLogproto
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *TimeSeriesChunk) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowLogproto
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: TimeSeriesChunk: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: TimeSeriesChunk: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field FromIngesterId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLogproto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthLogproto
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthLogproto
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.FromIngesterId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field UserId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLogproto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthLogproto
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthLogproto
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.UserId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Labels", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLogproto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthLogproto
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthLogproto
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Labels = append(m.Labels, &LabelPair{})
			if err := m.Labels[len(m.Labels)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Chunks", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLogproto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthLogproto
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthLogproto
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Chunks = append(m.Chunks, &Chunk{})
			if err := m.Chunks[len(m.Chunks)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipLogproto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthLogproto
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthLogproto
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *LabelPair) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowLogproto
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: LabelPair: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: LabelPair: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLogproto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthLogproto
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthLogproto
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Value", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLogproto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthLogproto
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthLogproto
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Value = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipLogproto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthLogproto
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthLogproto
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *LegacyLabelPair) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowLogproto
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: LegacyLabelPair: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: LegacyLabelPair: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLogproto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthLogproto
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthLogproto
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Name = append(m.Name[:0], dAtA[iNdEx:postIndex]...)
			if m.Name == nil {
				m.Name = []byte{}
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Value", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLogproto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthLogproto
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthLogproto
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Value = append(m.Value[:0], dAtA[iNdEx:postIndex]...)
			if m.Value == nil {
				m.Value = []byte{}
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipLogproto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthLogproto
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthLogproto
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Chunk) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowLogproto
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Chunk: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Chunk: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Data", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLogproto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthLogproto
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthLogproto
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Data = append(m.Data[:0], dAtA[iNdEx:postIndex]...)
			if m.Data == nil {
				m.Data = []byte{}
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipLogproto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthLogproto
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthLogproto
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *TransferChunksResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowLogproto
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: TransferChunksResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: TransferChunksResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipLogproto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthLogproto
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthLogproto
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *TailersCountRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowLogproto
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: TailersCountRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: TailersCountRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipLogproto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthLogproto
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthLogproto
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *TailersCountResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowLogproto
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: TailersCountResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: TailersCountResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Count", wireType)
			}
			m.Count = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLogproto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Count |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipLogproto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthLogproto
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthLogproto
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetChunkIDsRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowLogproto
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetChunkIDsRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetChunkIDsRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Matchers", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLogproto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthLogproto
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthLogproto
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Matchers = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Start", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLogproto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthLogproto
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthLogproto
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := github_com_gogo_protobuf_types.StdTimeUnmarshal(&m.Start, dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field End", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLogproto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthLogproto
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthLogproto
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := github_com_gogo_protobuf_types.StdTimeUnmarshal(&m.End, dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipLogproto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthLogproto
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthLogproto
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetChunkIDsResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowLogproto
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetChunkIDsResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetChunkIDsResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ChunkIDs", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLogproto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthLogproto
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthLogproto
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ChunkIDs = append(m.ChunkIDs, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipLogproto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthLogproto
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthLogproto
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ChunkRef) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowLogproto
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ChunkRef: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ChunkRef: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Fingerprint", wireType)
			}
			m.Fingerprint = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLogproto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Fingerprint |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field UserID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLogproto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthLogproto
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthLogproto
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.UserID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field From", wireType)
			}
			m.From = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLogproto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.From |= github_com_prometheus_common_model.Time(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Through", wireType)
			}
			m.Through = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLogproto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Through |= github_com_prometheus_common_model.Time(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Checksum", wireType)
			}
			m.Checksum = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLogproto
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Checksum |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipLogproto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthLogproto
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthLogproto
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipLogproto(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowLogproto
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowLogproto
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowLogproto
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if length < 0 {
				return 0, ErrInvalidLengthLogproto
			}
			iNdEx += length
			if iNdEx < 0 {
				return 0, ErrInvalidLengthLogproto
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start int = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowLogproto
					}
					if iNdEx >= l {
						return 0, io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipLogproto(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
				if iNdEx < 0 {
					return 0, ErrInvalidLengthLogproto
				}
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthLogproto = fmt.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowLogproto   = fmt.Errorf("proto: integer overflow")
)
