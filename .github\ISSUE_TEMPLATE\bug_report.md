---
name: Bug report
about: Create a report to help us improve
title: ''
labels: ''
assignees: ''

---

<!-- Please keep the structure below, or your issue may be closed. -->

**Describe the bug**
A clear and concise description of what the bug is.

**To Reproduce**
Steps to reproduce the behavior:
1. Started Loki (SHA or version)
2. Started Promtail (SHA or version) to tail '...'
3. Query: `{} term`

**Expected behavior**
A clear and concise description of what you expected to happen.

**Environment:**
 - Infrastructure: [e.g., Kubernetes, bare-metal, laptop]
 - Deployment tool: [e.g., helm, jsonnet]

**Screenshots, Promtail config, or terminal output**
If applicable, add any output to help explain your problem.
