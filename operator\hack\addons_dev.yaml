# This file is used to create additional objects to help development of the operator
# within a cluster. logcli pod helps write queries, promtail writes logs, etc
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: lokistack-dev-addons-logcli
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: lokistack-dev-addons-promtail
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: lokistack-dev-addons-logcli
spec:
  selector:
    matchLabels:
      app.kubernetes.io/name: logcli
      app.kubernetes.io/instance: developer-addons
  template:
    metadata:
      name: lokistack-dev-addons-logcli
      labels:
        app.kubernetes.io/name: logcli
        app.kubernetes.io/instance: developer-addons
    spec:
      containers:
        - name: logcli
          image: docker.io/grafana/logcli:2.4.2-amd64
          imagePullPolicy: IfNotPresent
          command:
            - /bin/sh
          env:
            - name: LOKI_ORG_ID
              value: application
            - name: LOKI_ADDR
              value: http://lokistack-dev-gateway-http.openshift-logging.svc:8080/api/logs/v1/application
            - name: LOKI_BEARER_TOKEN_FILE
              value: /var/run/secrets/kubernetes.io/serviceaccount/token
          args:
            - -c
            - while true; do logcli query '{job="systemd-journal"}'; sleep 30; done
      serviceAccountName: lokistack-dev-addons-logcli
---
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: lokistack-dev-addons-promtail
spec:
  selector:
    matchLabels:
      app.kubernetes.io/name: promtail
      app.kubernetes.io/instance: developer-addons
  template:
    metadata:
      name: lokistack-dev-addons-promtail
      labels:
        app.kubernetes.io/name: promtail
        app.kubernetes.io/instance: developer-addons
    spec:
      containers:
        - name: promtail
          image: docker.io/grafana/promtail:2.4.2
          args:
            - -config.file=/etc/promtail/promtail.yaml
            - -log.level=info
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
            - mountPath: /etc/promtail
              name: config
            - mountPath: /run/promtail
              name: run
            - mountPath: /var/lib/docker/containers
              name: docker
              readOnly: true
            - mountPath: /var/log/pods
              name: pods
              readOnly: true
            - mountPath: /var/log/journal
              name: journal
              readOnly: true
          securityContext:
            privileged: true
            readOnlyRootFilesystem: true
            runAsGroup: 0
            runAsUser: 0
      serviceAccountName: lokistack-dev-addons-promtail
      volumes:
        - configMap:
            defaultMode: 420
            name: lokistack-dev-addons-promtail
          name: config
        - hostPath:
            path: /run/promtail
            type: ""
          name: run
        - hostPath:
            path: /var/lib/docker/containers
            type: ""
          name: docker
        - hostPath:
            path: /var/log/pods
            type: ""
          name: pods
        - hostPath:
            path: /var/log/journal
            type: ""
          name: journal
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: lokistack-dev-addons-promtail
data:
  promtail.yaml: |
    clients:
      - url: http://lokistack-dev-gateway-http.openshift-logging.svc:8080/api/logs/v1/application/loki/api/v1/push
        tenant_id: application
        bearer_token_file: /var/run/secrets/kubernetes.io/serviceaccount/token
        backoff_config:
          max_period: 5m
          max_retries: 20
          min_period: 1s
        batchsize: 100
        batchwait: 10s
        timeout: 10s
    positions:
      filename: /run/promtail/positions.yaml
    server:
      http_listen_port: 3100
      grpc_listen_port: 9095
    target_config:
      sync_period: 10s
    scrape_configs:
      - job_name: journal
        journal:
          max_age: 12h
          path: /var/log/journal
          labels:
            job: systemd-journal
        relabel_configs:
          - source_labels:
            - __journal__systemd_unit
            target_label: unit
          - source_labels:
            - __journal__hostname
            target_label: hostname
      - job_name: kubernetes-pods-name
        pipeline_stages:
          - docker: {}
        kubernetes_sd_configs:
          - role: pod
        relabel_configs:
          - source_labels:
              - __meta_kubernetes_pod_label_name
            target_label: __service__
          - source_labels:
              - __meta_kubernetes_pod_node_name
            target_label: __host__
          - action: drop
            regex: ^$
            source_labels:
              - __service__
          - action: replace
            replacement: $1
            separator: /
            source_labels:
              - __meta_kubernetes_namespace
              - __service__
            target_label: job
          - action: replace
            source_labels:
              - __meta_kubernetes_namespace
            target_label: namespace
          - action: replace
            source_labels:
              - __meta_kubernetes_pod_name
            target_label: instance
          - action: replace
            source_labels:
              - __meta_kubernetes_pod_container_name
            target_label: container_name
          - action: labelmap
            regex: __meta_kubernetes_pod_label_(.+)
          - replacement: /var/log/pods/*$1/*.log
            separator: /
            source_labels:
                - __meta_kubernetes_pod_uid
                - __meta_kubernetes_pod_container_name
            target_label: __path__
      - job_name: kubernetes-pods-app
        pipeline_stages:
          - docker: {}
        kubernetes_sd_configs:
          - role: pod
        relabel_configs:
          - action: drop
            regex: .+
            source_labels:
              - __meta_kubernetes_pod_label_name
          - source_labels:
              - __meta_kubernetes_pod_label_app
            target_label: __service__
          - source_labels:
              - __meta_kubernetes_pod_node_name
            target_label: __host__
          - action: drop
            regex: ^$
            source_labels:
              - __service__
          - action: replace
            replacement: $1
            separator: /
            source_labels:
              - __meta_kubernetes_namespace
              - __service__
            target_label: job
          - action: replace
            source_labels:
              - __meta_kubernetes_namespace
            target_label: namespace
          - action: replace
            source_labels:
              - __meta_kubernetes_pod_name
            target_label: instance
          - action: replace
            source_labels:
              - __meta_kubernetes_pod_container_name
            target_label: container_name
          - action: labelmap
            regex: __meta_kubernetes_pod_label_(.+)
          - replacement: /var/log/pods/*$1/*.log
            separator: /
            source_labels:
              - __meta_kubernetes_pod_uid
              - __meta_kubernetes_pod_container_name
            target_label: __path__
      - job_name: kubernetes-pods-direct-controllers
        pipeline_stages:
          - docker: {}
        kubernetes_sd_configs:
          - role: pod
        relabel_configs:
          - action: drop
            regex: .+
            separator: ''
            source_labels:
              - __meta_kubernetes_pod_label_name
              - __meta_kubernetes_pod_label_app
          - action: drop
            regex: ^([0-9a-z-.]+)(-[0-9a-f]{8,10})$
            source_labels:
              - __meta_kubernetes_pod_controller_name
          - source_labels:
            - __meta_kubernetes_pod_controller_name
            target_label: __service__
          - source_labels:
            - __meta_kubernetes_pod_node_name
            target_label: __host__
          - action: drop
            regex: ^$
            source_labels:
              - __service__
          - action: replace
            replacement: $1
            separator: /
            source_labels:
              - __meta_kubernetes_namespace
              - __service__
            target_label: job
          - action: replace
            source_labels:
              - __meta_kubernetes_namespace
            target_label: namespace
          - action: replace
            source_labels:
              - __meta_kubernetes_pod_name
            target_label: instance
          - action: replace
            source_labels:
              - __meta_kubernetes_pod_container_name
            target_label:
              container_name
          - action: labelmap
            regex: __meta_kubernetes_pod_label_(.+)
          - replacement: /var/log/pods/*$1/*.log
            separator: /
            source_labels:
              - __meta_kubernetes_pod_uid
              - __meta_kubernetes_pod_container_name
            target_label: __path__
      - job_name: kubernetes-pods-indirect-controller
        pipeline_stages:
          - docker: {}
        kubernetes_sd_configs:
          - role: pod
        relabel_configs:
          - action: drop
            regex: .+
            separator: ''
            source_labels:
              - __meta_kubernetes_pod_label_name
              - __meta_kubernetes_pod_label_app
          - action: keep
            regex: ^([0-9a-z-.]+)(-[0-9a-f]{8,10})$
            source_labels:
              - __meta_kubernetes_pod_controller_name
          - action: replace
            regex: ^([0-9a-z-.]+)(-[0-9a-f]{8,10})$
            source_labels:
              - __meta_kubernetes_pod_controller_name
            target_label: __service__
          - source_labels:
              - __meta_kubernetes_pod_node_name
            target_label: __host__
          - action: drop
            regex: ^$
            source_labels:
              - __service__
          - action: replace
            replacement: $1
            separator: /
            source_labels:
              - __meta_kubernetes_namespace
              - __service__
            target_label: job
          - action: replace
            source_labels:
              - __meta_kubernetes_namespace
            target_label: namespace
          - action: replace
            source_labels:
              - __meta_kubernetes_pod_name
            target_label: instance
          - action: replace
            source_labels:
              - __meta_kubernetes_pod_container_name
            target_label: container_name
          - action: labelmap
            regex: __meta_kubernetes_pod_label_(.+)
          - replacement: /var/log/pods/*$1/*.log
            separator: /
            source_labels:
              - __meta_kubernetes_pod_uid
              - __meta_kubernetes_pod_container_name
            target_label: __path__
      - job_name: kubernetes-pods-static
        pipeline_stages:
          - docker: {}
        kubernetes_sd_configs:
          - role: pod
        relabel_configs:
          - action: drop
            regex: ^$
            source_labels:
              - __meta_kubernetes_pod_annotation_kubernetes_io_config_mirror
          - action: replace
            source_labels:
              - __meta_kubernetes_pod_label_component
            target_label: __service__
          - source_labels:
            - __meta_kubernetes_pod_node_name
            target_label: __host__
          - action: drop
            regex: ^$
            source_labels:
              - __service__
          - action: replace
            replacement: $1
            separator: /
            source_labels:
              - __meta_kubernetes_namespace
              - __service__
            target_label: job
          - action: replace
            source_labels:
              - __meta_kubernetes_namespace
            target_label: namespace
          - action: replace
            source_labels:
              - __meta_kubernetes_pod_name
            target_label: instance
          - action: replace
            source_labels:
              - __meta_kubernetes_pod_container_name
            target_label: container_name
          - action: labelmap
            regex: __meta_kubernetes_pod_label_(.+)
          - replacement: /var/log/pods/*$1/*.log
            separator: /
            source_labels:
              - __meta_kubernetes_pod_annotation_kubernetes_io_config_mirror
              - __meta_kubernetes_pod_container_name
            target_label: __path__
---
apiVersion: policy/v1beta1
kind: PodSecurityPolicy
metadata:
  name: lokistack-dev-addons-policy
spec:
  allowPrivilegeEscalation: false
  fsGroup:
    rule: RunAsAny
  hostIPC: false
  hostNetwork: false
  hostPID: false
  privileged: false
  readOnlyRootFilesystem: true
  requiredDropCapabilities:
  - ALL
  runAsUser:
    rule: RunAsAny
  seLinux:
    rule: RunAsAny
  supplementalGroups:
    rule: RunAsAny
  volumes:
  - secret
  - configMap
  - hostPath
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: lokistack-dev-addons-writer
rules:
- apiGroups:
  - extensions
  resourceNames:
  - lokistack-dev-addons-policy
  resources:
  - podsecuritypolicies
  verbs:
  - use
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: lokistack-dev-addons-writer
  labels:
    app.kubernetes.io/name: promtail
    app.kubernetes.io/instance: developer-addons
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: lokistack-dev-addons-writer
subjects:
- kind: ServiceAccount
  name: lokistack-dev-addons-promtail
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: lokistack-dev-addons-reader
  labels:
    app.kubernetes.io/name: logcli
    app.kubernetes.io/instance: developer-addons
rules:
- apiGroups:
  - loki.grafana.com
  resources:
  - application
  resourceNames:
  - logs
  verbs:
  - get
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: lokistack-dev-addons-writer
  labels:
    app.kubernetes.io/name: promtail
    app.kubernetes.io/instance: developer-addons
rules:
- apiGroups:
  - ""
  resources:
  - nodes
  - nodes/proxy
  - services
  - endpoints
  - pods
  verbs:
  - get
  - watch
  - list
- apiGroups:
  - loki.grafana.com
  resources:
  - application
  resourceNames:
  - logs
  verbs:
  - create
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: lokistack-dev-addons-reader-clusterrolebinding
  labels:
    app.kubernetes.io/name: logcli
    app.kubernetes.io/instance: developer-addons
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: lokistack-dev-addons-reader
subjects:
- kind: ServiceAccount
  name: lokistack-dev-addons-logcli
  namespace: openshift-logging
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: lokistack-dev-addons-writer-clusterrolebinding
  labels:
    app.kubernetes.io/name: promtail
    app.kubernetes.io/instance: developer-addons
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: lokistack-dev-addons-writer
subjects:
- kind: ServiceAccount
  name: lokistack-dev-addons-promtail
  namespace: openshift-logging
