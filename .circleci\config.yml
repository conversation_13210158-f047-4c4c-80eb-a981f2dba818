version: 2

.tags: &tags # tags need to be explicitly defined (whitelist)
  tags: { only: "/.*/" }

.only-tags: &only-tags
  <<: *tags
  branches: { ignore: "/.*/" }

.tag-or-main: &tag-or-main
  branches: { only: main }
  <<: *tags

.no-main: &no-main # contrary to tags, the branches must be excluded
  branches: { ignore: main }

workflows:
  version: 2
  default:
    jobs:
      # publish jobs depend on this as well,
      # thus tags need to be allowed for these
      - test: { filters: { <<: *tags } }

      - build/promtail-windows:
          requires: [test]

      - build/docker-driver:
          requires: [test]
          filters: { <<: *no-main }
      - publish/docker-driver:
          requires: [test]
          filters: { <<: *tag-or-main }

      - publish/binaries:
          requires: [test]
          filters: { <<: *only-tags }


# https://circleci.com/blog/circleci-hacks-reuse-yaml-in-your-circleci-config-with-yaml/
.defaults: &defaults
  docker:
    - image: grafana/loki-build-image:0.19.0
  working_directory: /src/loki

jobs:
  test:
    <<: *defaults
    steps:
      - checkout

  # Promtail
  build/promtail-windows:
    <<: *defaults
    steps:
      - checkout
      - setup_remote_docker
      - run:
          name: build
          command: make GOOS=windows GOGC=10 promtail

  # Docker driver
  build/docker-driver:
    <<: *defaults
    steps:
      - checkout
      - setup_remote_docker
      - run:
          name: docker-driver
          command: make docker-driver

  publish/docker-driver:
    <<: *defaults
    steps:
      - checkout
      - setup_remote_docker
      - run:
          name: login
          command: docker login -u "$DOCKER_USER" -p "$DOCKER_PASS"
      - run:
          name: docker-driver
          command: make docker-driver-push

  publish/binaries:
    <<: *defaults
    steps:
      - checkout
      - run:
          name: github release
          command: make BUILD_IN_CONTAINER=false publish
