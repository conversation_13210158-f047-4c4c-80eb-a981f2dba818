---
title: limit
---
# `limit` stage

The `limit` stage is a rate-limiting stage that throttles logs based on several options. 

## Limit stage schema

This pipeline stage places limits on the rate or burst quantity of log lines that <PERSON><PERSON><PERSON> pushes to <PERSON>.
The concept of having distinct burst and rate limits mirrors the approach to limits that can be set for Loki's distributor component:  `ingestion_rate_mb` and `ingestion_burst_size_mb`, as defined in [limits_config](../../../../configuration/#limits_config).

```yaml
limit:
  # The rate limit in lines per second that <PERSON><PERSON><PERSON> will push to Loki
  [rate: <int>]

  # The cap in the quantity of burst lines that <PERSON><PERSON><PERSON> will push to <PERSON>
  [burst: <int>]

  # When drop is true, log lines that exceed the current rate limit will be discarded.
  # When drop is false, log lines that exceed the current rate limit will only wait
  # to enter the back pressure mode. 
  [drop: <bool> | default = false]
```

## Examples

The following are examples showing the use of the `limit` stage.

### limit

Simple `limit` stage configurations.

#### Match a line and throttle

Given the pipeline:

```yaml
- limit:
    rate: 10
    burst: 10
```

Would throttle any log line.

#### Match a line and drop

Given the pipeline:

```yaml
- limit:
    rate: 10
    burst: 10
    drop: true
```

Would throttle any log line and drop logs when rate limit.
