// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: pkg/ingester/client/ingester.proto

package client

import (
	bytes "bytes"
	context "context"
	encoding_binary "encoding/binary"
	fmt "fmt"
	_ "github.com/gogo/protobuf/gogoproto"
	proto "github.com/gogo/protobuf/proto"
	github_com_grafana_loki_pkg_logproto "github.com/grafana/loki/pkg/logproto"
	logproto "github.com/grafana/loki/pkg/logproto"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	io "io"
	math "math"
	math_bits "math/bits"
	reflect "reflect"
	strconv "strconv"
	strings "strings"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion3 // please upgrade the proto package

type MatchType int32

const (
	EQUAL          MatchType = 0
	NOT_EQUAL      MatchType = 1
	REGEX_MATCH    MatchType = 2
	REGEX_NO_MATCH MatchType = 3
)

var MatchType_name = map[int32]string{
	0: "EQUAL",
	1: "NOT_EQUAL",
	2: "REGEX_MATCH",
	3: "REGEX_NO_MATCH",
}

var MatchType_value = map[string]int32{
	"EQUAL":          0,
	"NOT_EQUAL":      1,
	"REGEX_MATCH":    2,
	"REGEX_NO_MATCH": 3,
}

func (MatchType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_5b6c87318632a5b2, []int{0}
}

type ReadRequest struct {
	Queries []*QueryRequest `protobuf:"bytes,1,rep,name=queries,proto3" json:"queries,omitempty"`
}

func (m *ReadRequest) Reset()      { *m = ReadRequest{} }
func (*ReadRequest) ProtoMessage() {}
func (*ReadRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_5b6c87318632a5b2, []int{0}
}
func (m *ReadRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ReadRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ReadRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ReadRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReadRequest.Merge(m, src)
}
func (m *ReadRequest) XXX_Size() int {
	return m.Size()
}
func (m *ReadRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ReadRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ReadRequest proto.InternalMessageInfo

func (m *ReadRequest) GetQueries() []*QueryRequest {
	if m != nil {
		return m.Queries
	}
	return nil
}

type ReadResponse struct {
	Results []*QueryResponse `protobuf:"bytes,1,rep,name=results,proto3" json:"results,omitempty"`
}

func (m *ReadResponse) Reset()      { *m = ReadResponse{} }
func (*ReadResponse) ProtoMessage() {}
func (*ReadResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_5b6c87318632a5b2, []int{1}
}
func (m *ReadResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ReadResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ReadResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ReadResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReadResponse.Merge(m, src)
}
func (m *ReadResponse) XXX_Size() int {
	return m.Size()
}
func (m *ReadResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ReadResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ReadResponse proto.InternalMessageInfo

func (m *ReadResponse) GetResults() []*QueryResponse {
	if m != nil {
		return m.Results
	}
	return nil
}

type QueryRequest struct {
	StartTimestampMs int64           `protobuf:"varint,1,opt,name=start_timestamp_ms,json=startTimestampMs,proto3" json:"start_timestamp_ms,omitempty"`
	EndTimestampMs   int64           `protobuf:"varint,2,opt,name=end_timestamp_ms,json=endTimestampMs,proto3" json:"end_timestamp_ms,omitempty"`
	Matchers         []*LabelMatcher `protobuf:"bytes,3,rep,name=matchers,proto3" json:"matchers,omitempty"`
}

func (m *QueryRequest) Reset()      { *m = QueryRequest{} }
func (*QueryRequest) ProtoMessage() {}
func (*QueryRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_5b6c87318632a5b2, []int{2}
}
func (m *QueryRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *QueryRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_QueryRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *QueryRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryRequest.Merge(m, src)
}
func (m *QueryRequest) XXX_Size() int {
	return m.Size()
}
func (m *QueryRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryRequest.DiscardUnknown(m)
}

var xxx_messageInfo_QueryRequest proto.InternalMessageInfo

func (m *QueryRequest) GetStartTimestampMs() int64 {
	if m != nil {
		return m.StartTimestampMs
	}
	return 0
}

func (m *QueryRequest) GetEndTimestampMs() int64 {
	if m != nil {
		return m.EndTimestampMs
	}
	return 0
}

func (m *QueryRequest) GetMatchers() []*LabelMatcher {
	if m != nil {
		return m.Matchers
	}
	return nil
}

type ExemplarQueryRequest struct {
	StartTimestampMs int64            `protobuf:"varint,1,opt,name=start_timestamp_ms,json=startTimestampMs,proto3" json:"start_timestamp_ms,omitempty"`
	EndTimestampMs   int64            `protobuf:"varint,2,opt,name=end_timestamp_ms,json=endTimestampMs,proto3" json:"end_timestamp_ms,omitempty"`
	Matchers         []*LabelMatchers `protobuf:"bytes,3,rep,name=matchers,proto3" json:"matchers,omitempty"`
}

func (m *ExemplarQueryRequest) Reset()      { *m = ExemplarQueryRequest{} }
func (*ExemplarQueryRequest) ProtoMessage() {}
func (*ExemplarQueryRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_5b6c87318632a5b2, []int{3}
}
func (m *ExemplarQueryRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ExemplarQueryRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ExemplarQueryRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ExemplarQueryRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExemplarQueryRequest.Merge(m, src)
}
func (m *ExemplarQueryRequest) XXX_Size() int {
	return m.Size()
}
func (m *ExemplarQueryRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ExemplarQueryRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ExemplarQueryRequest proto.InternalMessageInfo

func (m *ExemplarQueryRequest) GetStartTimestampMs() int64 {
	if m != nil {
		return m.StartTimestampMs
	}
	return 0
}

func (m *ExemplarQueryRequest) GetEndTimestampMs() int64 {
	if m != nil {
		return m.EndTimestampMs
	}
	return 0
}

func (m *ExemplarQueryRequest) GetMatchers() []*LabelMatchers {
	if m != nil {
		return m.Matchers
	}
	return nil
}

type QueryResponse struct {
	Timeseries []logproto.TimeSeries `protobuf:"bytes,1,rep,name=timeseries,proto3" json:"timeseries"`
}

func (m *QueryResponse) Reset()      { *m = QueryResponse{} }
func (*QueryResponse) ProtoMessage() {}
func (*QueryResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_5b6c87318632a5b2, []int{4}
}
func (m *QueryResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *QueryResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_QueryResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *QueryResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryResponse.Merge(m, src)
}
func (m *QueryResponse) XXX_Size() int {
	return m.Size()
}
func (m *QueryResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryResponse.DiscardUnknown(m)
}

var xxx_messageInfo_QueryResponse proto.InternalMessageInfo

func (m *QueryResponse) GetTimeseries() []logproto.TimeSeries {
	if m != nil {
		return m.Timeseries
	}
	return nil
}

// QueryStreamResponse contains a batch of timeseries chunks or timeseries. Only one of these series will be populated.
type QueryStreamResponse struct {
	Chunkseries []TimeSeriesChunk     `protobuf:"bytes,1,rep,name=chunkseries,proto3" json:"chunkseries"`
	Timeseries  []logproto.TimeSeries `protobuf:"bytes,2,rep,name=timeseries,proto3" json:"timeseries"`
}

func (m *QueryStreamResponse) Reset()      { *m = QueryStreamResponse{} }
func (*QueryStreamResponse) ProtoMessage() {}
func (*QueryStreamResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_5b6c87318632a5b2, []int{5}
}
func (m *QueryStreamResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *QueryStreamResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_QueryStreamResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *QueryStreamResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryStreamResponse.Merge(m, src)
}
func (m *QueryStreamResponse) XXX_Size() int {
	return m.Size()
}
func (m *QueryStreamResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryStreamResponse.DiscardUnknown(m)
}

var xxx_messageInfo_QueryStreamResponse proto.InternalMessageInfo

func (m *QueryStreamResponse) GetChunkseries() []TimeSeriesChunk {
	if m != nil {
		return m.Chunkseries
	}
	return nil
}

func (m *QueryStreamResponse) GetTimeseries() []logproto.TimeSeries {
	if m != nil {
		return m.Timeseries
	}
	return nil
}

type ExemplarQueryResponse struct {
	Timeseries []logproto.TimeSeries `protobuf:"bytes,1,rep,name=timeseries,proto3" json:"timeseries"`
}

func (m *ExemplarQueryResponse) Reset()      { *m = ExemplarQueryResponse{} }
func (*ExemplarQueryResponse) ProtoMessage() {}
func (*ExemplarQueryResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_5b6c87318632a5b2, []int{6}
}
func (m *ExemplarQueryResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ExemplarQueryResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ExemplarQueryResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ExemplarQueryResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExemplarQueryResponse.Merge(m, src)
}
func (m *ExemplarQueryResponse) XXX_Size() int {
	return m.Size()
}
func (m *ExemplarQueryResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ExemplarQueryResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ExemplarQueryResponse proto.InternalMessageInfo

func (m *ExemplarQueryResponse) GetTimeseries() []logproto.TimeSeries {
	if m != nil {
		return m.Timeseries
	}
	return nil
}

type LabelValuesRequest struct {
	LabelName        string         `protobuf:"bytes,1,opt,name=label_name,json=labelName,proto3" json:"label_name,omitempty"`
	StartTimestampMs int64          `protobuf:"varint,2,opt,name=start_timestamp_ms,json=startTimestampMs,proto3" json:"start_timestamp_ms,omitempty"`
	EndTimestampMs   int64          `protobuf:"varint,3,opt,name=end_timestamp_ms,json=endTimestampMs,proto3" json:"end_timestamp_ms,omitempty"`
	Matchers         *LabelMatchers `protobuf:"bytes,4,opt,name=matchers,proto3" json:"matchers,omitempty"`
}

func (m *LabelValuesRequest) Reset()      { *m = LabelValuesRequest{} }
func (*LabelValuesRequest) ProtoMessage() {}
func (*LabelValuesRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_5b6c87318632a5b2, []int{7}
}
func (m *LabelValuesRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *LabelValuesRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_LabelValuesRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *LabelValuesRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LabelValuesRequest.Merge(m, src)
}
func (m *LabelValuesRequest) XXX_Size() int {
	return m.Size()
}
func (m *LabelValuesRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_LabelValuesRequest.DiscardUnknown(m)
}

var xxx_messageInfo_LabelValuesRequest proto.InternalMessageInfo

func (m *LabelValuesRequest) GetLabelName() string {
	if m != nil {
		return m.LabelName
	}
	return ""
}

func (m *LabelValuesRequest) GetStartTimestampMs() int64 {
	if m != nil {
		return m.StartTimestampMs
	}
	return 0
}

func (m *LabelValuesRequest) GetEndTimestampMs() int64 {
	if m != nil {
		return m.EndTimestampMs
	}
	return 0
}

func (m *LabelValuesRequest) GetMatchers() *LabelMatchers {
	if m != nil {
		return m.Matchers
	}
	return nil
}

type LabelValuesResponse struct {
	LabelValues []string `protobuf:"bytes,1,rep,name=label_values,json=labelValues,proto3" json:"label_values,omitempty"`
}

func (m *LabelValuesResponse) Reset()      { *m = LabelValuesResponse{} }
func (*LabelValuesResponse) ProtoMessage() {}
func (*LabelValuesResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_5b6c87318632a5b2, []int{8}
}
func (m *LabelValuesResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *LabelValuesResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_LabelValuesResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *LabelValuesResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LabelValuesResponse.Merge(m, src)
}
func (m *LabelValuesResponse) XXX_Size() int {
	return m.Size()
}
func (m *LabelValuesResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_LabelValuesResponse.DiscardUnknown(m)
}

var xxx_messageInfo_LabelValuesResponse proto.InternalMessageInfo

func (m *LabelValuesResponse) GetLabelValues() []string {
	if m != nil {
		return m.LabelValues
	}
	return nil
}

type LabelNamesRequest struct {
	StartTimestampMs int64 `protobuf:"varint,1,opt,name=start_timestamp_ms,json=startTimestampMs,proto3" json:"start_timestamp_ms,omitempty"`
	EndTimestampMs   int64 `protobuf:"varint,2,opt,name=end_timestamp_ms,json=endTimestampMs,proto3" json:"end_timestamp_ms,omitempty"`
}

func (m *LabelNamesRequest) Reset()      { *m = LabelNamesRequest{} }
func (*LabelNamesRequest) ProtoMessage() {}
func (*LabelNamesRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_5b6c87318632a5b2, []int{9}
}
func (m *LabelNamesRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *LabelNamesRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_LabelNamesRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *LabelNamesRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LabelNamesRequest.Merge(m, src)
}
func (m *LabelNamesRequest) XXX_Size() int {
	return m.Size()
}
func (m *LabelNamesRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_LabelNamesRequest.DiscardUnknown(m)
}

var xxx_messageInfo_LabelNamesRequest proto.InternalMessageInfo

func (m *LabelNamesRequest) GetStartTimestampMs() int64 {
	if m != nil {
		return m.StartTimestampMs
	}
	return 0
}

func (m *LabelNamesRequest) GetEndTimestampMs() int64 {
	if m != nil {
		return m.EndTimestampMs
	}
	return 0
}

type LabelNamesResponse struct {
	LabelNames []string `protobuf:"bytes,1,rep,name=label_names,json=labelNames,proto3" json:"label_names,omitempty"`
}

func (m *LabelNamesResponse) Reset()      { *m = LabelNamesResponse{} }
func (*LabelNamesResponse) ProtoMessage() {}
func (*LabelNamesResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_5b6c87318632a5b2, []int{10}
}
func (m *LabelNamesResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *LabelNamesResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_LabelNamesResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *LabelNamesResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LabelNamesResponse.Merge(m, src)
}
func (m *LabelNamesResponse) XXX_Size() int {
	return m.Size()
}
func (m *LabelNamesResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_LabelNamesResponse.DiscardUnknown(m)
}

var xxx_messageInfo_LabelNamesResponse proto.InternalMessageInfo

func (m *LabelNamesResponse) GetLabelNames() []string {
	if m != nil {
		return m.LabelNames
	}
	return nil
}

type UserStatsRequest struct {
}

func (m *UserStatsRequest) Reset()      { *m = UserStatsRequest{} }
func (*UserStatsRequest) ProtoMessage() {}
func (*UserStatsRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_5b6c87318632a5b2, []int{11}
}
func (m *UserStatsRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *UserStatsRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_UserStatsRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *UserStatsRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserStatsRequest.Merge(m, src)
}
func (m *UserStatsRequest) XXX_Size() int {
	return m.Size()
}
func (m *UserStatsRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UserStatsRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UserStatsRequest proto.InternalMessageInfo

type UserStatsResponse struct {
	IngestionRate     float64 `protobuf:"fixed64,1,opt,name=ingestion_rate,json=ingestionRate,proto3" json:"ingestion_rate,omitempty"`
	NumSeries         uint64  `protobuf:"varint,2,opt,name=num_series,json=numSeries,proto3" json:"num_series,omitempty"`
	ApiIngestionRate  float64 `protobuf:"fixed64,3,opt,name=api_ingestion_rate,json=apiIngestionRate,proto3" json:"api_ingestion_rate,omitempty"`
	RuleIngestionRate float64 `protobuf:"fixed64,4,opt,name=rule_ingestion_rate,json=ruleIngestionRate,proto3" json:"rule_ingestion_rate,omitempty"`
}

func (m *UserStatsResponse) Reset()      { *m = UserStatsResponse{} }
func (*UserStatsResponse) ProtoMessage() {}
func (*UserStatsResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_5b6c87318632a5b2, []int{12}
}
func (m *UserStatsResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *UserStatsResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_UserStatsResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *UserStatsResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserStatsResponse.Merge(m, src)
}
func (m *UserStatsResponse) XXX_Size() int {
	return m.Size()
}
func (m *UserStatsResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_UserStatsResponse.DiscardUnknown(m)
}

var xxx_messageInfo_UserStatsResponse proto.InternalMessageInfo

func (m *UserStatsResponse) GetIngestionRate() float64 {
	if m != nil {
		return m.IngestionRate
	}
	return 0
}

func (m *UserStatsResponse) GetNumSeries() uint64 {
	if m != nil {
		return m.NumSeries
	}
	return 0
}

func (m *UserStatsResponse) GetApiIngestionRate() float64 {
	if m != nil {
		return m.ApiIngestionRate
	}
	return 0
}

func (m *UserStatsResponse) GetRuleIngestionRate() float64 {
	if m != nil {
		return m.RuleIngestionRate
	}
	return 0
}

type UserIDStatsResponse struct {
	UserId string             `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Data   *UserStatsResponse `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"`
}

func (m *UserIDStatsResponse) Reset()      { *m = UserIDStatsResponse{} }
func (*UserIDStatsResponse) ProtoMessage() {}
func (*UserIDStatsResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_5b6c87318632a5b2, []int{13}
}
func (m *UserIDStatsResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *UserIDStatsResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_UserIDStatsResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *UserIDStatsResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserIDStatsResponse.Merge(m, src)
}
func (m *UserIDStatsResponse) XXX_Size() int {
	return m.Size()
}
func (m *UserIDStatsResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_UserIDStatsResponse.DiscardUnknown(m)
}

var xxx_messageInfo_UserIDStatsResponse proto.InternalMessageInfo

func (m *UserIDStatsResponse) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *UserIDStatsResponse) GetData() *UserStatsResponse {
	if m != nil {
		return m.Data
	}
	return nil
}

type UsersStatsResponse struct {
	Stats []*UserIDStatsResponse `protobuf:"bytes,1,rep,name=stats,proto3" json:"stats,omitempty"`
}

func (m *UsersStatsResponse) Reset()      { *m = UsersStatsResponse{} }
func (*UsersStatsResponse) ProtoMessage() {}
func (*UsersStatsResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_5b6c87318632a5b2, []int{14}
}
func (m *UsersStatsResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *UsersStatsResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_UsersStatsResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *UsersStatsResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UsersStatsResponse.Merge(m, src)
}
func (m *UsersStatsResponse) XXX_Size() int {
	return m.Size()
}
func (m *UsersStatsResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_UsersStatsResponse.DiscardUnknown(m)
}

var xxx_messageInfo_UsersStatsResponse proto.InternalMessageInfo

func (m *UsersStatsResponse) GetStats() []*UserIDStatsResponse {
	if m != nil {
		return m.Stats
	}
	return nil
}

type MetricsForLabelMatchersRequest struct {
	StartTimestampMs int64            `protobuf:"varint,1,opt,name=start_timestamp_ms,json=startTimestampMs,proto3" json:"start_timestamp_ms,omitempty"`
	EndTimestampMs   int64            `protobuf:"varint,2,opt,name=end_timestamp_ms,json=endTimestampMs,proto3" json:"end_timestamp_ms,omitempty"`
	MatchersSet      []*LabelMatchers `protobuf:"bytes,3,rep,name=matchers_set,json=matchersSet,proto3" json:"matchers_set,omitempty"`
}

func (m *MetricsForLabelMatchersRequest) Reset()      { *m = MetricsForLabelMatchersRequest{} }
func (*MetricsForLabelMatchersRequest) ProtoMessage() {}
func (*MetricsForLabelMatchersRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_5b6c87318632a5b2, []int{15}
}
func (m *MetricsForLabelMatchersRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *MetricsForLabelMatchersRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_MetricsForLabelMatchersRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *MetricsForLabelMatchersRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MetricsForLabelMatchersRequest.Merge(m, src)
}
func (m *MetricsForLabelMatchersRequest) XXX_Size() int {
	return m.Size()
}
func (m *MetricsForLabelMatchersRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_MetricsForLabelMatchersRequest.DiscardUnknown(m)
}

var xxx_messageInfo_MetricsForLabelMatchersRequest proto.InternalMessageInfo

func (m *MetricsForLabelMatchersRequest) GetStartTimestampMs() int64 {
	if m != nil {
		return m.StartTimestampMs
	}
	return 0
}

func (m *MetricsForLabelMatchersRequest) GetEndTimestampMs() int64 {
	if m != nil {
		return m.EndTimestampMs
	}
	return 0
}

func (m *MetricsForLabelMatchersRequest) GetMatchersSet() []*LabelMatchers {
	if m != nil {
		return m.MatchersSet
	}
	return nil
}

type MetricsForLabelMatchersResponse struct {
	Metric []*logproto.Metric `protobuf:"bytes,1,rep,name=metric,proto3" json:"metric,omitempty"`
}

func (m *MetricsForLabelMatchersResponse) Reset()      { *m = MetricsForLabelMatchersResponse{} }
func (*MetricsForLabelMatchersResponse) ProtoMessage() {}
func (*MetricsForLabelMatchersResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_5b6c87318632a5b2, []int{16}
}
func (m *MetricsForLabelMatchersResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *MetricsForLabelMatchersResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_MetricsForLabelMatchersResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *MetricsForLabelMatchersResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MetricsForLabelMatchersResponse.Merge(m, src)
}
func (m *MetricsForLabelMatchersResponse) XXX_Size() int {
	return m.Size()
}
func (m *MetricsForLabelMatchersResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_MetricsForLabelMatchersResponse.DiscardUnknown(m)
}

var xxx_messageInfo_MetricsForLabelMatchersResponse proto.InternalMessageInfo

func (m *MetricsForLabelMatchersResponse) GetMetric() []*logproto.Metric {
	if m != nil {
		return m.Metric
	}
	return nil
}

type MetricsMetadataRequest struct {
}

func (m *MetricsMetadataRequest) Reset()      { *m = MetricsMetadataRequest{} }
func (*MetricsMetadataRequest) ProtoMessage() {}
func (*MetricsMetadataRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_5b6c87318632a5b2, []int{17}
}
func (m *MetricsMetadataRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *MetricsMetadataRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_MetricsMetadataRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *MetricsMetadataRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MetricsMetadataRequest.Merge(m, src)
}
func (m *MetricsMetadataRequest) XXX_Size() int {
	return m.Size()
}
func (m *MetricsMetadataRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_MetricsMetadataRequest.DiscardUnknown(m)
}

var xxx_messageInfo_MetricsMetadataRequest proto.InternalMessageInfo

type MetricsMetadataResponse struct {
	Metadata []*logproto.MetricMetadata `protobuf:"bytes,1,rep,name=metadata,proto3" json:"metadata,omitempty"`
}

func (m *MetricsMetadataResponse) Reset()      { *m = MetricsMetadataResponse{} }
func (*MetricsMetadataResponse) ProtoMessage() {}
func (*MetricsMetadataResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_5b6c87318632a5b2, []int{18}
}
func (m *MetricsMetadataResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *MetricsMetadataResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_MetricsMetadataResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *MetricsMetadataResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MetricsMetadataResponse.Merge(m, src)
}
func (m *MetricsMetadataResponse) XXX_Size() int {
	return m.Size()
}
func (m *MetricsMetadataResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_MetricsMetadataResponse.DiscardUnknown(m)
}

var xxx_messageInfo_MetricsMetadataResponse proto.InternalMessageInfo

func (m *MetricsMetadataResponse) GetMetadata() []*logproto.MetricMetadata {
	if m != nil {
		return m.Metadata
	}
	return nil
}

type TimeSeriesChunk struct {
	FromIngesterId string                                              `protobuf:"bytes,1,opt,name=from_ingester_id,json=fromIngesterId,proto3" json:"from_ingester_id,omitempty"`
	UserId         string                                              `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Labels         []github_com_grafana_loki_pkg_logproto.LabelAdapter `protobuf:"bytes,3,rep,name=labels,proto3,customtype=github.com/grafana/loki/pkg/logproto.LabelAdapter" json:"labels"`
	Chunks         []Chunk                                             `protobuf:"bytes,4,rep,name=chunks,proto3" json:"chunks"`
}

func (m *TimeSeriesChunk) Reset()      { *m = TimeSeriesChunk{} }
func (*TimeSeriesChunk) ProtoMessage() {}
func (*TimeSeriesChunk) Descriptor() ([]byte, []int) {
	return fileDescriptor_5b6c87318632a5b2, []int{19}
}
func (m *TimeSeriesChunk) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *TimeSeriesChunk) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_TimeSeriesChunk.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *TimeSeriesChunk) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TimeSeriesChunk.Merge(m, src)
}
func (m *TimeSeriesChunk) XXX_Size() int {
	return m.Size()
}
func (m *TimeSeriesChunk) XXX_DiscardUnknown() {
	xxx_messageInfo_TimeSeriesChunk.DiscardUnknown(m)
}

var xxx_messageInfo_TimeSeriesChunk proto.InternalMessageInfo

func (m *TimeSeriesChunk) GetFromIngesterId() string {
	if m != nil {
		return m.FromIngesterId
	}
	return ""
}

func (m *TimeSeriesChunk) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *TimeSeriesChunk) GetChunks() []Chunk {
	if m != nil {
		return m.Chunks
	}
	return nil
}

type Chunk struct {
	StartTimestampMs int64  `protobuf:"varint,1,opt,name=start_timestamp_ms,json=startTimestampMs,proto3" json:"start_timestamp_ms,omitempty"`
	EndTimestampMs   int64  `protobuf:"varint,2,opt,name=end_timestamp_ms,json=endTimestampMs,proto3" json:"end_timestamp_ms,omitempty"`
	Encoding         int32  `protobuf:"varint,3,opt,name=encoding,proto3" json:"encoding,omitempty"`
	Data             []byte `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
}

func (m *Chunk) Reset()      { *m = Chunk{} }
func (*Chunk) ProtoMessage() {}
func (*Chunk) Descriptor() ([]byte, []int) {
	return fileDescriptor_5b6c87318632a5b2, []int{20}
}
func (m *Chunk) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Chunk) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Chunk.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *Chunk) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Chunk.Merge(m, src)
}
func (m *Chunk) XXX_Size() int {
	return m.Size()
}
func (m *Chunk) XXX_DiscardUnknown() {
	xxx_messageInfo_Chunk.DiscardUnknown(m)
}

var xxx_messageInfo_Chunk proto.InternalMessageInfo

func (m *Chunk) GetStartTimestampMs() int64 {
	if m != nil {
		return m.StartTimestampMs
	}
	return 0
}

func (m *Chunk) GetEndTimestampMs() int64 {
	if m != nil {
		return m.EndTimestampMs
	}
	return 0
}

func (m *Chunk) GetEncoding() int32 {
	if m != nil {
		return m.Encoding
	}
	return 0
}

func (m *Chunk) GetData() []byte {
	if m != nil {
		return m.Data
	}
	return nil
}

type TransferChunksResponse struct {
}

func (m *TransferChunksResponse) Reset()      { *m = TransferChunksResponse{} }
func (*TransferChunksResponse) ProtoMessage() {}
func (*TransferChunksResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_5b6c87318632a5b2, []int{21}
}
func (m *TransferChunksResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *TransferChunksResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_TransferChunksResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *TransferChunksResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TransferChunksResponse.Merge(m, src)
}
func (m *TransferChunksResponse) XXX_Size() int {
	return m.Size()
}
func (m *TransferChunksResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_TransferChunksResponse.DiscardUnknown(m)
}

var xxx_messageInfo_TransferChunksResponse proto.InternalMessageInfo

type LabelMatchers struct {
	Matchers []*LabelMatcher `protobuf:"bytes,1,rep,name=matchers,proto3" json:"matchers,omitempty"`
}

func (m *LabelMatchers) Reset()      { *m = LabelMatchers{} }
func (*LabelMatchers) ProtoMessage() {}
func (*LabelMatchers) Descriptor() ([]byte, []int) {
	return fileDescriptor_5b6c87318632a5b2, []int{22}
}
func (m *LabelMatchers) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *LabelMatchers) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_LabelMatchers.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *LabelMatchers) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LabelMatchers.Merge(m, src)
}
func (m *LabelMatchers) XXX_Size() int {
	return m.Size()
}
func (m *LabelMatchers) XXX_DiscardUnknown() {
	xxx_messageInfo_LabelMatchers.DiscardUnknown(m)
}

var xxx_messageInfo_LabelMatchers proto.InternalMessageInfo

func (m *LabelMatchers) GetMatchers() []*LabelMatcher {
	if m != nil {
		return m.Matchers
	}
	return nil
}

type LabelMatcher struct {
	Type  MatchType `protobuf:"varint,1,opt,name=type,proto3,enum=ingesterpb.MatchType" json:"type,omitempty"`
	Name  string    `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Value string    `protobuf:"bytes,3,opt,name=value,proto3" json:"value,omitempty"`
}

func (m *LabelMatcher) Reset()      { *m = LabelMatcher{} }
func (*LabelMatcher) ProtoMessage() {}
func (*LabelMatcher) Descriptor() ([]byte, []int) {
	return fileDescriptor_5b6c87318632a5b2, []int{23}
}
func (m *LabelMatcher) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *LabelMatcher) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_LabelMatcher.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *LabelMatcher) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LabelMatcher.Merge(m, src)
}
func (m *LabelMatcher) XXX_Size() int {
	return m.Size()
}
func (m *LabelMatcher) XXX_DiscardUnknown() {
	xxx_messageInfo_LabelMatcher.DiscardUnknown(m)
}

var xxx_messageInfo_LabelMatcher proto.InternalMessageInfo

func (m *LabelMatcher) GetType() MatchType {
	if m != nil {
		return m.Type
	}
	return EQUAL
}

func (m *LabelMatcher) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *LabelMatcher) GetValue() string {
	if m != nil {
		return m.Value
	}
	return ""
}

type TimeSeriesFile struct {
	FromIngesterId string `protobuf:"bytes,1,opt,name=from_ingester_id,json=fromIngesterId,proto3" json:"from_ingester_id,omitempty"`
	UserId         string `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Filename       string `protobuf:"bytes,3,opt,name=filename,proto3" json:"filename,omitempty"`
	Data           []byte `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
}

func (m *TimeSeriesFile) Reset()      { *m = TimeSeriesFile{} }
func (*TimeSeriesFile) ProtoMessage() {}
func (*TimeSeriesFile) Descriptor() ([]byte, []int) {
	return fileDescriptor_5b6c87318632a5b2, []int{24}
}
func (m *TimeSeriesFile) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *TimeSeriesFile) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_TimeSeriesFile.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *TimeSeriesFile) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TimeSeriesFile.Merge(m, src)
}
func (m *TimeSeriesFile) XXX_Size() int {
	return m.Size()
}
func (m *TimeSeriesFile) XXX_DiscardUnknown() {
	xxx_messageInfo_TimeSeriesFile.DiscardUnknown(m)
}

var xxx_messageInfo_TimeSeriesFile proto.InternalMessageInfo

func (m *TimeSeriesFile) GetFromIngesterId() string {
	if m != nil {
		return m.FromIngesterId
	}
	return ""
}

func (m *TimeSeriesFile) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *TimeSeriesFile) GetFilename() string {
	if m != nil {
		return m.Filename
	}
	return ""
}

func (m *TimeSeriesFile) GetData() []byte {
	if m != nil {
		return m.Data
	}
	return nil
}

func init() {
	proto.RegisterEnum("ingesterpb.MatchType", MatchType_name, MatchType_value)
	proto.RegisterType((*ReadRequest)(nil), "ingesterpb.ReadRequest")
	proto.RegisterType((*ReadResponse)(nil), "ingesterpb.ReadResponse")
	proto.RegisterType((*QueryRequest)(nil), "ingesterpb.QueryRequest")
	proto.RegisterType((*ExemplarQueryRequest)(nil), "ingesterpb.ExemplarQueryRequest")
	proto.RegisterType((*QueryResponse)(nil), "ingesterpb.QueryResponse")
	proto.RegisterType((*QueryStreamResponse)(nil), "ingesterpb.QueryStreamResponse")
	proto.RegisterType((*ExemplarQueryResponse)(nil), "ingesterpb.ExemplarQueryResponse")
	proto.RegisterType((*LabelValuesRequest)(nil), "ingesterpb.LabelValuesRequest")
	proto.RegisterType((*LabelValuesResponse)(nil), "ingesterpb.LabelValuesResponse")
	proto.RegisterType((*LabelNamesRequest)(nil), "ingesterpb.LabelNamesRequest")
	proto.RegisterType((*LabelNamesResponse)(nil), "ingesterpb.LabelNamesResponse")
	proto.RegisterType((*UserStatsRequest)(nil), "ingesterpb.UserStatsRequest")
	proto.RegisterType((*UserStatsResponse)(nil), "ingesterpb.UserStatsResponse")
	proto.RegisterType((*UserIDStatsResponse)(nil), "ingesterpb.UserIDStatsResponse")
	proto.RegisterType((*UsersStatsResponse)(nil), "ingesterpb.UsersStatsResponse")
	proto.RegisterType((*MetricsForLabelMatchersRequest)(nil), "ingesterpb.MetricsForLabelMatchersRequest")
	proto.RegisterType((*MetricsForLabelMatchersResponse)(nil), "ingesterpb.MetricsForLabelMatchersResponse")
	proto.RegisterType((*MetricsMetadataRequest)(nil), "ingesterpb.MetricsMetadataRequest")
	proto.RegisterType((*MetricsMetadataResponse)(nil), "ingesterpb.MetricsMetadataResponse")
	proto.RegisterType((*TimeSeriesChunk)(nil), "ingesterpb.TimeSeriesChunk")
	proto.RegisterType((*Chunk)(nil), "ingesterpb.Chunk")
	proto.RegisterType((*TransferChunksResponse)(nil), "ingesterpb.TransferChunksResponse")
	proto.RegisterType((*LabelMatchers)(nil), "ingesterpb.LabelMatchers")
	proto.RegisterType((*LabelMatcher)(nil), "ingesterpb.LabelMatcher")
	proto.RegisterType((*TimeSeriesFile)(nil), "ingesterpb.TimeSeriesFile")
}

func init() {
	proto.RegisterFile("pkg/ingester/client/ingester.proto", fileDescriptor_5b6c87318632a5b2)
}

var fileDescriptor_5b6c87318632a5b2 = []byte{
	// 1282 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xbc, 0x57, 0xcf, 0x6f, 0x1b, 0xc5,
	0x17, 0xdf, 0x49, 0x9c, 0x1f, 0x7e, 0x76, 0x5c, 0x67, 0xd2, 0x1f, 0xe9, 0xf6, 0xdb, 0x4d, 0x3b,
	0x5f, 0x21, 0x85, 0x16, 0x62, 0x9a, 0x52, 0x89, 0x22, 0x84, 0x94, 0x86, 0x14, 0xd2, 0x26, 0x69,
	0xbb, 0x49, 0x29, 0x42, 0x48, 0xd6, 0xc4, 0x9e, 0x38, 0xab, 0xec, 0x0f, 0x77, 0x67, 0x16, 0x91,
	0x1b, 0x12, 0x7f, 0x00, 0x1c, 0x10, 0x27, 0xae, 0x48, 0xfc, 0x05, 0x70, 0xe7, 0xd4, 0x63, 0x8f,
	0x15, 0x87, 0x8a, 0xba, 0x17, 0x8e, 0x95, 0xf8, 0x07, 0xd0, 0xce, 0xcc, 0xae, 0x77, 0xd7, 0xb1,
	0x9b, 0x4a, 0x2d, 0x37, 0xcf, 0x7b, 0x9f, 0xfd, 0xcc, 0x67, 0xde, 0x7b, 0x33, 0xef, 0x19, 0x48,
	0xf7, 0xa0, 0xd3, 0x70, 0xfc, 0x0e, 0xe3, 0x82, 0x85, 0x8d, 0x96, 0xeb, 0x30, 0x5f, 0xa4, 0xeb,
	0xa5, 0x6e, 0x18, 0x88, 0x00, 0x43, 0xb2, 0xee, 0xee, 0x9a, 0xef, 0x76, 0x1c, 0xb1, 0x1f, 0xed,
	0x2e, 0xb5, 0x02, 0xaf, 0xd1, 0x09, 0x3a, 0x41, 0x43, 0x42, 0x76, 0xa3, 0x3d, 0xb9, 0x92, 0x0b,
	0xf9, 0x4b, 0x7d, 0x6a, 0x9e, 0x8b, 0xe9, 0xdd, 0xa0, 0xa3, 0x1c, 0xc9, 0x0f, 0xed, 0x34, 0x73,
	0x4e, 0x8f, 0x89, 0xd0, 0x69, 0x71, 0xe5, 0x23, 0x2b, 0x50, 0xb1, 0x19, 0x6d, 0xdb, 0xec, 0x61,
	0xc4, 0xb8, 0xc0, 0xcb, 0x30, 0xf5, 0x30, 0x62, 0xa1, 0xc3, 0xf8, 0x3c, 0xba, 0x30, 0xbe, 0x58,
	0x59, 0x9e, 0x5f, 0xea, 0x8b, 0x5a, 0xba, 0x17, 0xb1, 0xf0, 0x50, 0x43, 0xed, 0x04, 0x48, 0x56,
	0xa1, 0xaa, 0x28, 0x78, 0x37, 0xf0, 0x39, 0xc3, 0x57, 0x61, 0x2a, 0x64, 0x3c, 0x72, 0x45, 0xc2,
	0x71, 0xf6, 0x08, 0x0e, 0x85, 0xb5, 0x13, 0x24, 0xf9, 0x19, 0x41, 0x35, 0x4b, 0x8f, 0xdf, 0x01,
	0xcc, 0x05, 0x0d, 0x45, 0x53, 0x38, 0x1e, 0xe3, 0x82, 0x7a, 0xdd, 0xa6, 0x17, 0x13, 0xa2, 0xc5,
	0x71, 0xbb, 0x2e, 0x3d, 0x3b, 0x89, 0x63, 0x93, 0xe3, 0x45, 0xa8, 0x33, 0xbf, 0x9d, 0xc7, 0x8e,
	0x49, 0x6c, 0x8d, 0xf9, 0xed, 0x2c, 0xf2, 0x7d, 0x98, 0xf6, 0xa8, 0x68, 0xed, 0xb3, 0x90, 0xcf,
	0x8f, 0x0f, 0x1e, 0x71, 0x83, 0xee, 0x32, 0x77, 0x53, 0x01, 0xec, 0x14, 0x49, 0x7e, 0x41, 0x70,
	0x72, 0xed, 0x1b, 0xe6, 0x75, 0x5d, 0x1a, 0xfe, 0x27, 0x32, 0xaf, 0x0d, 0xc8, 0x3c, 0x3b, 0x4c,
	0x26, 0xcf, 0xe8, 0xbc, 0x0d, 0x33, 0xb9, 0x00, 0xe3, 0x0f, 0x01, 0xe4, 0x6e, 0xd9, 0x9c, 0x9e,
	0x5c, 0x4a, 0x0b, 0x24, 0xde, 0x72, 0x5b, 0xfa, 0x6e, 0x94, 0x1e, 0x3d, 0x5d, 0x30, 0xec, 0x0c,
	0x9a, 0xfc, 0x84, 0x60, 0x4e, 0xb2, 0x6d, 0x8b, 0x90, 0x51, 0x2f, 0xe5, 0x5c, 0x85, 0x4a, 0x6b,
	0x3f, 0xf2, 0x0f, 0x72, 0xa4, 0xe7, 0xb2, 0xf2, 0xfa, 0xb4, 0xab, 0x31, 0x50, 0x73, 0x67, 0xbf,
	0x2a, 0x08, 0x1b, 0x7b, 0x25, 0x61, 0xdb, 0x70, 0xaa, 0x90, 0x8c, 0xd7, 0x70, 0xda, 0x3f, 0x10,
	0x60, 0x19, 0xd6, 0xcf, 0xa9, 0x1b, 0x31, 0x9e, 0x24, 0xf8, 0x3c, 0x80, 0x1b, 0x5b, 0x9b, 0x3e,
	0xf5, 0x98, 0x4c, 0x6c, 0xd9, 0x2e, 0x4b, 0xcb, 0x16, 0xf5, 0xd8, 0x90, 0xfc, 0x8f, 0xbd, 0x42,
	0xfe, 0xc7, 0x5f, 0x9a, 0xff, 0xd2, 0x05, 0x74, 0xdc, 0xfc, 0x7f, 0x00, 0x73, 0xb9, 0x33, 0xe8,
	0xb8, 0x5c, 0x84, 0xaa, 0x3a, 0xc4, 0xd7, 0xd2, 0x2e, 0x23, 0x53, 0xb6, 0x2b, 0x6e, 0x1f, 0x4a,
	0x0e, 0x60, 0x76, 0x23, 0x39, 0x15, 0x7f, 0xc3, 0xd5, 0x4d, 0xae, 0xe9, 0x50, 0xeb, 0xcd, 0xb4,
	0xca, 0x05, 0xa8, 0xf4, 0x43, 0x9d, 0x88, 0x84, 0x34, 0xd6, 0x9c, 0x60, 0xa8, 0xdf, 0xe7, 0x2c,
	0xdc, 0x16, 0x54, 0x24, 0x12, 0xc9, 0xef, 0x08, 0x66, 0x33, 0x46, 0x4d, 0xf5, 0x16, 0xd4, 0x54,
	0xb4, 0x9c, 0xc0, 0x6f, 0x86, 0x54, 0xa8, 0xcc, 0x21, 0x7b, 0x26, 0xb5, 0xda, 0x54, 0xb0, 0x38,
	0xb9, 0x7e, 0xe4, 0x35, 0xd3, 0x22, 0x44, 0x8b, 0x25, 0xbb, 0xec, 0x47, 0x9e, 0x2a, 0x92, 0xf8,
	0xf8, 0xb4, 0xeb, 0x34, 0x0b, 0x4c, 0xe3, 0x92, 0xa9, 0x4e, 0xbb, 0xce, 0x7a, 0x8e, 0x6c, 0x09,
	0xe6, 0xc2, 0xc8, 0x65, 0x45, 0x78, 0x49, 0xc2, 0x67, 0x63, 0x57, 0x0e, 0x4f, 0x28, 0xcc, 0xc5,
	0xc2, 0xd7, 0x3f, 0xc9, 0x4b, 0x3f, 0x03, 0x53, 0x11, 0x67, 0x61, 0xd3, 0x69, 0xeb, 0x6a, 0x9b,
	0x8c, 0x97, 0xeb, 0x6d, 0x7c, 0x05, 0x4a, 0x6d, 0x2a, 0xa8, 0x94, 0x59, 0x59, 0x3e, 0x9f, 0x2d,
	0x87, 0x81, 0x00, 0xd8, 0x12, 0x4a, 0x6e, 0x03, 0x8e, 0x5d, 0x3c, 0xbf, 0xc3, 0x35, 0x98, 0xe0,
	0xb1, 0x41, 0x5f, 0x90, 0x85, 0x22, 0x53, 0x41, 0x91, 0xad, 0xd0, 0xe4, 0x37, 0x04, 0xd6, 0xa6,
	0x6a, 0x1e, 0x37, 0x83, 0x30, 0x5f, 0x81, 0x6f, 0xf8, 0x35, 0xfc, 0x08, 0xaa, 0x49, 0x89, 0x37,
	0x39, 0x13, 0x2f, 0x7f, 0x11, 0x2b, 0x09, 0x7c, 0x9b, 0x09, 0x72, 0x1b, 0x16, 0x86, 0xea, 0xd6,
	0x21, 0x59, 0x84, 0x49, 0xd5, 0x17, 0x75, 0x4c, 0xea, 0xfd, 0x47, 0x43, 0x7d, 0x6a, 0x6b, 0x3f,
	0x99, 0x87, 0xd3, 0x9a, 0x6c, 0x93, 0x09, 0x1a, 0x47, 0x39, 0xa9, 0xc4, 0x3b, 0x70, 0x66, 0xc0,
	0xa3, 0xe9, 0xe3, 0xa6, 0xa3, 0x6d, 0x69, 0x5f, 0x2d, 0x6c, 0x90, 0x7e, 0x93, 0x22, 0xc9, 0x3f,
	0x08, 0x4e, 0x14, 0x5e, 0xd2, 0x38, 0x66, 0x7b, 0x61, 0xe0, 0x35, 0x93, 0x93, 0xf7, 0xcb, 0xa4,
	0x16, 0xdb, 0xd7, 0xb5, 0x79, 0xbd, 0x9d, 0xad, 0xa3, 0xb1, 0x5c, 0x1d, 0xb9, 0x30, 0x29, 0xef,
	0x54, 0xbf, 0xb1, 0xa4, 0x52, 0x36, 0x58, 0x87, 0xb6, 0x0e, 0x65, 0x88, 0xee, 0x52, 0x27, 0xbc,
	0x71, 0x3d, 0x7e, 0x25, 0xff, 0x7c, 0xba, 0x70, 0x25, 0x3b, 0x8d, 0x84, 0x74, 0x8f, 0xfa, 0xb4,
	0xe1, 0x06, 0x07, 0x4e, 0x23, 0x3b, 0x59, 0xa8, 0x14, 0xac, 0xb4, 0x69, 0x57, 0xb0, 0xd0, 0xd6,
	0x7b, 0xe0, 0x06, 0x4c, 0xaa, 0x67, 0x7f, 0xbe, 0x24, 0x77, 0x9b, 0xcd, 0x26, 0x2d, 0xdb, 0x1d,
	0x34, 0x8c, 0x7c, 0x8f, 0x60, 0x42, 0x9d, 0xf5, 0x4d, 0x55, 0x93, 0x09, 0xd3, 0xcc, 0x6f, 0x05,
	0x6d, 0xc7, 0xef, 0xc8, 0xcb, 0x3c, 0x61, 0xa7, 0x6b, 0x8c, 0xf5, 0x25, 0x8b, 0x6f, 0x6d, 0x55,
	0xdf, 0xa2, 0x79, 0x38, 0xbd, 0x13, 0x52, 0x9f, 0xef, 0xb1, 0x50, 0x0a, 0x4b, 0xcb, 0x86, 0xac,
	0xc1, 0x4c, 0xae, 0x9e, 0x72, 0xd3, 0x05, 0x3a, 0xf6, 0x74, 0xd1, 0x82, 0x6a, 0xd6, 0x83, 0xdf,
	0x86, 0x92, 0x38, 0xec, 0xaa, 0x37, 0xab, 0xb6, 0x7c, 0x2a, 0xcb, 0x20, 0x21, 0x3b, 0x87, 0x5d,
	0x66, 0x4b, 0x48, 0xac, 0x57, 0x36, 0x26, 0x95, 0x62, 0xf9, 0x1b, 0x9f, 0x84, 0x09, 0xf9, 0xce,
	0xcb, 0xc3, 0x95, 0x6d, 0xb5, 0x20, 0xdf, 0x21, 0xa8, 0xf5, 0xab, 0xe9, 0xa6, 0xe3, 0xb2, 0xd7,
	0x51, 0x4c, 0x26, 0x4c, 0xef, 0x39, 0x2e, 0x93, 0x1a, 0xd4, 0x76, 0xe9, 0xfa, 0xa8, 0x58, 0x5e,
	0xba, 0x05, 0xe5, 0xf4, 0x08, 0xb8, 0x0c, 0x13, 0x6b, 0xf7, 0xee, 0xaf, 0x6c, 0xd4, 0x0d, 0x3c,
	0x03, 0xe5, 0xad, 0x3b, 0x3b, 0x4d, 0xb5, 0x44, 0xf8, 0x04, 0x54, 0xec, 0xb5, 0x4f, 0xd7, 0xbe,
	0x68, 0x6e, 0xae, 0xec, 0xac, 0x7e, 0x56, 0x1f, 0xc3, 0x18, 0x6a, 0xca, 0xb0, 0x75, 0x47, 0xdb,
	0xc6, 0x97, 0x7f, 0x9c, 0x82, 0xe9, 0x44, 0x23, 0xbe, 0x0e, 0xa5, 0xbb, 0x11, 0xdf, 0xc7, 0xa7,
	0xfb, 0xd5, 0xfc, 0x20, 0x74, 0x04, 0xd3, 0xb7, 0xd3, 0x3c, 0x33, 0x60, 0xd7, 0x39, 0x34, 0xf0,
	0xc7, 0x30, 0x21, 0xc7, 0x08, 0x3c, 0x74, 0xd8, 0x35, 0x87, 0x8f, 0xb0, 0xc4, 0xc0, 0x1b, 0x50,
	0xc9, 0x8c, 0x49, 0x23, 0x58, 0x16, 0x06, 0x3c, 0xf9, 0xc9, 0x8a, 0x18, 0xef, 0x21, 0xfc, 0x00,
	0x6a, 0xd2, 0x95, 0x4c, 0x38, 0x1c, 0x5f, 0xc8, 0x7e, 0x76, 0xd4, 0x14, 0x6a, 0x5e, 0x1c, 0x81,
	0x48, 0x65, 0xde, 0x85, 0x4a, 0x66, 0x36, 0xc0, 0xd6, 0x40, 0x61, 0xe6, 0x06, 0x9f, 0xbc, 0xd8,
	0x23, 0x86, 0x0a, 0x62, 0xe0, 0x4d, 0x80, 0x7e, 0x1b, 0xc7, 0xe7, 0x07, 0x3e, 0xc8, 0xce, 0x12,
	0xa6, 0x35, 0xcc, 0x9d, 0xd2, 0xdd, 0x82, 0x72, 0xda, 0xc8, 0xf0, 0xff, 0x86, 0xf4, 0x37, 0x45,
	0x36, 0xba, 0xfb, 0x11, 0x03, 0x6f, 0x41, 0x75, 0xc5, 0x75, 0x8f, 0x4b, 0x67, 0x15, 0xbd, 0xbc,
	0xc8, 0x27, 0xd2, 0xc7, 0xbd, 0xd8, 0x43, 0xf0, 0xa5, 0xdc, 0xfd, 0x1c, 0xd9, 0x20, 0xcd, 0xcb,
	0xc7, 0xc2, 0xa6, 0xbb, 0x7e, 0x05, 0x27, 0x0a, 0x2d, 0x05, 0x93, 0x23, 0x18, 0x0a, 0x9d, 0xc8,
	0xfc, 0xff, 0x48, 0x4c, 0xca, 0x7e, 0x1f, 0x6a, 0xf9, 0x77, 0x0d, 0x8f, 0x1a, 0xe2, 0xcd, 0xdc,
	0xce, 0x43, 0x1e, 0x44, 0x63, 0x11, 0xdd, 0xa0, 0x8f, 0x9f, 0x59, 0xc6, 0x93, 0x67, 0x96, 0xf1,
	0xe2, 0x99, 0x85, 0xbe, 0xed, 0x59, 0xe8, 0xd7, 0x9e, 0x85, 0x1e, 0xf5, 0x2c, 0xf4, 0xb8, 0x67,
	0xa1, 0xbf, 0x7a, 0x16, 0xfa, 0xbb, 0x67, 0x19, 0x2f, 0x7a, 0x16, 0xfa, 0xe1, 0xb9, 0x65, 0x3c,
	0x7e, 0x6e, 0x19, 0x4f, 0x9e, 0x5b, 0xc6, 0x97, 0x97, 0x47, 0xb5, 0x98, 0xc2, 0x1f, 0xe7, 0xdd,
	0x49, 0x79, 0x91, 0xaf, 0xfe, 0x1b, 0x00, 0x00, 0xff, 0xff, 0xc9, 0xb9, 0xeb, 0xf0, 0x56, 0x0f,
	0x00, 0x00,
}

func (x MatchType) String() string {
	s, ok := MatchType_name[int32(x)]
	if ok {
		return s
	}
	return strconv.Itoa(int(x))
}
func (this *ReadRequest) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*ReadRequest)
	if !ok {
		that2, ok := that.(ReadRequest)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if len(this.Queries) != len(that1.Queries) {
		return false
	}
	for i := range this.Queries {
		if !this.Queries[i].Equal(that1.Queries[i]) {
			return false
		}
	}
	return true
}
func (this *ReadResponse) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*ReadResponse)
	if !ok {
		that2, ok := that.(ReadResponse)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if len(this.Results) != len(that1.Results) {
		return false
	}
	for i := range this.Results {
		if !this.Results[i].Equal(that1.Results[i]) {
			return false
		}
	}
	return true
}
func (this *QueryRequest) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*QueryRequest)
	if !ok {
		that2, ok := that.(QueryRequest)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.StartTimestampMs != that1.StartTimestampMs {
		return false
	}
	if this.EndTimestampMs != that1.EndTimestampMs {
		return false
	}
	if len(this.Matchers) != len(that1.Matchers) {
		return false
	}
	for i := range this.Matchers {
		if !this.Matchers[i].Equal(that1.Matchers[i]) {
			return false
		}
	}
	return true
}
func (this *ExemplarQueryRequest) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*ExemplarQueryRequest)
	if !ok {
		that2, ok := that.(ExemplarQueryRequest)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.StartTimestampMs != that1.StartTimestampMs {
		return false
	}
	if this.EndTimestampMs != that1.EndTimestampMs {
		return false
	}
	if len(this.Matchers) != len(that1.Matchers) {
		return false
	}
	for i := range this.Matchers {
		if !this.Matchers[i].Equal(that1.Matchers[i]) {
			return false
		}
	}
	return true
}
func (this *QueryResponse) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*QueryResponse)
	if !ok {
		that2, ok := that.(QueryResponse)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if len(this.Timeseries) != len(that1.Timeseries) {
		return false
	}
	for i := range this.Timeseries {
		if !this.Timeseries[i].Equal(&that1.Timeseries[i]) {
			return false
		}
	}
	return true
}
func (this *QueryStreamResponse) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*QueryStreamResponse)
	if !ok {
		that2, ok := that.(QueryStreamResponse)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if len(this.Chunkseries) != len(that1.Chunkseries) {
		return false
	}
	for i := range this.Chunkseries {
		if !this.Chunkseries[i].Equal(&that1.Chunkseries[i]) {
			return false
		}
	}
	if len(this.Timeseries) != len(that1.Timeseries) {
		return false
	}
	for i := range this.Timeseries {
		if !this.Timeseries[i].Equal(&that1.Timeseries[i]) {
			return false
		}
	}
	return true
}
func (this *ExemplarQueryResponse) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*ExemplarQueryResponse)
	if !ok {
		that2, ok := that.(ExemplarQueryResponse)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if len(this.Timeseries) != len(that1.Timeseries) {
		return false
	}
	for i := range this.Timeseries {
		if !this.Timeseries[i].Equal(&that1.Timeseries[i]) {
			return false
		}
	}
	return true
}
func (this *LabelValuesRequest) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*LabelValuesRequest)
	if !ok {
		that2, ok := that.(LabelValuesRequest)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.LabelName != that1.LabelName {
		return false
	}
	if this.StartTimestampMs != that1.StartTimestampMs {
		return false
	}
	if this.EndTimestampMs != that1.EndTimestampMs {
		return false
	}
	if !this.Matchers.Equal(that1.Matchers) {
		return false
	}
	return true
}
func (this *LabelValuesResponse) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*LabelValuesResponse)
	if !ok {
		that2, ok := that.(LabelValuesResponse)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if len(this.LabelValues) != len(that1.LabelValues) {
		return false
	}
	for i := range this.LabelValues {
		if this.LabelValues[i] != that1.LabelValues[i] {
			return false
		}
	}
	return true
}
func (this *LabelNamesRequest) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*LabelNamesRequest)
	if !ok {
		that2, ok := that.(LabelNamesRequest)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.StartTimestampMs != that1.StartTimestampMs {
		return false
	}
	if this.EndTimestampMs != that1.EndTimestampMs {
		return false
	}
	return true
}
func (this *LabelNamesResponse) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*LabelNamesResponse)
	if !ok {
		that2, ok := that.(LabelNamesResponse)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if len(this.LabelNames) != len(that1.LabelNames) {
		return false
	}
	for i := range this.LabelNames {
		if this.LabelNames[i] != that1.LabelNames[i] {
			return false
		}
	}
	return true
}
func (this *UserStatsRequest) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*UserStatsRequest)
	if !ok {
		that2, ok := that.(UserStatsRequest)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	return true
}
func (this *UserStatsResponse) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*UserStatsResponse)
	if !ok {
		that2, ok := that.(UserStatsResponse)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.IngestionRate != that1.IngestionRate {
		return false
	}
	if this.NumSeries != that1.NumSeries {
		return false
	}
	if this.ApiIngestionRate != that1.ApiIngestionRate {
		return false
	}
	if this.RuleIngestionRate != that1.RuleIngestionRate {
		return false
	}
	return true
}
func (this *UserIDStatsResponse) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*UserIDStatsResponse)
	if !ok {
		that2, ok := that.(UserIDStatsResponse)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.UserId != that1.UserId {
		return false
	}
	if !this.Data.Equal(that1.Data) {
		return false
	}
	return true
}
func (this *UsersStatsResponse) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*UsersStatsResponse)
	if !ok {
		that2, ok := that.(UsersStatsResponse)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if len(this.Stats) != len(that1.Stats) {
		return false
	}
	for i := range this.Stats {
		if !this.Stats[i].Equal(that1.Stats[i]) {
			return false
		}
	}
	return true
}
func (this *MetricsForLabelMatchersRequest) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*MetricsForLabelMatchersRequest)
	if !ok {
		that2, ok := that.(MetricsForLabelMatchersRequest)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.StartTimestampMs != that1.StartTimestampMs {
		return false
	}
	if this.EndTimestampMs != that1.EndTimestampMs {
		return false
	}
	if len(this.MatchersSet) != len(that1.MatchersSet) {
		return false
	}
	for i := range this.MatchersSet {
		if !this.MatchersSet[i].Equal(that1.MatchersSet[i]) {
			return false
		}
	}
	return true
}
func (this *MetricsForLabelMatchersResponse) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*MetricsForLabelMatchersResponse)
	if !ok {
		that2, ok := that.(MetricsForLabelMatchersResponse)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if len(this.Metric) != len(that1.Metric) {
		return false
	}
	for i := range this.Metric {
		if !this.Metric[i].Equal(that1.Metric[i]) {
			return false
		}
	}
	return true
}
func (this *MetricsMetadataRequest) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*MetricsMetadataRequest)
	if !ok {
		that2, ok := that.(MetricsMetadataRequest)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	return true
}
func (this *MetricsMetadataResponse) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*MetricsMetadataResponse)
	if !ok {
		that2, ok := that.(MetricsMetadataResponse)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if len(this.Metadata) != len(that1.Metadata) {
		return false
	}
	for i := range this.Metadata {
		if !this.Metadata[i].Equal(that1.Metadata[i]) {
			return false
		}
	}
	return true
}
func (this *TimeSeriesChunk) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*TimeSeriesChunk)
	if !ok {
		that2, ok := that.(TimeSeriesChunk)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.FromIngesterId != that1.FromIngesterId {
		return false
	}
	if this.UserId != that1.UserId {
		return false
	}
	if len(this.Labels) != len(that1.Labels) {
		return false
	}
	for i := range this.Labels {
		if !this.Labels[i].Equal(that1.Labels[i]) {
			return false
		}
	}
	if len(this.Chunks) != len(that1.Chunks) {
		return false
	}
	for i := range this.Chunks {
		if !this.Chunks[i].Equal(&that1.Chunks[i]) {
			return false
		}
	}
	return true
}
func (this *Chunk) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*Chunk)
	if !ok {
		that2, ok := that.(Chunk)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.StartTimestampMs != that1.StartTimestampMs {
		return false
	}
	if this.EndTimestampMs != that1.EndTimestampMs {
		return false
	}
	if this.Encoding != that1.Encoding {
		return false
	}
	if !bytes.Equal(this.Data, that1.Data) {
		return false
	}
	return true
}
func (this *TransferChunksResponse) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*TransferChunksResponse)
	if !ok {
		that2, ok := that.(TransferChunksResponse)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	return true
}
func (this *LabelMatchers) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*LabelMatchers)
	if !ok {
		that2, ok := that.(LabelMatchers)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if len(this.Matchers) != len(that1.Matchers) {
		return false
	}
	for i := range this.Matchers {
		if !this.Matchers[i].Equal(that1.Matchers[i]) {
			return false
		}
	}
	return true
}
func (this *LabelMatcher) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*LabelMatcher)
	if !ok {
		that2, ok := that.(LabelMatcher)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.Type != that1.Type {
		return false
	}
	if this.Name != that1.Name {
		return false
	}
	if this.Value != that1.Value {
		return false
	}
	return true
}
func (this *TimeSeriesFile) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*TimeSeriesFile)
	if !ok {
		that2, ok := that.(TimeSeriesFile)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.FromIngesterId != that1.FromIngesterId {
		return false
	}
	if this.UserId != that1.UserId {
		return false
	}
	if this.Filename != that1.Filename {
		return false
	}
	if !bytes.Equal(this.Data, that1.Data) {
		return false
	}
	return true
}
func (this *ReadRequest) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 5)
	s = append(s, "&client.ReadRequest{")
	if this.Queries != nil {
		s = append(s, "Queries: "+fmt.Sprintf("%#v", this.Queries)+",\n")
	}
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *ReadResponse) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 5)
	s = append(s, "&client.ReadResponse{")
	if this.Results != nil {
		s = append(s, "Results: "+fmt.Sprintf("%#v", this.Results)+",\n")
	}
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *QueryRequest) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 7)
	s = append(s, "&client.QueryRequest{")
	s = append(s, "StartTimestampMs: "+fmt.Sprintf("%#v", this.StartTimestampMs)+",\n")
	s = append(s, "EndTimestampMs: "+fmt.Sprintf("%#v", this.EndTimestampMs)+",\n")
	if this.Matchers != nil {
		s = append(s, "Matchers: "+fmt.Sprintf("%#v", this.Matchers)+",\n")
	}
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *ExemplarQueryRequest) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 7)
	s = append(s, "&client.ExemplarQueryRequest{")
	s = append(s, "StartTimestampMs: "+fmt.Sprintf("%#v", this.StartTimestampMs)+",\n")
	s = append(s, "EndTimestampMs: "+fmt.Sprintf("%#v", this.EndTimestampMs)+",\n")
	if this.Matchers != nil {
		s = append(s, "Matchers: "+fmt.Sprintf("%#v", this.Matchers)+",\n")
	}
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *QueryResponse) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 5)
	s = append(s, "&client.QueryResponse{")
	if this.Timeseries != nil {
		vs := make([]*logproto.TimeSeries, len(this.Timeseries))
		for i := range vs {
			vs[i] = &this.Timeseries[i]
		}
		s = append(s, "Timeseries: "+fmt.Sprintf("%#v", vs)+",\n")
	}
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *QueryStreamResponse) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 6)
	s = append(s, "&client.QueryStreamResponse{")
	if this.Chunkseries != nil {
		vs := make([]*TimeSeriesChunk, len(this.Chunkseries))
		for i := range vs {
			vs[i] = &this.Chunkseries[i]
		}
		s = append(s, "Chunkseries: "+fmt.Sprintf("%#v", vs)+",\n")
	}
	if this.Timeseries != nil {
		vs := make([]*logproto.TimeSeries, len(this.Timeseries))
		for i := range vs {
			vs[i] = &this.Timeseries[i]
		}
		s = append(s, "Timeseries: "+fmt.Sprintf("%#v", vs)+",\n")
	}
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *ExemplarQueryResponse) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 5)
	s = append(s, "&client.ExemplarQueryResponse{")
	if this.Timeseries != nil {
		vs := make([]*logproto.TimeSeries, len(this.Timeseries))
		for i := range vs {
			vs[i] = &this.Timeseries[i]
		}
		s = append(s, "Timeseries: "+fmt.Sprintf("%#v", vs)+",\n")
	}
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *LabelValuesRequest) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 8)
	s = append(s, "&client.LabelValuesRequest{")
	s = append(s, "LabelName: "+fmt.Sprintf("%#v", this.LabelName)+",\n")
	s = append(s, "StartTimestampMs: "+fmt.Sprintf("%#v", this.StartTimestampMs)+",\n")
	s = append(s, "EndTimestampMs: "+fmt.Sprintf("%#v", this.EndTimestampMs)+",\n")
	if this.Matchers != nil {
		s = append(s, "Matchers: "+fmt.Sprintf("%#v", this.Matchers)+",\n")
	}
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *LabelValuesResponse) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 5)
	s = append(s, "&client.LabelValuesResponse{")
	s = append(s, "LabelValues: "+fmt.Sprintf("%#v", this.LabelValues)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *LabelNamesRequest) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 6)
	s = append(s, "&client.LabelNamesRequest{")
	s = append(s, "StartTimestampMs: "+fmt.Sprintf("%#v", this.StartTimestampMs)+",\n")
	s = append(s, "EndTimestampMs: "+fmt.Sprintf("%#v", this.EndTimestampMs)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *LabelNamesResponse) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 5)
	s = append(s, "&client.LabelNamesResponse{")
	s = append(s, "LabelNames: "+fmt.Sprintf("%#v", this.LabelNames)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *UserStatsRequest) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 4)
	s = append(s, "&client.UserStatsRequest{")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *UserStatsResponse) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 8)
	s = append(s, "&client.UserStatsResponse{")
	s = append(s, "IngestionRate: "+fmt.Sprintf("%#v", this.IngestionRate)+",\n")
	s = append(s, "NumSeries: "+fmt.Sprintf("%#v", this.NumSeries)+",\n")
	s = append(s, "ApiIngestionRate: "+fmt.Sprintf("%#v", this.ApiIngestionRate)+",\n")
	s = append(s, "RuleIngestionRate: "+fmt.Sprintf("%#v", this.RuleIngestionRate)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *UserIDStatsResponse) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 6)
	s = append(s, "&client.UserIDStatsResponse{")
	s = append(s, "UserId: "+fmt.Sprintf("%#v", this.UserId)+",\n")
	if this.Data != nil {
		s = append(s, "Data: "+fmt.Sprintf("%#v", this.Data)+",\n")
	}
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *UsersStatsResponse) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 5)
	s = append(s, "&client.UsersStatsResponse{")
	if this.Stats != nil {
		s = append(s, "Stats: "+fmt.Sprintf("%#v", this.Stats)+",\n")
	}
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *MetricsForLabelMatchersRequest) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 7)
	s = append(s, "&client.MetricsForLabelMatchersRequest{")
	s = append(s, "StartTimestampMs: "+fmt.Sprintf("%#v", this.StartTimestampMs)+",\n")
	s = append(s, "EndTimestampMs: "+fmt.Sprintf("%#v", this.EndTimestampMs)+",\n")
	if this.MatchersSet != nil {
		s = append(s, "MatchersSet: "+fmt.Sprintf("%#v", this.MatchersSet)+",\n")
	}
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *MetricsForLabelMatchersResponse) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 5)
	s = append(s, "&client.MetricsForLabelMatchersResponse{")
	if this.Metric != nil {
		s = append(s, "Metric: "+fmt.Sprintf("%#v", this.Metric)+",\n")
	}
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *MetricsMetadataRequest) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 4)
	s = append(s, "&client.MetricsMetadataRequest{")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *MetricsMetadataResponse) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 5)
	s = append(s, "&client.MetricsMetadataResponse{")
	if this.Metadata != nil {
		s = append(s, "Metadata: "+fmt.Sprintf("%#v", this.Metadata)+",\n")
	}
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *TimeSeriesChunk) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 8)
	s = append(s, "&client.TimeSeriesChunk{")
	s = append(s, "FromIngesterId: "+fmt.Sprintf("%#v", this.FromIngesterId)+",\n")
	s = append(s, "UserId: "+fmt.Sprintf("%#v", this.UserId)+",\n")
	s = append(s, "Labels: "+fmt.Sprintf("%#v", this.Labels)+",\n")
	if this.Chunks != nil {
		vs := make([]*Chunk, len(this.Chunks))
		for i := range vs {
			vs[i] = &this.Chunks[i]
		}
		s = append(s, "Chunks: "+fmt.Sprintf("%#v", vs)+",\n")
	}
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *Chunk) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 8)
	s = append(s, "&client.Chunk{")
	s = append(s, "StartTimestampMs: "+fmt.Sprintf("%#v", this.StartTimestampMs)+",\n")
	s = append(s, "EndTimestampMs: "+fmt.Sprintf("%#v", this.EndTimestampMs)+",\n")
	s = append(s, "Encoding: "+fmt.Sprintf("%#v", this.Encoding)+",\n")
	s = append(s, "Data: "+fmt.Sprintf("%#v", this.Data)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *TransferChunksResponse) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 4)
	s = append(s, "&client.TransferChunksResponse{")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *LabelMatchers) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 5)
	s = append(s, "&client.LabelMatchers{")
	if this.Matchers != nil {
		s = append(s, "Matchers: "+fmt.Sprintf("%#v", this.Matchers)+",\n")
	}
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *LabelMatcher) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 7)
	s = append(s, "&client.LabelMatcher{")
	s = append(s, "Type: "+fmt.Sprintf("%#v", this.Type)+",\n")
	s = append(s, "Name: "+fmt.Sprintf("%#v", this.Name)+",\n")
	s = append(s, "Value: "+fmt.Sprintf("%#v", this.Value)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *TimeSeriesFile) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 8)
	s = append(s, "&client.TimeSeriesFile{")
	s = append(s, "FromIngesterId: "+fmt.Sprintf("%#v", this.FromIngesterId)+",\n")
	s = append(s, "UserId: "+fmt.Sprintf("%#v", this.UserId)+",\n")
	s = append(s, "Filename: "+fmt.Sprintf("%#v", this.Filename)+",\n")
	s = append(s, "Data: "+fmt.Sprintf("%#v", this.Data)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func valueToGoStringIngester(v interface{}, typ string) string {
	rv := reflect.ValueOf(v)
	if rv.IsNil() {
		return "nil"
	}
	pv := reflect.Indirect(rv).Interface()
	return fmt.Sprintf("func(v %v) *%v { return &v } ( %#v )", typ, typ, pv)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// IngesterClient is the client API for Ingester service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type IngesterClient interface {
	Push(ctx context.Context, in *logproto.WriteRequest, opts ...grpc.CallOption) (*logproto.WriteResponse, error)
	Query(ctx context.Context, in *QueryRequest, opts ...grpc.CallOption) (*QueryResponse, error)
	QueryStream(ctx context.Context, in *QueryRequest, opts ...grpc.CallOption) (Ingester_QueryStreamClient, error)
	QueryExemplars(ctx context.Context, in *ExemplarQueryRequest, opts ...grpc.CallOption) (*ExemplarQueryResponse, error)
	LabelValues(ctx context.Context, in *LabelValuesRequest, opts ...grpc.CallOption) (*LabelValuesResponse, error)
	LabelNames(ctx context.Context, in *LabelNamesRequest, opts ...grpc.CallOption) (*LabelNamesResponse, error)
	UserStats(ctx context.Context, in *UserStatsRequest, opts ...grpc.CallOption) (*UserStatsResponse, error)
	AllUserStats(ctx context.Context, in *UserStatsRequest, opts ...grpc.CallOption) (*UsersStatsResponse, error)
	MetricsForLabelMatchers(ctx context.Context, in *MetricsForLabelMatchersRequest, opts ...grpc.CallOption) (*MetricsForLabelMatchersResponse, error)
	MetricsMetadata(ctx context.Context, in *MetricsMetadataRequest, opts ...grpc.CallOption) (*MetricsMetadataResponse, error)
	// TransferChunks allows leaving ingester (client) to stream chunks directly to joining ingesters (server).
	TransferChunks(ctx context.Context, opts ...grpc.CallOption) (Ingester_TransferChunksClient, error)
}

type ingesterClient struct {
	cc *grpc.ClientConn
}

func NewIngesterClient(cc *grpc.ClientConn) IngesterClient {
	return &ingesterClient{cc}
}

func (c *ingesterClient) Push(ctx context.Context, in *logproto.WriteRequest, opts ...grpc.CallOption) (*logproto.WriteResponse, error) {
	out := new(logproto.WriteResponse)
	err := c.cc.Invoke(ctx, "/ingesterpb.Ingester/Push", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ingesterClient) Query(ctx context.Context, in *QueryRequest, opts ...grpc.CallOption) (*QueryResponse, error) {
	out := new(QueryResponse)
	err := c.cc.Invoke(ctx, "/ingesterpb.Ingester/Query", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ingesterClient) QueryStream(ctx context.Context, in *QueryRequest, opts ...grpc.CallOption) (Ingester_QueryStreamClient, error) {
	stream, err := c.cc.NewStream(ctx, &_Ingester_serviceDesc.Streams[0], "/ingesterpb.Ingester/QueryStream", opts...)
	if err != nil {
		return nil, err
	}
	x := &ingesterQueryStreamClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type Ingester_QueryStreamClient interface {
	Recv() (*QueryStreamResponse, error)
	grpc.ClientStream
}

type ingesterQueryStreamClient struct {
	grpc.ClientStream
}

func (x *ingesterQueryStreamClient) Recv() (*QueryStreamResponse, error) {
	m := new(QueryStreamResponse)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *ingesterClient) QueryExemplars(ctx context.Context, in *ExemplarQueryRequest, opts ...grpc.CallOption) (*ExemplarQueryResponse, error) {
	out := new(ExemplarQueryResponse)
	err := c.cc.Invoke(ctx, "/ingesterpb.Ingester/QueryExemplars", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ingesterClient) LabelValues(ctx context.Context, in *LabelValuesRequest, opts ...grpc.CallOption) (*LabelValuesResponse, error) {
	out := new(LabelValuesResponse)
	err := c.cc.Invoke(ctx, "/ingesterpb.Ingester/LabelValues", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ingesterClient) LabelNames(ctx context.Context, in *LabelNamesRequest, opts ...grpc.CallOption) (*LabelNamesResponse, error) {
	out := new(LabelNamesResponse)
	err := c.cc.Invoke(ctx, "/ingesterpb.Ingester/LabelNames", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ingesterClient) UserStats(ctx context.Context, in *UserStatsRequest, opts ...grpc.CallOption) (*UserStatsResponse, error) {
	out := new(UserStatsResponse)
	err := c.cc.Invoke(ctx, "/ingesterpb.Ingester/UserStats", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ingesterClient) AllUserStats(ctx context.Context, in *UserStatsRequest, opts ...grpc.CallOption) (*UsersStatsResponse, error) {
	out := new(UsersStatsResponse)
	err := c.cc.Invoke(ctx, "/ingesterpb.Ingester/AllUserStats", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ingesterClient) MetricsForLabelMatchers(ctx context.Context, in *MetricsForLabelMatchersRequest, opts ...grpc.CallOption) (*MetricsForLabelMatchersResponse, error) {
	out := new(MetricsForLabelMatchersResponse)
	err := c.cc.Invoke(ctx, "/ingesterpb.Ingester/MetricsForLabelMatchers", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ingesterClient) MetricsMetadata(ctx context.Context, in *MetricsMetadataRequest, opts ...grpc.CallOption) (*MetricsMetadataResponse, error) {
	out := new(MetricsMetadataResponse)
	err := c.cc.Invoke(ctx, "/ingesterpb.Ingester/MetricsMetadata", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ingesterClient) TransferChunks(ctx context.Context, opts ...grpc.CallOption) (Ingester_TransferChunksClient, error) {
	stream, err := c.cc.NewStream(ctx, &_Ingester_serviceDesc.Streams[1], "/ingesterpb.Ingester/TransferChunks", opts...)
	if err != nil {
		return nil, err
	}
	x := &ingesterTransferChunksClient{stream}
	return x, nil
}

type Ingester_TransferChunksClient interface {
	Send(*TimeSeriesChunk) error
	CloseAndRecv() (*TransferChunksResponse, error)
	grpc.ClientStream
}

type ingesterTransferChunksClient struct {
	grpc.ClientStream
}

func (x *ingesterTransferChunksClient) Send(m *TimeSeriesChunk) error {
	return x.ClientStream.SendMsg(m)
}

func (x *ingesterTransferChunksClient) CloseAndRecv() (*TransferChunksResponse, error) {
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	m := new(TransferChunksResponse)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

// IngesterServer is the server API for Ingester service.
type IngesterServer interface {
	Push(context.Context, *logproto.WriteRequest) (*logproto.WriteResponse, error)
	Query(context.Context, *QueryRequest) (*QueryResponse, error)
	QueryStream(*QueryRequest, Ingester_QueryStreamServer) error
	QueryExemplars(context.Context, *ExemplarQueryRequest) (*ExemplarQueryResponse, error)
	LabelValues(context.Context, *LabelValuesRequest) (*LabelValuesResponse, error)
	LabelNames(context.Context, *LabelNamesRequest) (*LabelNamesResponse, error)
	UserStats(context.Context, *UserStatsRequest) (*UserStatsResponse, error)
	AllUserStats(context.Context, *UserStatsRequest) (*UsersStatsResponse, error)
	MetricsForLabelMatchers(context.Context, *MetricsForLabelMatchersRequest) (*MetricsForLabelMatchersResponse, error)
	MetricsMetadata(context.Context, *MetricsMetadataRequest) (*MetricsMetadataResponse, error)
	// TransferChunks allows leaving ingester (client) to stream chunks directly to joining ingesters (server).
	TransferChunks(Ingester_TransferChunksServer) error
}

// UnimplementedIngesterServer can be embedded to have forward compatible implementations.
type UnimplementedIngesterServer struct {
}

func (*UnimplementedIngesterServer) Push(ctx context.Context, req *logproto.WriteRequest) (*logproto.WriteResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Push not implemented")
}
func (*UnimplementedIngesterServer) Query(ctx context.Context, req *QueryRequest) (*QueryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Query not implemented")
}
func (*UnimplementedIngesterServer) QueryStream(req *QueryRequest, srv Ingester_QueryStreamServer) error {
	return status.Errorf(codes.Unimplemented, "method QueryStream not implemented")
}
func (*UnimplementedIngesterServer) QueryExemplars(ctx context.Context, req *ExemplarQueryRequest) (*ExemplarQueryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryExemplars not implemented")
}
func (*UnimplementedIngesterServer) LabelValues(ctx context.Context, req *LabelValuesRequest) (*LabelValuesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LabelValues not implemented")
}
func (*UnimplementedIngesterServer) LabelNames(ctx context.Context, req *LabelNamesRequest) (*LabelNamesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LabelNames not implemented")
}
func (*UnimplementedIngesterServer) UserStats(ctx context.Context, req *UserStatsRequest) (*UserStatsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UserStats not implemented")
}
func (*UnimplementedIngesterServer) AllUserStats(ctx context.Context, req *UserStatsRequest) (*UsersStatsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AllUserStats not implemented")
}
func (*UnimplementedIngesterServer) MetricsForLabelMatchers(ctx context.Context, req *MetricsForLabelMatchersRequest) (*MetricsForLabelMatchersResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MetricsForLabelMatchers not implemented")
}
func (*UnimplementedIngesterServer) MetricsMetadata(ctx context.Context, req *MetricsMetadataRequest) (*MetricsMetadataResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MetricsMetadata not implemented")
}
func (*UnimplementedIngesterServer) TransferChunks(srv Ingester_TransferChunksServer) error {
	return status.Errorf(codes.Unimplemented, "method TransferChunks not implemented")
}

func RegisterIngesterServer(s *grpc.Server, srv IngesterServer) {
	s.RegisterService(&_Ingester_serviceDesc, srv)
}

func _Ingester_Push_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(logproto.WriteRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(IngesterServer).Push(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ingesterpb.Ingester/Push",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(IngesterServer).Push(ctx, req.(*logproto.WriteRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ingester_Query_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(IngesterServer).Query(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ingesterpb.Ingester/Query",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(IngesterServer).Query(ctx, req.(*QueryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ingester_QueryStream_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(QueryRequest)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(IngesterServer).QueryStream(m, &ingesterQueryStreamServer{stream})
}

type Ingester_QueryStreamServer interface {
	Send(*QueryStreamResponse) error
	grpc.ServerStream
}

type ingesterQueryStreamServer struct {
	grpc.ServerStream
}

func (x *ingesterQueryStreamServer) Send(m *QueryStreamResponse) error {
	return x.ServerStream.SendMsg(m)
}

func _Ingester_QueryExemplars_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExemplarQueryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(IngesterServer).QueryExemplars(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ingesterpb.Ingester/QueryExemplars",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(IngesterServer).QueryExemplars(ctx, req.(*ExemplarQueryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ingester_LabelValues_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LabelValuesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(IngesterServer).LabelValues(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ingesterpb.Ingester/LabelValues",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(IngesterServer).LabelValues(ctx, req.(*LabelValuesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ingester_LabelNames_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LabelNamesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(IngesterServer).LabelNames(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ingesterpb.Ingester/LabelNames",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(IngesterServer).LabelNames(ctx, req.(*LabelNamesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ingester_UserStats_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserStatsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(IngesterServer).UserStats(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ingesterpb.Ingester/UserStats",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(IngesterServer).UserStats(ctx, req.(*UserStatsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ingester_AllUserStats_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserStatsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(IngesterServer).AllUserStats(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ingesterpb.Ingester/AllUserStats",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(IngesterServer).AllUserStats(ctx, req.(*UserStatsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ingester_MetricsForLabelMatchers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MetricsForLabelMatchersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(IngesterServer).MetricsForLabelMatchers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ingesterpb.Ingester/MetricsForLabelMatchers",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(IngesterServer).MetricsForLabelMatchers(ctx, req.(*MetricsForLabelMatchersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ingester_MetricsMetadata_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MetricsMetadataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(IngesterServer).MetricsMetadata(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ingesterpb.Ingester/MetricsMetadata",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(IngesterServer).MetricsMetadata(ctx, req.(*MetricsMetadataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ingester_TransferChunks_Handler(srv interface{}, stream grpc.ServerStream) error {
	return srv.(IngesterServer).TransferChunks(&ingesterTransferChunksServer{stream})
}

type Ingester_TransferChunksServer interface {
	SendAndClose(*TransferChunksResponse) error
	Recv() (*TimeSeriesChunk, error)
	grpc.ServerStream
}

type ingesterTransferChunksServer struct {
	grpc.ServerStream
}

func (x *ingesterTransferChunksServer) SendAndClose(m *TransferChunksResponse) error {
	return x.ServerStream.SendMsg(m)
}

func (x *ingesterTransferChunksServer) Recv() (*TimeSeriesChunk, error) {
	m := new(TimeSeriesChunk)
	if err := x.ServerStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

var _Ingester_serviceDesc = grpc.ServiceDesc{
	ServiceName: "ingesterpb.Ingester",
	HandlerType: (*IngesterServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Push",
			Handler:    _Ingester_Push_Handler,
		},
		{
			MethodName: "Query",
			Handler:    _Ingester_Query_Handler,
		},
		{
			MethodName: "QueryExemplars",
			Handler:    _Ingester_QueryExemplars_Handler,
		},
		{
			MethodName: "LabelValues",
			Handler:    _Ingester_LabelValues_Handler,
		},
		{
			MethodName: "LabelNames",
			Handler:    _Ingester_LabelNames_Handler,
		},
		{
			MethodName: "UserStats",
			Handler:    _Ingester_UserStats_Handler,
		},
		{
			MethodName: "AllUserStats",
			Handler:    _Ingester_AllUserStats_Handler,
		},
		{
			MethodName: "MetricsForLabelMatchers",
			Handler:    _Ingester_MetricsForLabelMatchers_Handler,
		},
		{
			MethodName: "MetricsMetadata",
			Handler:    _Ingester_MetricsMetadata_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "QueryStream",
			Handler:       _Ingester_QueryStream_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "TransferChunks",
			Handler:       _Ingester_TransferChunks_Handler,
			ClientStreams: true,
		},
	},
	Metadata: "pkg/ingester/client/ingester.proto",
}

func (m *ReadRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ReadRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ReadRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Queries) > 0 {
		for iNdEx := len(m.Queries) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Queries[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintIngester(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *ReadResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ReadResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ReadResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Results) > 0 {
		for iNdEx := len(m.Results) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Results[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintIngester(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *QueryRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *QueryRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *QueryRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Matchers) > 0 {
		for iNdEx := len(m.Matchers) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Matchers[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintIngester(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x1a
		}
	}
	if m.EndTimestampMs != 0 {
		i = encodeVarintIngester(dAtA, i, uint64(m.EndTimestampMs))
		i--
		dAtA[i] = 0x10
	}
	if m.StartTimestampMs != 0 {
		i = encodeVarintIngester(dAtA, i, uint64(m.StartTimestampMs))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *ExemplarQueryRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ExemplarQueryRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ExemplarQueryRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Matchers) > 0 {
		for iNdEx := len(m.Matchers) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Matchers[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintIngester(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x1a
		}
	}
	if m.EndTimestampMs != 0 {
		i = encodeVarintIngester(dAtA, i, uint64(m.EndTimestampMs))
		i--
		dAtA[i] = 0x10
	}
	if m.StartTimestampMs != 0 {
		i = encodeVarintIngester(dAtA, i, uint64(m.StartTimestampMs))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *QueryResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *QueryResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *QueryResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Timeseries) > 0 {
		for iNdEx := len(m.Timeseries) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Timeseries[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintIngester(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *QueryStreamResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *QueryStreamResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *QueryStreamResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Timeseries) > 0 {
		for iNdEx := len(m.Timeseries) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Timeseries[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintIngester(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x12
		}
	}
	if len(m.Chunkseries) > 0 {
		for iNdEx := len(m.Chunkseries) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Chunkseries[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintIngester(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *ExemplarQueryResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ExemplarQueryResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ExemplarQueryResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Timeseries) > 0 {
		for iNdEx := len(m.Timeseries) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Timeseries[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintIngester(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *LabelValuesRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *LabelValuesRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *LabelValuesRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Matchers != nil {
		{
			size, err := m.Matchers.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintIngester(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x22
	}
	if m.EndTimestampMs != 0 {
		i = encodeVarintIngester(dAtA, i, uint64(m.EndTimestampMs))
		i--
		dAtA[i] = 0x18
	}
	if m.StartTimestampMs != 0 {
		i = encodeVarintIngester(dAtA, i, uint64(m.StartTimestampMs))
		i--
		dAtA[i] = 0x10
	}
	if len(m.LabelName) > 0 {
		i -= len(m.LabelName)
		copy(dAtA[i:], m.LabelName)
		i = encodeVarintIngester(dAtA, i, uint64(len(m.LabelName)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *LabelValuesResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *LabelValuesResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *LabelValuesResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.LabelValues) > 0 {
		for iNdEx := len(m.LabelValues) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.LabelValues[iNdEx])
			copy(dAtA[i:], m.LabelValues[iNdEx])
			i = encodeVarintIngester(dAtA, i, uint64(len(m.LabelValues[iNdEx])))
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *LabelNamesRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *LabelNamesRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *LabelNamesRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.EndTimestampMs != 0 {
		i = encodeVarintIngester(dAtA, i, uint64(m.EndTimestampMs))
		i--
		dAtA[i] = 0x10
	}
	if m.StartTimestampMs != 0 {
		i = encodeVarintIngester(dAtA, i, uint64(m.StartTimestampMs))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *LabelNamesResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *LabelNamesResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *LabelNamesResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.LabelNames) > 0 {
		for iNdEx := len(m.LabelNames) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.LabelNames[iNdEx])
			copy(dAtA[i:], m.LabelNames[iNdEx])
			i = encodeVarintIngester(dAtA, i, uint64(len(m.LabelNames[iNdEx])))
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *UserStatsRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UserStatsRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *UserStatsRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	return len(dAtA) - i, nil
}

func (m *UserStatsResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UserStatsResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *UserStatsResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.RuleIngestionRate != 0 {
		i -= 8
		encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(math.Float64bits(float64(m.RuleIngestionRate))))
		i--
		dAtA[i] = 0x21
	}
	if m.ApiIngestionRate != 0 {
		i -= 8
		encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(math.Float64bits(float64(m.ApiIngestionRate))))
		i--
		dAtA[i] = 0x19
	}
	if m.NumSeries != 0 {
		i = encodeVarintIngester(dAtA, i, uint64(m.NumSeries))
		i--
		dAtA[i] = 0x10
	}
	if m.IngestionRate != 0 {
		i -= 8
		encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(math.Float64bits(float64(m.IngestionRate))))
		i--
		dAtA[i] = 0x9
	}
	return len(dAtA) - i, nil
}

func (m *UserIDStatsResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UserIDStatsResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *UserIDStatsResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Data != nil {
		{
			size, err := m.Data.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintIngester(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x12
	}
	if len(m.UserId) > 0 {
		i -= len(m.UserId)
		copy(dAtA[i:], m.UserId)
		i = encodeVarintIngester(dAtA, i, uint64(len(m.UserId)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *UsersStatsResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UsersStatsResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *UsersStatsResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Stats) > 0 {
		for iNdEx := len(m.Stats) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Stats[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintIngester(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *MetricsForLabelMatchersRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MetricsForLabelMatchersRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *MetricsForLabelMatchersRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.MatchersSet) > 0 {
		for iNdEx := len(m.MatchersSet) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.MatchersSet[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintIngester(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x1a
		}
	}
	if m.EndTimestampMs != 0 {
		i = encodeVarintIngester(dAtA, i, uint64(m.EndTimestampMs))
		i--
		dAtA[i] = 0x10
	}
	if m.StartTimestampMs != 0 {
		i = encodeVarintIngester(dAtA, i, uint64(m.StartTimestampMs))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *MetricsForLabelMatchersResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MetricsForLabelMatchersResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *MetricsForLabelMatchersResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Metric) > 0 {
		for iNdEx := len(m.Metric) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Metric[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintIngester(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *MetricsMetadataRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MetricsMetadataRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *MetricsMetadataRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	return len(dAtA) - i, nil
}

func (m *MetricsMetadataResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MetricsMetadataResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *MetricsMetadataResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Metadata) > 0 {
		for iNdEx := len(m.Metadata) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Metadata[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintIngester(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *TimeSeriesChunk) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TimeSeriesChunk) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *TimeSeriesChunk) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Chunks) > 0 {
		for iNdEx := len(m.Chunks) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Chunks[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintIngester(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x22
		}
	}
	if len(m.Labels) > 0 {
		for iNdEx := len(m.Labels) - 1; iNdEx >= 0; iNdEx-- {
			{
				size := m.Labels[iNdEx].Size()
				i -= size
				if _, err := m.Labels[iNdEx].MarshalTo(dAtA[i:]); err != nil {
					return 0, err
				}
				i = encodeVarintIngester(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x1a
		}
	}
	if len(m.UserId) > 0 {
		i -= len(m.UserId)
		copy(dAtA[i:], m.UserId)
		i = encodeVarintIngester(dAtA, i, uint64(len(m.UserId)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.FromIngesterId) > 0 {
		i -= len(m.FromIngesterId)
		copy(dAtA[i:], m.FromIngesterId)
		i = encodeVarintIngester(dAtA, i, uint64(len(m.FromIngesterId)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *Chunk) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Chunk) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Chunk) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Data) > 0 {
		i -= len(m.Data)
		copy(dAtA[i:], m.Data)
		i = encodeVarintIngester(dAtA, i, uint64(len(m.Data)))
		i--
		dAtA[i] = 0x22
	}
	if m.Encoding != 0 {
		i = encodeVarintIngester(dAtA, i, uint64(m.Encoding))
		i--
		dAtA[i] = 0x18
	}
	if m.EndTimestampMs != 0 {
		i = encodeVarintIngester(dAtA, i, uint64(m.EndTimestampMs))
		i--
		dAtA[i] = 0x10
	}
	if m.StartTimestampMs != 0 {
		i = encodeVarintIngester(dAtA, i, uint64(m.StartTimestampMs))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *TransferChunksResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TransferChunksResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *TransferChunksResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	return len(dAtA) - i, nil
}

func (m *LabelMatchers) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *LabelMatchers) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *LabelMatchers) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Matchers) > 0 {
		for iNdEx := len(m.Matchers) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Matchers[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintIngester(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *LabelMatcher) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *LabelMatcher) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *LabelMatcher) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Value) > 0 {
		i -= len(m.Value)
		copy(dAtA[i:], m.Value)
		i = encodeVarintIngester(dAtA, i, uint64(len(m.Value)))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.Name) > 0 {
		i -= len(m.Name)
		copy(dAtA[i:], m.Name)
		i = encodeVarintIngester(dAtA, i, uint64(len(m.Name)))
		i--
		dAtA[i] = 0x12
	}
	if m.Type != 0 {
		i = encodeVarintIngester(dAtA, i, uint64(m.Type))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *TimeSeriesFile) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TimeSeriesFile) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *TimeSeriesFile) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Data) > 0 {
		i -= len(m.Data)
		copy(dAtA[i:], m.Data)
		i = encodeVarintIngester(dAtA, i, uint64(len(m.Data)))
		i--
		dAtA[i] = 0x22
	}
	if len(m.Filename) > 0 {
		i -= len(m.Filename)
		copy(dAtA[i:], m.Filename)
		i = encodeVarintIngester(dAtA, i, uint64(len(m.Filename)))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.UserId) > 0 {
		i -= len(m.UserId)
		copy(dAtA[i:], m.UserId)
		i = encodeVarintIngester(dAtA, i, uint64(len(m.UserId)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.FromIngesterId) > 0 {
		i -= len(m.FromIngesterId)
		copy(dAtA[i:], m.FromIngesterId)
		i = encodeVarintIngester(dAtA, i, uint64(len(m.FromIngesterId)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func encodeVarintIngester(dAtA []byte, offset int, v uint64) int {
	offset -= sovIngester(v)
	base := offset
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return base
}
func (m *ReadRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Queries) > 0 {
		for _, e := range m.Queries {
			l = e.Size()
			n += 1 + l + sovIngester(uint64(l))
		}
	}
	return n
}

func (m *ReadResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Results) > 0 {
		for _, e := range m.Results {
			l = e.Size()
			n += 1 + l + sovIngester(uint64(l))
		}
	}
	return n
}

func (m *QueryRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.StartTimestampMs != 0 {
		n += 1 + sovIngester(uint64(m.StartTimestampMs))
	}
	if m.EndTimestampMs != 0 {
		n += 1 + sovIngester(uint64(m.EndTimestampMs))
	}
	if len(m.Matchers) > 0 {
		for _, e := range m.Matchers {
			l = e.Size()
			n += 1 + l + sovIngester(uint64(l))
		}
	}
	return n
}

func (m *ExemplarQueryRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.StartTimestampMs != 0 {
		n += 1 + sovIngester(uint64(m.StartTimestampMs))
	}
	if m.EndTimestampMs != 0 {
		n += 1 + sovIngester(uint64(m.EndTimestampMs))
	}
	if len(m.Matchers) > 0 {
		for _, e := range m.Matchers {
			l = e.Size()
			n += 1 + l + sovIngester(uint64(l))
		}
	}
	return n
}

func (m *QueryResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Timeseries) > 0 {
		for _, e := range m.Timeseries {
			l = e.Size()
			n += 1 + l + sovIngester(uint64(l))
		}
	}
	return n
}

func (m *QueryStreamResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Chunkseries) > 0 {
		for _, e := range m.Chunkseries {
			l = e.Size()
			n += 1 + l + sovIngester(uint64(l))
		}
	}
	if len(m.Timeseries) > 0 {
		for _, e := range m.Timeseries {
			l = e.Size()
			n += 1 + l + sovIngester(uint64(l))
		}
	}
	return n
}

func (m *ExemplarQueryResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Timeseries) > 0 {
		for _, e := range m.Timeseries {
			l = e.Size()
			n += 1 + l + sovIngester(uint64(l))
		}
	}
	return n
}

func (m *LabelValuesRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.LabelName)
	if l > 0 {
		n += 1 + l + sovIngester(uint64(l))
	}
	if m.StartTimestampMs != 0 {
		n += 1 + sovIngester(uint64(m.StartTimestampMs))
	}
	if m.EndTimestampMs != 0 {
		n += 1 + sovIngester(uint64(m.EndTimestampMs))
	}
	if m.Matchers != nil {
		l = m.Matchers.Size()
		n += 1 + l + sovIngester(uint64(l))
	}
	return n
}

func (m *LabelValuesResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.LabelValues) > 0 {
		for _, s := range m.LabelValues {
			l = len(s)
			n += 1 + l + sovIngester(uint64(l))
		}
	}
	return n
}

func (m *LabelNamesRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.StartTimestampMs != 0 {
		n += 1 + sovIngester(uint64(m.StartTimestampMs))
	}
	if m.EndTimestampMs != 0 {
		n += 1 + sovIngester(uint64(m.EndTimestampMs))
	}
	return n
}

func (m *LabelNamesResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.LabelNames) > 0 {
		for _, s := range m.LabelNames {
			l = len(s)
			n += 1 + l + sovIngester(uint64(l))
		}
	}
	return n
}

func (m *UserStatsRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	return n
}

func (m *UserStatsResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.IngestionRate != 0 {
		n += 9
	}
	if m.NumSeries != 0 {
		n += 1 + sovIngester(uint64(m.NumSeries))
	}
	if m.ApiIngestionRate != 0 {
		n += 9
	}
	if m.RuleIngestionRate != 0 {
		n += 9
	}
	return n
}

func (m *UserIDStatsResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.UserId)
	if l > 0 {
		n += 1 + l + sovIngester(uint64(l))
	}
	if m.Data != nil {
		l = m.Data.Size()
		n += 1 + l + sovIngester(uint64(l))
	}
	return n
}

func (m *UsersStatsResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Stats) > 0 {
		for _, e := range m.Stats {
			l = e.Size()
			n += 1 + l + sovIngester(uint64(l))
		}
	}
	return n
}

func (m *MetricsForLabelMatchersRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.StartTimestampMs != 0 {
		n += 1 + sovIngester(uint64(m.StartTimestampMs))
	}
	if m.EndTimestampMs != 0 {
		n += 1 + sovIngester(uint64(m.EndTimestampMs))
	}
	if len(m.MatchersSet) > 0 {
		for _, e := range m.MatchersSet {
			l = e.Size()
			n += 1 + l + sovIngester(uint64(l))
		}
	}
	return n
}

func (m *MetricsForLabelMatchersResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Metric) > 0 {
		for _, e := range m.Metric {
			l = e.Size()
			n += 1 + l + sovIngester(uint64(l))
		}
	}
	return n
}

func (m *MetricsMetadataRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	return n
}

func (m *MetricsMetadataResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Metadata) > 0 {
		for _, e := range m.Metadata {
			l = e.Size()
			n += 1 + l + sovIngester(uint64(l))
		}
	}
	return n
}

func (m *TimeSeriesChunk) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.FromIngesterId)
	if l > 0 {
		n += 1 + l + sovIngester(uint64(l))
	}
	l = len(m.UserId)
	if l > 0 {
		n += 1 + l + sovIngester(uint64(l))
	}
	if len(m.Labels) > 0 {
		for _, e := range m.Labels {
			l = e.Size()
			n += 1 + l + sovIngester(uint64(l))
		}
	}
	if len(m.Chunks) > 0 {
		for _, e := range m.Chunks {
			l = e.Size()
			n += 1 + l + sovIngester(uint64(l))
		}
	}
	return n
}

func (m *Chunk) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.StartTimestampMs != 0 {
		n += 1 + sovIngester(uint64(m.StartTimestampMs))
	}
	if m.EndTimestampMs != 0 {
		n += 1 + sovIngester(uint64(m.EndTimestampMs))
	}
	if m.Encoding != 0 {
		n += 1 + sovIngester(uint64(m.Encoding))
	}
	l = len(m.Data)
	if l > 0 {
		n += 1 + l + sovIngester(uint64(l))
	}
	return n
}

func (m *TransferChunksResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	return n
}

func (m *LabelMatchers) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Matchers) > 0 {
		for _, e := range m.Matchers {
			l = e.Size()
			n += 1 + l + sovIngester(uint64(l))
		}
	}
	return n
}

func (m *LabelMatcher) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Type != 0 {
		n += 1 + sovIngester(uint64(m.Type))
	}
	l = len(m.Name)
	if l > 0 {
		n += 1 + l + sovIngester(uint64(l))
	}
	l = len(m.Value)
	if l > 0 {
		n += 1 + l + sovIngester(uint64(l))
	}
	return n
}

func (m *TimeSeriesFile) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.FromIngesterId)
	if l > 0 {
		n += 1 + l + sovIngester(uint64(l))
	}
	l = len(m.UserId)
	if l > 0 {
		n += 1 + l + sovIngester(uint64(l))
	}
	l = len(m.Filename)
	if l > 0 {
		n += 1 + l + sovIngester(uint64(l))
	}
	l = len(m.Data)
	if l > 0 {
		n += 1 + l + sovIngester(uint64(l))
	}
	return n
}

func sovIngester(x uint64) (n int) {
	return (math_bits.Len64(x|1) + 6) / 7
}
func sozIngester(x uint64) (n int) {
	return sovIngester(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (this *ReadRequest) String() string {
	if this == nil {
		return "nil"
	}
	repeatedStringForQueries := "[]*QueryRequest{"
	for _, f := range this.Queries {
		repeatedStringForQueries += strings.Replace(f.String(), "QueryRequest", "QueryRequest", 1) + ","
	}
	repeatedStringForQueries += "}"
	s := strings.Join([]string{`&ReadRequest{`,
		`Queries:` + repeatedStringForQueries + `,`,
		`}`,
	}, "")
	return s
}
func (this *ReadResponse) String() string {
	if this == nil {
		return "nil"
	}
	repeatedStringForResults := "[]*QueryResponse{"
	for _, f := range this.Results {
		repeatedStringForResults += strings.Replace(f.String(), "QueryResponse", "QueryResponse", 1) + ","
	}
	repeatedStringForResults += "}"
	s := strings.Join([]string{`&ReadResponse{`,
		`Results:` + repeatedStringForResults + `,`,
		`}`,
	}, "")
	return s
}
func (this *QueryRequest) String() string {
	if this == nil {
		return "nil"
	}
	repeatedStringForMatchers := "[]*LabelMatcher{"
	for _, f := range this.Matchers {
		repeatedStringForMatchers += strings.Replace(f.String(), "LabelMatcher", "LabelMatcher", 1) + ","
	}
	repeatedStringForMatchers += "}"
	s := strings.Join([]string{`&QueryRequest{`,
		`StartTimestampMs:` + fmt.Sprintf("%v", this.StartTimestampMs) + `,`,
		`EndTimestampMs:` + fmt.Sprintf("%v", this.EndTimestampMs) + `,`,
		`Matchers:` + repeatedStringForMatchers + `,`,
		`}`,
	}, "")
	return s
}
func (this *ExemplarQueryRequest) String() string {
	if this == nil {
		return "nil"
	}
	repeatedStringForMatchers := "[]*LabelMatchers{"
	for _, f := range this.Matchers {
		repeatedStringForMatchers += strings.Replace(f.String(), "LabelMatchers", "LabelMatchers", 1) + ","
	}
	repeatedStringForMatchers += "}"
	s := strings.Join([]string{`&ExemplarQueryRequest{`,
		`StartTimestampMs:` + fmt.Sprintf("%v", this.StartTimestampMs) + `,`,
		`EndTimestampMs:` + fmt.Sprintf("%v", this.EndTimestampMs) + `,`,
		`Matchers:` + repeatedStringForMatchers + `,`,
		`}`,
	}, "")
	return s
}
func (this *QueryResponse) String() string {
	if this == nil {
		return "nil"
	}
	repeatedStringForTimeseries := "[]TimeSeries{"
	for _, f := range this.Timeseries {
		repeatedStringForTimeseries += fmt.Sprintf("%v", f) + ","
	}
	repeatedStringForTimeseries += "}"
	s := strings.Join([]string{`&QueryResponse{`,
		`Timeseries:` + repeatedStringForTimeseries + `,`,
		`}`,
	}, "")
	return s
}
func (this *QueryStreamResponse) String() string {
	if this == nil {
		return "nil"
	}
	repeatedStringForChunkseries := "[]TimeSeriesChunk{"
	for _, f := range this.Chunkseries {
		repeatedStringForChunkseries += strings.Replace(strings.Replace(f.String(), "TimeSeriesChunk", "TimeSeriesChunk", 1), `&`, ``, 1) + ","
	}
	repeatedStringForChunkseries += "}"
	repeatedStringForTimeseries := "[]TimeSeries{"
	for _, f := range this.Timeseries {
		repeatedStringForTimeseries += fmt.Sprintf("%v", f) + ","
	}
	repeatedStringForTimeseries += "}"
	s := strings.Join([]string{`&QueryStreamResponse{`,
		`Chunkseries:` + repeatedStringForChunkseries + `,`,
		`Timeseries:` + repeatedStringForTimeseries + `,`,
		`}`,
	}, "")
	return s
}
func (this *ExemplarQueryResponse) String() string {
	if this == nil {
		return "nil"
	}
	repeatedStringForTimeseries := "[]TimeSeries{"
	for _, f := range this.Timeseries {
		repeatedStringForTimeseries += fmt.Sprintf("%v", f) + ","
	}
	repeatedStringForTimeseries += "}"
	s := strings.Join([]string{`&ExemplarQueryResponse{`,
		`Timeseries:` + repeatedStringForTimeseries + `,`,
		`}`,
	}, "")
	return s
}
func (this *LabelValuesRequest) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&LabelValuesRequest{`,
		`LabelName:` + fmt.Sprintf("%v", this.LabelName) + `,`,
		`StartTimestampMs:` + fmt.Sprintf("%v", this.StartTimestampMs) + `,`,
		`EndTimestampMs:` + fmt.Sprintf("%v", this.EndTimestampMs) + `,`,
		`Matchers:` + strings.Replace(this.Matchers.String(), "LabelMatchers", "LabelMatchers", 1) + `,`,
		`}`,
	}, "")
	return s
}
func (this *LabelValuesResponse) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&LabelValuesResponse{`,
		`LabelValues:` + fmt.Sprintf("%v", this.LabelValues) + `,`,
		`}`,
	}, "")
	return s
}
func (this *LabelNamesRequest) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&LabelNamesRequest{`,
		`StartTimestampMs:` + fmt.Sprintf("%v", this.StartTimestampMs) + `,`,
		`EndTimestampMs:` + fmt.Sprintf("%v", this.EndTimestampMs) + `,`,
		`}`,
	}, "")
	return s
}
func (this *LabelNamesResponse) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&LabelNamesResponse{`,
		`LabelNames:` + fmt.Sprintf("%v", this.LabelNames) + `,`,
		`}`,
	}, "")
	return s
}
func (this *UserStatsRequest) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&UserStatsRequest{`,
		`}`,
	}, "")
	return s
}
func (this *UserStatsResponse) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&UserStatsResponse{`,
		`IngestionRate:` + fmt.Sprintf("%v", this.IngestionRate) + `,`,
		`NumSeries:` + fmt.Sprintf("%v", this.NumSeries) + `,`,
		`ApiIngestionRate:` + fmt.Sprintf("%v", this.ApiIngestionRate) + `,`,
		`RuleIngestionRate:` + fmt.Sprintf("%v", this.RuleIngestionRate) + `,`,
		`}`,
	}, "")
	return s
}
func (this *UserIDStatsResponse) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&UserIDStatsResponse{`,
		`UserId:` + fmt.Sprintf("%v", this.UserId) + `,`,
		`Data:` + strings.Replace(this.Data.String(), "UserStatsResponse", "UserStatsResponse", 1) + `,`,
		`}`,
	}, "")
	return s
}
func (this *UsersStatsResponse) String() string {
	if this == nil {
		return "nil"
	}
	repeatedStringForStats := "[]*UserIDStatsResponse{"
	for _, f := range this.Stats {
		repeatedStringForStats += strings.Replace(f.String(), "UserIDStatsResponse", "UserIDStatsResponse", 1) + ","
	}
	repeatedStringForStats += "}"
	s := strings.Join([]string{`&UsersStatsResponse{`,
		`Stats:` + repeatedStringForStats + `,`,
		`}`,
	}, "")
	return s
}
func (this *MetricsForLabelMatchersRequest) String() string {
	if this == nil {
		return "nil"
	}
	repeatedStringForMatchersSet := "[]*LabelMatchers{"
	for _, f := range this.MatchersSet {
		repeatedStringForMatchersSet += strings.Replace(f.String(), "LabelMatchers", "LabelMatchers", 1) + ","
	}
	repeatedStringForMatchersSet += "}"
	s := strings.Join([]string{`&MetricsForLabelMatchersRequest{`,
		`StartTimestampMs:` + fmt.Sprintf("%v", this.StartTimestampMs) + `,`,
		`EndTimestampMs:` + fmt.Sprintf("%v", this.EndTimestampMs) + `,`,
		`MatchersSet:` + repeatedStringForMatchersSet + `,`,
		`}`,
	}, "")
	return s
}
func (this *MetricsForLabelMatchersResponse) String() string {
	if this == nil {
		return "nil"
	}
	repeatedStringForMetric := "[]*Metric{"
	for _, f := range this.Metric {
		repeatedStringForMetric += strings.Replace(fmt.Sprintf("%v", f), "Metric", "logproto.Metric", 1) + ","
	}
	repeatedStringForMetric += "}"
	s := strings.Join([]string{`&MetricsForLabelMatchersResponse{`,
		`Metric:` + repeatedStringForMetric + `,`,
		`}`,
	}, "")
	return s
}
func (this *MetricsMetadataRequest) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&MetricsMetadataRequest{`,
		`}`,
	}, "")
	return s
}
func (this *MetricsMetadataResponse) String() string {
	if this == nil {
		return "nil"
	}
	repeatedStringForMetadata := "[]*MetricMetadata{"
	for _, f := range this.Metadata {
		repeatedStringForMetadata += strings.Replace(fmt.Sprintf("%v", f), "MetricMetadata", "logproto.MetricMetadata", 1) + ","
	}
	repeatedStringForMetadata += "}"
	s := strings.Join([]string{`&MetricsMetadataResponse{`,
		`Metadata:` + repeatedStringForMetadata + `,`,
		`}`,
	}, "")
	return s
}
func (this *TimeSeriesChunk) String() string {
	if this == nil {
		return "nil"
	}
	repeatedStringForChunks := "[]Chunk{"
	for _, f := range this.Chunks {
		repeatedStringForChunks += strings.Replace(strings.Replace(f.String(), "Chunk", "Chunk", 1), `&`, ``, 1) + ","
	}
	repeatedStringForChunks += "}"
	s := strings.Join([]string{`&TimeSeriesChunk{`,
		`FromIngesterId:` + fmt.Sprintf("%v", this.FromIngesterId) + `,`,
		`UserId:` + fmt.Sprintf("%v", this.UserId) + `,`,
		`Labels:` + fmt.Sprintf("%v", this.Labels) + `,`,
		`Chunks:` + repeatedStringForChunks + `,`,
		`}`,
	}, "")
	return s
}
func (this *Chunk) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&Chunk{`,
		`StartTimestampMs:` + fmt.Sprintf("%v", this.StartTimestampMs) + `,`,
		`EndTimestampMs:` + fmt.Sprintf("%v", this.EndTimestampMs) + `,`,
		`Encoding:` + fmt.Sprintf("%v", this.Encoding) + `,`,
		`Data:` + fmt.Sprintf("%v", this.Data) + `,`,
		`}`,
	}, "")
	return s
}
func (this *TransferChunksResponse) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&TransferChunksResponse{`,
		`}`,
	}, "")
	return s
}
func (this *LabelMatchers) String() string {
	if this == nil {
		return "nil"
	}
	repeatedStringForMatchers := "[]*LabelMatcher{"
	for _, f := range this.Matchers {
		repeatedStringForMatchers += strings.Replace(f.String(), "LabelMatcher", "LabelMatcher", 1) + ","
	}
	repeatedStringForMatchers += "}"
	s := strings.Join([]string{`&LabelMatchers{`,
		`Matchers:` + repeatedStringForMatchers + `,`,
		`}`,
	}, "")
	return s
}
func (this *LabelMatcher) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&LabelMatcher{`,
		`Type:` + fmt.Sprintf("%v", this.Type) + `,`,
		`Name:` + fmt.Sprintf("%v", this.Name) + `,`,
		`Value:` + fmt.Sprintf("%v", this.Value) + `,`,
		`}`,
	}, "")
	return s
}
func (this *TimeSeriesFile) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&TimeSeriesFile{`,
		`FromIngesterId:` + fmt.Sprintf("%v", this.FromIngesterId) + `,`,
		`UserId:` + fmt.Sprintf("%v", this.UserId) + `,`,
		`Filename:` + fmt.Sprintf("%v", this.Filename) + `,`,
		`Data:` + fmt.Sprintf("%v", this.Data) + `,`,
		`}`,
	}, "")
	return s
}
func valueToStringIngester(v interface{}) string {
	rv := reflect.ValueOf(v)
	if rv.IsNil() {
		return "nil"
	}
	pv := reflect.Indirect(rv).Interface()
	return fmt.Sprintf("*%v", pv)
}
func (m *ReadRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowIngester
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ReadRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ReadRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Queries", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIngester
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthIngester
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthIngester
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Queries = append(m.Queries, &QueryRequest{})
			if err := m.Queries[len(m.Queries)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipIngester(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthIngester
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthIngester
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ReadResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowIngester
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ReadResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ReadResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Results", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIngester
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthIngester
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthIngester
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Results = append(m.Results, &QueryResponse{})
			if err := m.Results[len(m.Results)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipIngester(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthIngester
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthIngester
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *QueryRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowIngester
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: QueryRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: QueryRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field StartTimestampMs", wireType)
			}
			m.StartTimestampMs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIngester
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StartTimestampMs |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field EndTimestampMs", wireType)
			}
			m.EndTimestampMs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIngester
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.EndTimestampMs |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Matchers", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIngester
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthIngester
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthIngester
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Matchers = append(m.Matchers, &LabelMatcher{})
			if err := m.Matchers[len(m.Matchers)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipIngester(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthIngester
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthIngester
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ExemplarQueryRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowIngester
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ExemplarQueryRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ExemplarQueryRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field StartTimestampMs", wireType)
			}
			m.StartTimestampMs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIngester
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StartTimestampMs |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field EndTimestampMs", wireType)
			}
			m.EndTimestampMs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIngester
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.EndTimestampMs |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Matchers", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIngester
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthIngester
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthIngester
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Matchers = append(m.Matchers, &LabelMatchers{})
			if err := m.Matchers[len(m.Matchers)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipIngester(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthIngester
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthIngester
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *QueryResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowIngester
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: QueryResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: QueryResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Timeseries", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIngester
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthIngester
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthIngester
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Timeseries = append(m.Timeseries, logproto.TimeSeries{})
			if err := m.Timeseries[len(m.Timeseries)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipIngester(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthIngester
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthIngester
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *QueryStreamResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowIngester
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: QueryStreamResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: QueryStreamResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Chunkseries", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIngester
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthIngester
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthIngester
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Chunkseries = append(m.Chunkseries, TimeSeriesChunk{})
			if err := m.Chunkseries[len(m.Chunkseries)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Timeseries", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIngester
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthIngester
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthIngester
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Timeseries = append(m.Timeseries, logproto.TimeSeries{})
			if err := m.Timeseries[len(m.Timeseries)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipIngester(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthIngester
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthIngester
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ExemplarQueryResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowIngester
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ExemplarQueryResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ExemplarQueryResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Timeseries", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIngester
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthIngester
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthIngester
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Timeseries = append(m.Timeseries, logproto.TimeSeries{})
			if err := m.Timeseries[len(m.Timeseries)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipIngester(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthIngester
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthIngester
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *LabelValuesRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowIngester
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: LabelValuesRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: LabelValuesRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field LabelName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIngester
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthIngester
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthIngester
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.LabelName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field StartTimestampMs", wireType)
			}
			m.StartTimestampMs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIngester
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StartTimestampMs |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field EndTimestampMs", wireType)
			}
			m.EndTimestampMs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIngester
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.EndTimestampMs |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Matchers", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIngester
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthIngester
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthIngester
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Matchers == nil {
				m.Matchers = &LabelMatchers{}
			}
			if err := m.Matchers.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipIngester(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthIngester
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthIngester
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *LabelValuesResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowIngester
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: LabelValuesResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: LabelValuesResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field LabelValues", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIngester
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthIngester
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthIngester
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.LabelValues = append(m.LabelValues, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipIngester(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthIngester
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthIngester
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *LabelNamesRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowIngester
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: LabelNamesRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: LabelNamesRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field StartTimestampMs", wireType)
			}
			m.StartTimestampMs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIngester
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StartTimestampMs |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field EndTimestampMs", wireType)
			}
			m.EndTimestampMs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIngester
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.EndTimestampMs |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipIngester(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthIngester
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthIngester
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *LabelNamesResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowIngester
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: LabelNamesResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: LabelNamesResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field LabelNames", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIngester
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthIngester
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthIngester
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.LabelNames = append(m.LabelNames, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipIngester(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthIngester
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthIngester
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *UserStatsRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowIngester
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: UserStatsRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: UserStatsRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipIngester(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthIngester
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthIngester
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *UserStatsResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowIngester
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: UserStatsResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: UserStatsResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 1 {
				return fmt.Errorf("proto: wrong wireType = %d for field IngestionRate", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io.ErrUnexpectedEOF
			}
			v = uint64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
			iNdEx += 8
			m.IngestionRate = float64(math.Float64frombits(v))
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field NumSeries", wireType)
			}
			m.NumSeries = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIngester
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.NumSeries |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 1 {
				return fmt.Errorf("proto: wrong wireType = %d for field ApiIngestionRate", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io.ErrUnexpectedEOF
			}
			v = uint64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
			iNdEx += 8
			m.ApiIngestionRate = float64(math.Float64frombits(v))
		case 4:
			if wireType != 1 {
				return fmt.Errorf("proto: wrong wireType = %d for field RuleIngestionRate", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io.ErrUnexpectedEOF
			}
			v = uint64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
			iNdEx += 8
			m.RuleIngestionRate = float64(math.Float64frombits(v))
		default:
			iNdEx = preIndex
			skippy, err := skipIngester(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthIngester
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthIngester
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *UserIDStatsResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowIngester
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: UserIDStatsResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: UserIDStatsResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field UserId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIngester
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthIngester
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthIngester
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.UserId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Data", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIngester
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthIngester
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthIngester
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Data == nil {
				m.Data = &UserStatsResponse{}
			}
			if err := m.Data.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipIngester(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthIngester
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthIngester
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *UsersStatsResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowIngester
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: UsersStatsResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: UsersStatsResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Stats", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIngester
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthIngester
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthIngester
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Stats = append(m.Stats, &UserIDStatsResponse{})
			if err := m.Stats[len(m.Stats)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipIngester(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthIngester
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthIngester
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *MetricsForLabelMatchersRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowIngester
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: MetricsForLabelMatchersRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: MetricsForLabelMatchersRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field StartTimestampMs", wireType)
			}
			m.StartTimestampMs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIngester
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StartTimestampMs |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field EndTimestampMs", wireType)
			}
			m.EndTimestampMs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIngester
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.EndTimestampMs |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field MatchersSet", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIngester
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthIngester
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthIngester
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.MatchersSet = append(m.MatchersSet, &LabelMatchers{})
			if err := m.MatchersSet[len(m.MatchersSet)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipIngester(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthIngester
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthIngester
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *MetricsForLabelMatchersResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowIngester
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: MetricsForLabelMatchersResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: MetricsForLabelMatchersResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Metric", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIngester
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthIngester
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthIngester
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Metric = append(m.Metric, &logproto.Metric{})
			if err := m.Metric[len(m.Metric)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipIngester(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthIngester
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthIngester
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *MetricsMetadataRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowIngester
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: MetricsMetadataRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: MetricsMetadataRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipIngester(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthIngester
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthIngester
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *MetricsMetadataResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowIngester
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: MetricsMetadataResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: MetricsMetadataResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Metadata", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIngester
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthIngester
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthIngester
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Metadata = append(m.Metadata, &logproto.MetricMetadata{})
			if err := m.Metadata[len(m.Metadata)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipIngester(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthIngester
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthIngester
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *TimeSeriesChunk) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowIngester
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: TimeSeriesChunk: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: TimeSeriesChunk: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field FromIngesterId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIngester
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthIngester
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthIngester
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.FromIngesterId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field UserId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIngester
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthIngester
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthIngester
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.UserId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Labels", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIngester
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthIngester
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthIngester
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Labels = append(m.Labels, github_com_grafana_loki_pkg_logproto.LabelAdapter{})
			if err := m.Labels[len(m.Labels)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Chunks", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIngester
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthIngester
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthIngester
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Chunks = append(m.Chunks, Chunk{})
			if err := m.Chunks[len(m.Chunks)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipIngester(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthIngester
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthIngester
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Chunk) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowIngester
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Chunk: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Chunk: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field StartTimestampMs", wireType)
			}
			m.StartTimestampMs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIngester
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StartTimestampMs |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field EndTimestampMs", wireType)
			}
			m.EndTimestampMs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIngester
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.EndTimestampMs |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Encoding", wireType)
			}
			m.Encoding = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIngester
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Encoding |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Data", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIngester
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthIngester
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthIngester
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Data = append(m.Data[:0], dAtA[iNdEx:postIndex]...)
			if m.Data == nil {
				m.Data = []byte{}
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipIngester(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthIngester
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthIngester
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *TransferChunksResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowIngester
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: TransferChunksResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: TransferChunksResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipIngester(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthIngester
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthIngester
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *LabelMatchers) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowIngester
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: LabelMatchers: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: LabelMatchers: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Matchers", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIngester
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthIngester
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthIngester
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Matchers = append(m.Matchers, &LabelMatcher{})
			if err := m.Matchers[len(m.Matchers)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipIngester(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthIngester
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthIngester
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *LabelMatcher) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowIngester
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: LabelMatcher: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: LabelMatcher: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIngester
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= MatchType(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIngester
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthIngester
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthIngester
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Value", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIngester
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthIngester
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthIngester
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Value = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipIngester(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthIngester
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthIngester
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *TimeSeriesFile) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowIngester
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: TimeSeriesFile: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: TimeSeriesFile: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field FromIngesterId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIngester
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthIngester
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthIngester
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.FromIngesterId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field UserId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIngester
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthIngester
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthIngester
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.UserId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Filename", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIngester
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthIngester
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthIngester
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Filename = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Data", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIngester
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthIngester
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthIngester
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Data = append(m.Data[:0], dAtA[iNdEx:postIndex]...)
			if m.Data == nil {
				m.Data = []byte{}
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipIngester(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthIngester
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthIngester
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipIngester(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowIngester
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowIngester
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowIngester
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if length < 0 {
				return 0, ErrInvalidLengthIngester
			}
			iNdEx += length
			if iNdEx < 0 {
				return 0, ErrInvalidLengthIngester
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start int = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowIngester
					}
					if iNdEx >= l {
						return 0, io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipIngester(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
				if iNdEx < 0 {
					return 0, ErrInvalidLengthIngester
				}
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthIngester = fmt.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowIngester   = fmt.Errorf("proto: integer overflow")
)
