resources:
- ../../crd
- ../../rbac
- ../../manager
- ./minio

# Adds namespace to all resources.
namespace: default

labels:
- pairs:
    app.kubernetes.io/name: loki-operator
    app.kubernetes.io/part-of: loki-operator
    app.kubernetes.io/managed-by: operator-lifecycle-manager
  includeSelectors: true
- pairs:
    app.kubernetes.io/instance: loki-operator-v0.0.1
    app.kubernetes.io/version: "0.0.1"

patchesStrategicMerge:
- manager_run_flags_patch.yaml
- manager_related_image_patch.yaml
- manager_image_pull_policy_patch.yaml
