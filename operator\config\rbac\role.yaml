---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  creationTimestamp: null
  name: manager-role
rules:
- apiGroups:
  - ""
  resources:
  - configmaps
  - endpoints
  - nodes
  - pods
  - serviceaccounts
  - services
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - ""
  resources:
  - namespaces
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - ""
  resources:
  - secrets
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - apps
  resources:
  - deployments
  - statefulsets
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - config.openshift.io
  resources:
  - dnses
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - coordination.k8s.io
  resources:
  - leases
  verbs:
  - create
  - get
  - update
- apiGroups:
  - loki.grafana.com
  resources:
  - alertingrules
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - loki.grafana.com
  resources:
  - alertingrules/finalizers
  verbs:
  - update
- apiGroups:
  - loki.grafana.com
  resources:
  - alertingrules/status
  verbs:
  - get
  - patch
  - update
- apiGroups:
  - loki.grafana.com
  resources:
  - lokistacks
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - loki.grafana.com
  resources:
  - lokistacks/finalizers
  verbs:
  - update
- apiGroups:
  - loki.grafana.com
  resources:
  - lokistacks/status
  verbs:
  - get
  - patch
  - update
- apiGroups:
  - loki.grafana.com
  resources:
  - recordingrules
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - loki.grafana.com
  resources:
  - recordingrules/finalizers
  verbs:
  - update
- apiGroups:
  - loki.grafana.com
  resources:
  - recordingrules/status
  verbs:
  - get
  - patch
  - update
- apiGroups:
  - loki.grafana.com
  resources:
  - rulerconfigs
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - loki.grafana.com
  resources:
  - rulerconfigs/finalizers
  verbs:
  - update
- apiGroups:
  - loki.grafana.com
  resources:
  - rulerconfigs/status
  verbs:
  - get
  - patch
  - update
- apiGroups:
  - monitoring.coreos.com
  resources:
  - prometheusrules
  - servicemonitors
  verbs:
  - create
  - get
  - list
  - update
  - watch
- apiGroups:
  - networking.k8s.io
  resources:
  - ingresses
  verbs:
  - create
  - get
  - list
  - update
  - watch
- apiGroups:
  - rbac.authorization.k8s.io
  resources:
  - clusterrolebindings
  - clusterroles
  - rolebindings
  - roles
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - route.openshift.io
  resources:
  - routes
  verbs:
  - create
  - get
  - list
  - update
  - watch
