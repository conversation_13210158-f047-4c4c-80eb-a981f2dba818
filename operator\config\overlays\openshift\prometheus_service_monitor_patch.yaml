apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  labels:
    name: loki-operator
  name: metrics-monitor
spec:
  endpoints:
    - bearerTokenFile: /var/run/secrets/kubernetes.io/serviceaccount/token
      path: /metrics
      targetPort: 8443
      scheme: https
      interval: 30s
      scrapeTimeout: 10s
      tlsConfig:
        caFile: /etc/prometheus/configmaps/serving-certs-ca-bundle/service-ca.crt
        serverName: loki-operator-controller-manager-metrics-service.openshift-operators-redhat.svc
