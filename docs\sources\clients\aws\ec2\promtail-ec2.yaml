server:
  http_listen_port: 3100
  grpc_listen_port: 0

clients:
  - url: https://<user id>:<api secret>@logs-prod-us-central1.grafana.net/loki/api/v1/push

positions:
  filename: /opt/promtail/positions.yaml

scrape_configs:
  - job_name: ec2-logs
    ec2_sd_configs:
      - region: us-east-2
        access_key: REDACTED
        secret_key: REDACTED
    relabel_configs:
      - source_labels: [__meta_ec2_tag_Name]
        target_label: name
        action: replace
      - source_labels: [__meta_ec2_instance_id]
        target_label: instance
        action: replace
      - source_labels: [__meta_ec2_availability_zone]
        target_label: zone
        action: replace
      - action: replace
        replacement: /var/log/**.log
        target_label: __path__
      - source_labels: [__meta_ec2_private_dns_name]
        regex: "(.*)"
        target_label: __host__
