version: "3"

networks:
  loki:

services:
  loki:
    # this is required according to https://github.com/Microsoft/vscode-go/wiki/Debugging-Go-code-using-VS-Code#linuxdocker
    security_opt:
      - seccomp:unconfined
    image: grafana/loki-debug:latest
    ports:
      - "40000:40000"
      - "3100:3100"
    command: -config.file=/etc/loki/local-config.yaml
    networks:
      - loki

  promtail:
    # this is required according to https://github.com/Microsoft/vscode-go/wiki/Debugging-Go-code-using-VS-Code#linuxdocker
    security_opt:
      - seccomp:unconfined
    image: grafana/promtail-debug:latest
    ports:
      - "40100:40000"
    volumes:
      - /var/log:/var/log
    command: -config.file=/etc/promtail/docker-config.yaml
    networks:
      - loki

  grafana:
    image: grafana/grafana:master
    ports:
      - "3000:3000"
    networks:
      - loki
