package main

import (
	"context"
	"fmt"
	"time"

	"github.com/grafana/dskit/ring"
	"github.com/grafana/dskit/kv"
	"github.com/grafana/dskit/kv/consul"
)

func main() {
	// 模拟 "at least 1 live replicas required, could only find 0" 错误
	
	// 1. 创建 Ring 配置
	ringConfig := ring.Config{
		KVStore: kv.Config{
			Store: "consul",
			Consul: consul.Config{
				Host: "localhost:8500",
			},
		},
		HeartbeatTimeout:  5 * time.Second, // 设置短超时
		ReplicationFactor: 1,
	}

	// 2. 创建 Ring 实例
	r, err := ring.New(ringConfig, "test-ring", "test-key", nil, nil, nil)
	if err != nil {
		panic(err)
	}

	// 3. 创建一个过期的实例描述
	expiredInstance := ring.InstanceDesc{
		Addr:      "127.0.0.1:9095",
		Timestamp: time.Now().Add(-10 * time.Second).Unix(), // 10秒前的时间戳
		State:     ring.ACTIVE,
		Tokens:    []uint32{1000},
	}

	// 4. 手动构造实例列表
	instances := []ring.InstanceDesc{expiredInstance}

	// 5. 创建 replication strategy
	strategy := ring.NewDefaultReplicationStrategy()

	// 6. 调用 Filter 方法，应该会返回错误
	healthyInstances, maxFailures, err := strategy.Filter(
		instances,
		ring.Write, // 写操作
		ringConfig.ReplicationFactor,
		ringConfig.HeartbeatTimeout,
		false, // zone awareness disabled
	)

	// 7. 检查结果
	if err != nil {
		fmt.Printf("成功触发错误: %v\n", err)
		fmt.Printf("健康实例数: %d\n", len(healthyInstances))
		fmt.Printf("最大失败数: %d\n", maxFailures)
	} else {
		fmt.Printf("未触发错误，健康实例数: %d\n", len(healthyInstances))
	}
}
