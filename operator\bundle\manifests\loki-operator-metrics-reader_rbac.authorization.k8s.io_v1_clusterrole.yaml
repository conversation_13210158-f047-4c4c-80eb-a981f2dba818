apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  creationTimestamp: null
  labels:
    app.kubernetes.io/instance: loki-operator-v0.0.1
    app.kubernetes.io/managed-by: operator-lifecycle-manager
    app.kubernetes.io/name: loki-operator
    app.kubernetes.io/part-of: cluster-logging
    app.kubernetes.io/version: 0.0.1
  name: loki-operator-metrics-reader
rules:
- nonResourceURLs:
  - /metrics
  verbs:
  - get
