input {
    beats {
        port => 5044
    }
}

output {
  loki {
    url => "http://localhost:3100/loki/api/v1/push"

    #tenant_id => "fake" #default none

    #message_field => "message" #default message

    # If include_fields is set, only fields in this list will be sent to <PERSON> as labels.
    #include_fields => ["service","host","app","env"] #default empty array, all labels included.

    #batch_wait => 1 ## in seconds #default 1 second

    #batch_size => 102400 #bytes #default 102400 bytes

    #min_delay => 1

    #max_delay => 300

    #retries => 10

    # Basic auth credentials
    #username => "test"

    #password => "test"

    # TLS config
    # cert => /path/to/certificate.pem

    # key => /path/to/key.key

    # ca_cert => /path/to/ca.pem

    # insecure_skip_verify => false
  }
}
