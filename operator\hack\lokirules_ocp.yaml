---
apiVersion: loki.grafana.com/v1beta1
kind: AlertingRule
metadata:
  name: loki-operator-dev
  namespace: openshift-operators-redhat
  labels:
    openshift.io/cluster-monitoring: "true"
spec:
  tenantID: "infrastructure"
  groups:
    - name: LokiOperatorHighReconciliationError
      rules:
        - alert: HighPercentageError
          expr: |
            sum(rate({kubernetes_namespace_name="openshift-operators-redhat", kubernetes_pod_name=~"loki-operator-controller-manager.*"} |= "error" [1m])) by (job)
              /
            sum(rate({kubernetes_namespace_name="openshift-operators-redhat", kubernetes_pod_name=~"loki-operator-controller-manager.*"}[1m])) by (job)
              > 0.01
          for: 10s
          labels:
            severity: page
          annotations:
            summary: High Loki Operator Reconciliation Errors
---
apiVersion: loki.grafana.com/v1beta1
kind: RecordingRule
metadata:
  name: loki-operator-dev
  namespace: openshift-operators-redhat
  labels:
    openshift.io/cluster-monitoring: "true"
spec:
  tenantID: "infrastructure"
  groups:
    - name: LokiOperatorReconciliationErrors10m
      interval: 10m
      rules:
        - record: "loki:operator:errors:rate10m"
          expr: |
            sum(rate({kubernetes_namespace_name="openshift-operators-redhat", kubernetes_pod_name=~"loki-operator-controller-manager.*"} |= "error" [10m]))
