apiVersion: operators.coreos.com/v1alpha1
kind: ClusterServiceVersion
metadata:
  annotations:
    alm-examples: '[]'
    capabilities: Full Lifecycle
    categories: OpenShift Optional, Logging & Tracing
    certified: "false"
    containerImage: quay.io/openshift-logging/loki-operator:v0.0.1
    description: |
      The Loki Operator for OCP provides a means for configuring and managing a Loki stack for cluster logging.
      ## Prerequisites and Requirements
      ### Loki Operator Namespace
      The Loki Operator must be deployed to the global operator group namespace `openshift-logging`.
      ### Memory Considerations
      Loki is a memory intensive application.  The initial
      set of OCP nodes may not be large enough to support the Loki stack.  Additional OCP nodes must be added
      to the OCP cluster if you desire to run with the recommended (or better) memory.
    olm.skipRange: '>=5.4.0-0 <5.5.0'
    operatorframework.io/cluster-monitoring: "true"
    operatorframework.io/suggested-namespace: openshift-operators-redhat
    support: AOS Cluster Logging
  labels:
    operatorframework.io/arch.amd64: supported
    operatorframework.io/arch.arm64: supported
    operatorframework.io/arch.ppc64le: supported
    operatorframework.io/arch.s390x: supported
  name: loki-operator.v0.0.0
  namespace: placeholder
spec:
  apiservicedefinitions: {}
  customresourcedefinitions:
    owned:
    - description: AlertingRule is the Schema for the alertingrules API
      displayName: AlertingRule
      kind: AlertingRule
      name: alertingrules.loki.grafana.com
      resources:
      - kind: LokiStack
        name: ""
        version: v1beta1
      specDescriptors:
      - description: List of groups for alerting rules.
        displayName: Groups
        path: groups
      - description: Interval defines the time interval between evaluation of the
          given alerting rule.
        displayName: Evaluation Interval
        path: groups[0].interval
      - description: Limit defines the number of alerts an alerting rule can produce.
          0 is no limit.
        displayName: Limit of firing alerts
        path: groups[0].limit
        x-descriptors:
        - urn:alm:descriptor:com.tectonic.ui:number
      - description: Name of the alerting rule group. Must be unique within all alerting
          rules.
        displayName: Name
        path: groups[0].name
      - description: Rules defines a list of alerting rules
        displayName: Rules
        path: groups[0].rules
      - description: The name of the alert. Must be a valid label value.
        displayName: Name
        path: groups[0].rules[0].alert
      - description: Annotations to add to each alert.
        displayName: Annotations
        path: groups[0].rules[0].annotations
      - description: The LogQL expression to evaluate. Every evaluation cycle this
          is evaluated at the current time, and all resultant time series become pending/firing
          alerts.
        displayName: LogQL Expression
        path: groups[0].rules[0].expr
      - description: Alerts are considered firing once they have been returned for
          this long. Alerts which have not yet fired for long enough are considered
          pending.
        displayName: Firing Threshold
        path: groups[0].rules[0].for
      - description: Labels to add to each alert.
        displayName: Labels
        path: groups[0].rules[0].labels
      - description: TenantID of tenant where the alerting rules are evaluated in.
        displayName: Tenant ID
        path: tenantID
      statusDescriptors:
      - description: Conditions of the AlertingRule generation health.
        displayName: Conditions
        path: conditions
        x-descriptors:
        - urn:alm:descriptor:io.kubernetes.conditions
      version: v1beta1
    - description: LokiStack is the Schema for the lokistacks API
      displayName: LokiStack
      kind: LokiStack
      name: lokistacks.loki.grafana.com
      resources:
      - kind: ConfigMap
        name: ""
        version: v1
      - kind: Deployment
        name: ""
        version: v1
      - kind: Ingress
        name: ""
        version: v1
      - kind: PersistentVolumeClaims
        name: ""
        version: v1
      - kind: Route
        name: ""
        version: v1
      - kind: Service
        name: ""
        version: v1
      - kind: ServiceAccount
        name: ""
        version: v1
      - kind: ServiceMonitor
        name: ""
        version: v1
      - kind: StatefulSet
        name: ""
        version: v1
      specDescriptors:
      - description: Limits defines the limits to be applied to log stream processing.
        displayName: Rate Limiting
        path: limits
        x-descriptors:
        - urn:alm:descriptor:com.tectonic.ui:advanced
      - description: Global defines the limits applied globally across the cluster.
        displayName: Global Limits
        path: limits.global
      - description: IngestionBurstSize defines the local rate-limited sample size
          per distributor replica. It should be set to the set at least to the maximum
          logs size expected in a single push request.
        displayName: Ingestion Burst Size (in MB)
        path: limits.global.ingestion.ingestionBurstSize
        x-descriptors:
        - urn:alm:descriptor:com.tectonic.ui:number
      - description: IngestionRate defines the sample size per second. Units MB.
        displayName: Ingestion Rate (in MB)
        path: limits.global.ingestion.ingestionRate
        x-descriptors:
        - urn:alm:descriptor:com.tectonic.ui:number
      - description: MaxGlobalStreamsPerTenant defines the maximum number of active
          streams per tenant, across the cluster.
        displayName: Max Global Streams per  Tenant
        path: limits.global.ingestion.maxGlobalStreamsPerTenant
        x-descriptors:
        - urn:alm:descriptor:com.tectonic.ui:number
      - description: MaxLabelNameLength defines the maximum number of characters allowed
          for label keys in log streams.
        displayName: Max Label Name Length
        path: limits.global.ingestion.maxLabelNameLength
        x-descriptors:
        - urn:alm:descriptor:com.tectonic.ui:number
      - description: MaxLabelNamesPerSeries defines the maximum number of label names
          per series in each log stream.
        displayName: Max Labels Names per Series
        path: limits.global.ingestion.maxLabelNamesPerSeries
        x-descriptors:
        - urn:alm:descriptor:com.tectonic.ui:number
      - description: MaxLabelValueLength defines the maximum number of characters
          allowed for label values in log streams.
        displayName: Max Label Value Length
        path: limits.global.ingestion.maxLabelValueLength
        x-descriptors:
        - urn:alm:descriptor:com.tectonic.ui:number
      - description: MaxLineSize defines the maximum line size on ingestion path.
          Units in Bytes.
        displayName: Max Line Size
        path: limits.global.ingestion.maxLineSize
        x-descriptors:
        - urn:alm:descriptor:com.tectonic.ui:number
      - description: MaxChunksPerQuery defines the maximum number of chunks that can
          be fetched by a single query.
        displayName: Max Chunk per Query
        path: limits.global.queries.maxChunksPerQuery
        x-descriptors:
        - urn:alm:descriptor:com.tectonic.ui:number
      - description: MaxEntriesLimitsPerQuery defines the maximum number of log entries
          that will be returned for a query.
        displayName: Max Entries Limit per Query
        path: limits.global.queries.maxEntriesLimitPerQuery
        x-descriptors:
        - urn:alm:descriptor:com.tectonic.ui:number
      - description: MaxQuerySeries defines the the maximum of unique series that
          is returned by a metric query.
        displayName: Max Query Series
        path: limits.global.queries.maxQuerySeries
        x-descriptors:
        - urn:alm:descriptor:com.tectonic.ui:number
      - description: Tenants defines the limits applied per tenant.
        displayName: Limits per Tenant
        path: limits.tenants
      - description: IngestionBurstSize defines the local rate-limited sample size
          per distributor replica. It should be set to the set at least to the maximum
          logs size expected in a single push request.
        displayName: Ingestion Burst Size (in MB)
        path: limits.tenants.ingestion.ingestionBurstSize
        x-descriptors:
        - urn:alm:descriptor:com.tectonic.ui:number
      - description: IngestionRate defines the sample size per second. Units MB.
        displayName: Ingestion Rate (in MB)
        path: limits.tenants.ingestion.ingestionRate
        x-descriptors:
        - urn:alm:descriptor:com.tectonic.ui:number
      - description: MaxGlobalStreamsPerTenant defines the maximum number of active
          streams per tenant, across the cluster.
        displayName: Max Global Streams per  Tenant
        path: limits.tenants.ingestion.maxGlobalStreamsPerTenant
        x-descriptors:
        - urn:alm:descriptor:com.tectonic.ui:number
      - description: MaxLabelNameLength defines the maximum number of characters allowed
          for label keys in log streams.
        displayName: Max Label Name Length
        path: limits.tenants.ingestion.maxLabelNameLength
        x-descriptors:
        - urn:alm:descriptor:com.tectonic.ui:number
      - description: MaxLabelNamesPerSeries defines the maximum number of label names
          per series in each log stream.
        displayName: Max Labels Names per Series
        path: limits.tenants.ingestion.maxLabelNamesPerSeries
        x-descriptors:
        - urn:alm:descriptor:com.tectonic.ui:number
      - description: MaxLabelValueLength defines the maximum number of characters
          allowed for label values in log streams.
        displayName: Max Label Value Length
        path: limits.tenants.ingestion.maxLabelValueLength
        x-descriptors:
        - urn:alm:descriptor:com.tectonic.ui:number
      - description: MaxLineSize defines the maximum line size on ingestion path.
          Units in Bytes.
        displayName: Max Line Size
        path: limits.tenants.ingestion.maxLineSize
        x-descriptors:
        - urn:alm:descriptor:com.tectonic.ui:number
      - description: MaxChunksPerQuery defines the maximum number of chunks that can
          be fetched by a single query.
        displayName: Max Chunk per Query
        path: limits.tenants.queries.maxChunksPerQuery
        x-descriptors:
        - urn:alm:descriptor:com.tectonic.ui:number
      - description: MaxEntriesLimitsPerQuery defines the maximum number of log entries
          that will be returned for a query.
        displayName: Max Entries Limit per Query
        path: limits.tenants.queries.maxEntriesLimitPerQuery
        x-descriptors:
        - urn:alm:descriptor:com.tectonic.ui:number
      - description: MaxQuerySeries defines the the maximum of unique series that
          is returned by a metric query.
        displayName: Max Query Series
        path: limits.tenants.queries.maxQuerySeries
        x-descriptors:
        - urn:alm:descriptor:com.tectonic.ui:number
      - description: ManagementState defines if the CR should be managed by the operator
          or not. Default is managed.
        displayName: Management State
        path: managementState
        x-descriptors:
        - urn:alm:descriptor:com.tectonic.ui:select:Managed
        - urn:alm:descriptor:com.tectonic.ui:select:Unmanaged
      - description: ReplicationFactor defines the policy for log stream replication.
        displayName: Replication Factor
        path: replicationFactor
        x-descriptors:
        - urn:alm:descriptor:com.tectonic.ui:number
      - description: Rules defines the spec for the ruler component
        displayName: Rules
        path: rules
        x-descriptors:
        - urn:alm:descriptor:com.tectonic.ui:advanced
      - description: Enabled defines a flag to enable/disable the ruler component
        displayName: Enable
        path: rules.enabled
        x-descriptors:
        - urn:alm:descriptor:com.tectonic.ui:booleanSwitch
      - description: Namespaces to be selected for PrometheusRules discovery. If unspecified,
          only the same namespace as the LokiStack object is in is used.
        displayName: Namespace Selector
        path: rules.namespaceSelector
      - description: A selector to select which LokiRules to mount for loading alerting/recording
          rules from.
        displayName: Selector
        path: rules.selector
      - description: Size defines one of the support Loki deployment scale out sizes.
        displayName: LokiStack Size
        path: size
        x-descriptors:
        - urn:alm:descriptor:com.tectonic.ui:select:1x.extra-small
        - urn:alm:descriptor:com.tectonic.ui:select:1x.small
        - urn:alm:descriptor:com.tectonic.ui:select:1x.medium
      - description: Storage defines the spec for the object storage endpoint to store
          logs.
        displayName: Object Storage
        path: storage
      - description: Name of a secret in the namespace configured for object storage
          secrets.
        displayName: Object Storage Secret Name
        path: storage.secret.name
        x-descriptors:
        - urn:alm:descriptor:io.kubernetes:Secret
      - description: Type of object storage that should be used
        displayName: Object Storage Secret Type
        path: storage.secret.type
        x-descriptors:
        - urn:alm:descriptor:com.tectonic.ui:select:azure
        - urn:alm:descriptor:com.tectonic.ui:select:gcs
        - urn:alm:descriptor:com.tectonic.ui:select:s3
        - urn:alm:descriptor:com.tectonic.ui:select:swift
      - description: TLS configuration for reaching the object storage endpoint.
        displayName: TLS Config
        path: storage.tls
      - description: CA is the name of a ConfigMap containing a CA certificate. It
          needs to be in the same namespace as the LokiStack custom resource.
        displayName: CA ConfigMap Name
        path: storage.tls.caName
        x-descriptors:
        - urn:alm:descriptor:io.kubernetes:ConfigMap
      - description: Storage class name defines the storage class for ingester/querier
          PVCs.
        displayName: Storage Class Name
        path: storageClassName
        x-descriptors:
        - urn:alm:descriptor:io.kubernetes:StorageClass
      - description: Template defines the resource/limits/tolerations/nodeselectors
          per component
        displayName: Node Placement
        path: template
        x-descriptors:
        - urn:alm:descriptor:com.tectonic.ui:advanced
      - description: Compactor defines the compaction component spec.
        displayName: Compactor pods
        path: template.compactor
      - description: Replicas defines the number of replica pods of the component.
        displayName: Replicas
        path: template.compactor.replicas
        x-descriptors:
        - urn:alm:descriptor:com.tectonic.ui:hidden
      - description: Distributor defines the distributor component spec.
        displayName: Distributor pods
        path: template.distributor
      - description: Replicas defines the number of replica pods of the component.
        displayName: Replicas
        path: template.distributor.replicas
        x-descriptors:
        - urn:alm:descriptor:com.tectonic.ui:hidden
      - description: Gateway defines the lokistack gateway component spec.
        displayName: Gateway pods
        path: template.gateway
      - description: Replicas defines the number of replica pods of the component.
        displayName: Replicas
        path: template.gateway.replicas
        x-descriptors:
        - urn:alm:descriptor:com.tectonic.ui:hidden
      - description: IndexGateway defines the index gateway component spec.
        displayName: Index Gateway pods
        path: template.indexGateway
      - description: Replicas defines the number of replica pods of the component.
        displayName: Replicas
        path: template.indexGateway.replicas
        x-descriptors:
        - urn:alm:descriptor:com.tectonic.ui:hidden
      - description: Ingester defines the ingester component spec.
        displayName: Ingester pods
        path: template.ingester
      - description: Replicas defines the number of replica pods of the component.
        displayName: Replicas
        path: template.ingester.replicas
        x-descriptors:
        - urn:alm:descriptor:com.tectonic.ui:hidden
      - description: Querier defines the querier component spec.
        displayName: Querier pods
        path: template.querier
      - description: Replicas defines the number of replica pods of the component.
        displayName: Replicas
        path: template.querier.replicas
        x-descriptors:
        - urn:alm:descriptor:com.tectonic.ui:hidden
      - description: QueryFrontend defines the query frontend component spec.
        displayName: Query Frontend pods
        path: template.queryFrontend
      - description: Replicas defines the number of replica pods of the component.
        displayName: Replicas
        path: template.queryFrontend.replicas
        x-descriptors:
        - urn:alm:descriptor:com.tectonic.ui:hidden
      - description: Ruler defines the ruler component spec.
        displayName: Ruler pods
        path: template.ruler
      - description: Replicas defines the number of replica pods of the component.
        displayName: Replicas
        path: template.ruler.replicas
        x-descriptors:
        - urn:alm:descriptor:com.tectonic.ui:hidden
      - description: Tenants defines the per-tenant authentication and authorization
          spec for the lokistack-gateway component.
        displayName: Tenants Configuration
        path: tenants
      - description: Authentication defines the lokistack-gateway component authentication
          configuration spec per tenant.
        displayName: Authentication
        path: tenants.authentication
      - description: OIDC defines the spec for the OIDC tenant's authentication.
        displayName: OIDC Configuration
        path: tenants.authentication[0].oidc
      - description: IssuerURL defines the URL for issuer.
        displayName: Issuer URL
        path: tenants.authentication[0].oidc.issuerURL
      - description: RedirectURL defines the URL for redirect.
        displayName: Redirect URL
        path: tenants.authentication[0].oidc.redirectURL
      - description: Secret defines the spec for the clientID, clientSecret and issuerCAPath
          for tenant's authentication.
        displayName: Tenant Secret
        path: tenants.authentication[0].oidc.secret
      - description: Name of a secret in the namespace configured for tenant secrets.
        displayName: Tenant Secret Name
        path: tenants.authentication[0].oidc.secret.name
        x-descriptors:
        - urn:alm:descriptor:io.kubernetes:Secret
      - description: TenantID defines the id of the tenant.
        displayName: Tenant ID
        path: tenants.authentication[0].tenantId
      - description: TenantName defines the name of the tenant.
        displayName: Tenant Name
        path: tenants.authentication[0].tenantName
      - description: Authorization defines the lokistack-gateway component authorization
          configuration spec per tenant.
        displayName: Authorization
        path: tenants.authorization
      - description: OPA defines the spec for the third-party endpoint for tenant's
          authorization.
        displayName: OPA Configuration
        path: tenants.authorization.opa
      - description: URL defines the third-party endpoint for authorization.
        displayName: OpenPolicyAgent URL
        path: tenants.authorization.opa.url
      - description: RoleBindings defines configuration to bind a set of roles to
          a set of subjects.
        displayName: Static Role Bindings
        path: tenants.authorization.roleBindings
      - description: Roles defines a set of permissions to interact with a tenant.
        displayName: Static Roles
        path: tenants.authorization.roles
      - description: Mode defines the mode in which lokistack-gateway component will
          be configured.
        displayName: Mode
        path: tenants.mode
        x-descriptors:
        - urn:alm:descriptor:com.tectonic.ui:select:static
        - urn:alm:descriptor:com.tectonic.ui:select:dynamic
        - urn:alm:descriptor:com.tectonic.ui:select:openshift-logging
      statusDescriptors:
      - description: Distributor is a map to the per pod status of the distributor
          deployment
        displayName: Distributor
        path: components.distributor
        x-descriptors:
        - urn:alm:descriptor:com.tectonic.ui:podStatuses
      - description: Ingester is a map to the per pod status of the ingester statefulset
        displayName: Ingester
        path: components.ingester
        x-descriptors:
        - urn:alm:descriptor:com.tectonic.ui:podStatuses
      - description: Querier is a map to the per pod status of the querier deployment
        displayName: Querier
        path: components.querier
        x-descriptors:
        - urn:alm:descriptor:com.tectonic.ui:podStatuses
      - description: QueryFrontend is a map to the per pod status of the query frontend
          deployment
        displayName: Query Frontend
        path: components.queryFrontend
        x-descriptors:
        - urn:alm:descriptor:com.tectonic.ui:podStatuses
      - description: Compactor is a map to the pod status of the compactor pod.
        displayName: Compactor
        path: components.compactor
        x-descriptors:
        - urn:alm:descriptor:com.tectonic.ui:podStatuses
      - description: Gateway is a map to the per pod status of the lokistack gateway
          deployment.
        displayName: Gateway
        path: components.gateway
        x-descriptors:
        - urn:alm:descriptor:com.tectonic.ui:podStatuses
      - description: IndexGateway is a map to the per pod status of the index gateway
          statefulset
        displayName: IndexGateway
        path: components.indexGateway
        x-descriptors:
        - urn:alm:descriptor:com.tectonic.ui:podStatuses
      - description: Ruler is a map to the per pod status of the lokistack ruler statefulset.
        displayName: Ruler
        path: components.ruler
        x-descriptors:
        - urn:alm:descriptor:com.tectonic.ui:podStatuses
      - description: Conditions of the Loki deployment health.
        displayName: Conditions
        path: conditions
        x-descriptors:
        - urn:alm:descriptor:io.kubernetes.conditions
      version: v1beta1
    - description: RecordingRule is the Schema for the recordingrules API
      displayName: RecordingRule
      kind: RecordingRule
      name: recordingrules.loki.grafana.com
      resources:
      - kind: LokiStack
        name: ""
        version: v1beta1
      specDescriptors:
      - description: List of groups for recording rules.
        displayName: Groups
        path: groups
      - description: Interval defines the time interval between evaluation of the
          given recoding rule.
        displayName: Evaluation Interval
        path: groups[0].interval
      - description: Limit defines the number of series a recording rule can produce.
          0 is no limit.
        displayName: Limit of produced series
        path: groups[0].limit
        x-descriptors:
        - urn:alm:descriptor:com.tectonic.ui:number
      - description: Name of the recording rule group. Must be unique within all recording
          rules.
        displayName: Name
        path: groups[0].name
      - description: Rules defines a list of recording rules
        displayName: Rules
        path: groups[0].rules
      - description: The LogQL expression to evaluate. Every evaluation cycle this
          is evaluated at the current time, and all resultant time series become pending/firing
          alerts.
        displayName: LogQL Expression
        path: groups[0].rules[0].expr
      - description: The name of the time series to output to. Must be a valid metric
          name.
        displayName: Metric Name
        path: groups[0].rules[0].record
      - description: TenantID of tenant where the recording rules are evaluated in.
        displayName: Tenant ID
        path: tenantID
      statusDescriptors:
      - description: Conditions of the RecordingRule generation health.
        displayName: Conditions
        path: conditions
        x-descriptors:
        - urn:alm:descriptor:io.kubernetes.conditions
      version: v1beta1
    - description: RulerConfig is the Schema for the rulerconfigs API
      displayName: RulerConfig
      kind: RulerConfig
      name: rulerconfigs.loki.grafana.com
      resources:
      - kind: LokiStack
        name: ""
        version: v1beta1
      specDescriptors:
      - description: Defines alert manager configuration to notify on firing alerts.
        displayName: Alert Manager Configuration
        path: alertmanager
        x-descriptors:
        - urn:alm:descriptor:com.tectonic.ui:advanced
      - description: Defines the configuration for DNS-based discovery of AlertManager
          hosts.
        displayName: DNS Discovery
        path: alertmanager.discovery
        x-descriptors:
        - urn:alm:descriptor:com.tectonic.ui:advanced
      - description: Use DNS SRV records to discover Alertmanager hosts.
        displayName: Enable SRV
        path: alertmanager.discovery.enableSRV
      - description: How long to wait between refreshing DNS resolutions of Alertmanager
          hosts.
        displayName: Refresh Interval
        path: alertmanager.discovery.refreshInterval
      - description: If enabled, then requests to Alertmanager use the v2 API.
        displayName: Enable AlertManager V2 API
        path: alertmanager.enableV2
        x-descriptors:
        - urn:alm:descriptor:com.tectonic.ui:booleanSwitch
      - description: List of AlertManager URLs to send notifications to. Each Alertmanager
          URL is treated as a separate group in the configuration. Multiple Alertmanagers
          in HA per group can be supported by using DNS resolution (See EnableDNSDiscovery).
        displayName: AlertManager Endpoints
        path: alertmanager.endpoints
      - description: Additional labels to add to all alerts.
        displayName: Extra Alert Labels
        path: alertmanager.externalLabels
      - description: URL for alerts return path.
        displayName: Alert External URL
        path: alertmanager.externalUrl
      - description: Defines the configuration for the notification queue to AlertManager
          hosts.
        displayName: Notification Queue
        path: alertmanager.notificationQueue
        x-descriptors:
        - urn:alm:descriptor:com.tectonic.ui:advanced
      - description: Capacity of the queue for notifications to be sent to the Alertmanager.
        displayName: Notification Queue Capacity
        path: alertmanager.notificationQueue.capacity
        x-descriptors:
        - urn:alm:descriptor:com.tectonic.ui:number
      - description: Minimum duration between alert and restored "for" state. This
          is maintained only for alerts with configured "for" time greater than the
          grace period.
        displayName: Firing Grace Period
        path: alertmanager.notificationQueue.forGracePeriod
      - description: Max time to tolerate outage for restoring "for" state of alert.
        displayName: Outage Tolerance
        path: alertmanager.notificationQueue.forOutageTolerance
      - description: Minimum amount of time to wait before resending an alert to Alertmanager.
        displayName: Resend Delay
        path: alertmanager.notificationQueue.resendDelay
      - description: HTTP timeout duration when sending notifications to the Alertmanager.
        displayName: Timeout
        path: alertmanager.notificationQueue.timeout
      - description: Interval on how frequently to evaluate rules.
        displayName: Evaluation Interval
        path: evaluationInterval
      - description: Interval on how frequently to poll for new rule definitions.
        displayName: Poll Interval
        path: pollInterval
      - description: Defines a remote write endpoint to write recording rule metrics.
        displayName: Remote Write Configuration
        path: remoteWrite
        x-descriptors:
        - urn:alm:descriptor:com.tectonic.ui:advanced
      - description: Defines the configuration for remote write client.
        displayName: Client
        path: remoteWrite.client
        x-descriptors:
        - urn:alm:descriptor:com.tectonic.ui:advanced
      - description: Type of authorzation to use to access the remote write endpoint
        displayName: Authorization Type
        path: remoteWrite.client.authorization
        x-descriptors:
        - urn:alm:descriptor:com.tectonic.ui:select:basic
        - urn:alm:descriptor:com.tectonic.ui:select:header
      - description: Name of a secret in the namespace configured for authorization
          secrets.
        displayName: Authorization Secret Name
        path: remoteWrite.client.authorizationSecretName
        x-descriptors:
        - urn:alm:descriptor:io.kubernetes:Secret
      - description: Configure whether HTTP requests follow HTTP 3xx redirects.
        displayName: Follow HTTP Redirects
        path: remoteWrite.client.followRedirects
        x-descriptors:
        - urn:alm:descriptor:com.tectonic.ui:booleanSwitch
      - description: Name of the remote write config, which if specified must be unique
          among remote write configs.
        displayName: Name
        path: remoteWrite.client.name
      - description: Optional proxy URL.
        displayName: HTTP Proxy URL
        path: remoteWrite.client.proxyUrl
      - description: List of remote write relabel configurations.
        displayName: Metric Relabel Configuration
        path: remoteWrite.client.relabelConfigs
      - description: Action to perform based on regex matching. Default is 'replace'
        displayName: Action
        path: remoteWrite.client.relabelConfigs[0].action
        x-descriptors:
        - urn:alm:descriptor:com.tectonic.ui:select:drop
        - urn:alm:descriptor:com.tectonic.ui:select:hashmod
        - urn:alm:descriptor:com.tectonic.ui:select:keep
        - urn:alm:descriptor:com.tectonic.ui:select:labeldrop
        - urn:alm:descriptor:com.tectonic.ui:select:labelkeep
        - urn:alm:descriptor:com.tectonic.ui:select:labelmap
        - urn:alm:descriptor:com.tectonic.ui:select:replace
      - description: Modulus to take of the hash of the source label values.
        displayName: Modulus
        path: remoteWrite.client.relabelConfigs[0].modulus
      - description: Regular expression against which the extracted value is matched.
          Default is '(.*)'
        displayName: Regex
        path: remoteWrite.client.relabelConfigs[0].regex
      - description: Replacement value against which a regex replace is performed
          if the regular expression matches. Regex capture groups are available. Default
          is '$1'
        displayName: Replacement
        path: remoteWrite.client.relabelConfigs[0].replacement
      - description: Separator placed between concatenated source label values. default
          is ';'.
        displayName: Separator
        path: remoteWrite.client.relabelConfigs[0].separator
      - description: The source labels select values from existing labels. Their content
          is concatenated using the configured separator and matched against the configured
          regular expression for the replace, keep, and drop actions.
        displayName: Source Labels
        path: remoteWrite.client.relabelConfigs[0].sourceLabels
      - description: Label to which the resulting value is written in a replace action.
          It is mandatory for replace actions. Regex capture groups are available.
        displayName: Target Label
        path: remoteWrite.client.relabelConfigs[0].targetLabel
      - description: Timeout for requests to the remote write endpoint.
        displayName: Remote Write Timeout
        path: remoteWrite.client.timeout
      - description: The URL of the endpoint to send samples to.
        displayName: Endpoint
        path: remoteWrite.client.url
      - description: Enable remote-write functionality.
        displayName: Enabled
        path: remoteWrite.enabled
        x-descriptors:
        - urn:alm:descriptor:com.tectonic.ui:booleanSwitch
      - description: Defines the configuration for remote write client queue.
        displayName: Client Queue
        path: remoteWrite.queue
        x-descriptors:
        - urn:alm:descriptor:com.tectonic.ui:advanced
      - description: Maximum time a sample will wait in buffer.
        displayName: Batch Send Deadline
        path: remoteWrite.queue.batchSendDeadline
      - description: Number of samples to buffer per shard before we block reading
          of more
        displayName: Queue Capacity
        path: remoteWrite.queue.capacity
        x-descriptors:
        - urn:alm:descriptor:com.tectonic.ui:number
      - description: Maximum retry delay.
        displayName: Max BackOff Period
        path: remoteWrite.queue.maxBackOffPeriod
      - description: Maximum number of samples per send.
        displayName: Maximum Shards per Send
        path: remoteWrite.queue.maxSamplesPerSend
        x-descriptors:
        - urn:alm:descriptor:com.tectonic.ui:number
      - description: Maximum number of shards, i.e. amount of concurrency.
        displayName: Maximum Shards
        path: remoteWrite.queue.maxShards
        x-descriptors:
        - urn:alm:descriptor:com.tectonic.ui:number
      - description: Initial retry delay. Gets doubled for every retry.
        displayName: Min BackOff Period
        path: remoteWrite.queue.minBackOffPeriod
      - description: Minimum number of shards, i.e. amount of concurrency.
        displayName: Minimum Shards
        path: remoteWrite.queue.minShards
        x-descriptors:
        - urn:alm:descriptor:com.tectonic.ui:number
      - description: Minimum period to wait between refreshing remote-write reconfigurations.
        displayName: Min Refresh Period
        path: remoteWrite.refreshPeriod
      statusDescriptors:
      - description: Conditions of the RulerConfig health.
        displayName: Conditions
        path: conditions
        x-descriptors:
        - urn:alm:descriptor:io.kubernetes.conditions
      version: v1beta1
  description: |
    The Loki Operator for OCP provides a means for configuring and managing a Loki stack for cluster logging.
    ## Prerequisites and Requirements
    ### Loki Operator Namespace
    The Loki Operator must be deployed to the global operator group namespace `openshift-logging`.
    ### Memory Considerations
    Loki is a memory intensive application.  The initial
    set of OCP nodes may not be large enough to support the Loki cluster.  Additional OCP nodes must be added
    to the OCP cluster if you desire to run with the recommended (or better) memory.
  displayName: Loki Operator
  icon:
  - base64data: 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
    mediatype: image/svg+xml
  install:
    spec:
      deployments: null
    strategy: ""
  installModes:
  - supported: true
    type: OwnNamespace
  - supported: false
    type: SingleNamespace
  - supported: false
    type: MultiNamespace
  - supported: true
    type: AllNamespaces
  keywords:
  - logging
  - loki
  links:
  - name: Loki Operator
    url: https://github.com/grafana/loki
  maintainers:
  - email: <EMAIL>
    name: Grafana Loki SIG Operator
  maturity: alpha
  minKubeVersion: 1.21.1
  provider:
    name: Grafana.com
  version: 0.0.0
