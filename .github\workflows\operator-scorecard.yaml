name: operator scorecard

on:
  push:
    paths:
      - 'operator/**'
    branches: [ main ]
  pull_request:
    paths:
      - 'operator/**'

jobs:
  build:
    name: scorecard
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        go: ['1.17']
    steps:
    - name: Set up Go 1.x
      uses: actions/setup-go@v3
      with:
        go-version: ${{ matrix.go }}
      id: go
    - uses: engineerd/setup-kind@v0.5.0
      with:
        version: "v0.11.1"
    - uses: actions/checkout@v3
    - name: Install make
      run: sudo apt-get install make
    - name: Run scorecard
      run: make scorecard
      working-directory: ./operator
