---
title: V2.6
weight: 66
---

# Version 2.6 release notes

Grafana Labs is excited to announce the release of Loki 2.6. Here's a summary of new enhancements and important fixes.

## Features and enhancements

- **Query multiple tenants at once.** We've introduced cross-tenant query federation, which allows you to issue one query to multiple tenants and get a single, consolidated result. This is great for scenarios where you need a global view of logs within your multi-tenant cluster. For more information on how to enable this feature, see [Multi-Tenancy]({{< relref "../operations/multi-tenancy.md" >}}). 
- **Filter out and delete certain log lines from query results.** This is particularly useful in cases where users may accidentally write sensitive information to <PERSON> that they do not want exposed. Users craft a LogQL query that selects the specific lines they're interested in, and then can choose to either filter out those lines from query results, or permanently delete them from <PERSON>'s storage. For more information, see [Logs Deletion]({{< relref "../operations/storage/logs-deletion.md" >}}).
- **Improved query performance on instant queries.** Loki now splits instant queries with a large time range (for example, `sum(rate({app="foo"}[6h]))`) into several smaller sub-queries and executes them in parallel. Users don't need to take any action to enjoy this performance improvement; however, they can adjust the number of sub-queries generated by modifying the `split_queries_by_interval` configuration parameter, which currently defaults to `30m`. 
- **Support Baidu AI Cloud as a storage backend.** Loki users can now use Baidu Object Storage (BOS) as their storage backend. See [bos_storage_config]({{< relref "../configuration/_index.md#bos_storage_config" >}}) for details. 

For a full list of all changes please look at the [CHANGELOG](https://github.com/grafana/loki/blob/main/CHANGELOG.md).

## Upgrade Considerations

As always, please read the [upgrade guide](../../upgrading/#260) before upgrading Loki.

## Bug fixes

### 2.6.0 bug fixes

V2.6.0 fixes numerous bugs. The [CHANGELOG](https://github.com/grafana/loki/blob/main/CHANGELOG.md) has the complete list.

A summary of some of the more important fixes:

- [PR 6152](https://github.com/grafana/loki/pull/6152) Fixed a scenario where live tailing of logs could cause unbounded ingester memory growth. 
- [PR 5685](https://github.com/grafana/loki/pull/5685) Fixed a bug in Loki's push request parser that allowed users to send arbitrary non-string data as a log line. We now test that the pushed values are valid strings and return an error if values are not valid strings.
- [PR 5799](https://github.com/grafana/loki/pull/5799) Fixed incorrect deduplication logic for cases where multiple log entries with the same timestamp exist. 
- [PR 5888](https://github.com/grafana/loki/pull/5888) Fixed a bug in the [common configuration]({{< relref "../configuration/_index.md#common" >}}) where the `instance_interface_names` setting was getting overwritten by the default ring configuration. 