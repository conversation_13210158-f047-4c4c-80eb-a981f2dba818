# Auto generated binary variables helper managed by https://github.com/bwplotka/bingo v0.5.2. DO NOT EDIT.
# All tools are designed to be build inside $GOBIN.
# Those variables will work only until 'bingo get' was invoked, or if tools were installed via Makefile's Variables.mk.
GOBIN=${GOBIN:=$(go env GOBIN)}

if [ -z "$GOBIN" ]; then
	GOBIN="$(go env GOPATH)/bin"
fi


BINGO="${GOBIN}/bingo-v0.5.2"

CONTROLLER_GEN="${GOBIN}/controller-gen-v0.8.0"

GOFUMPT="${GOBIN}/gofumpt-v0.1.1"

GOLANGCI_LINT="${GOBIN}/golangci-lint-v1.38.0"

KUSTOMIZE="${GOBIN}/kustomize-v4.2.0"

OPERATOR_SDK="${GOBIN}/operator-sdk-v1.19.1"

PROMTOOL="${GOBIN}/promtool-v1.8.2-0.20220211202545-56e14463bccf"

