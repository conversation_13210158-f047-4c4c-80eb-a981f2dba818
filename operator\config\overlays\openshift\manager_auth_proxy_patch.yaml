apiVersion: apps/v1
kind: Deployment
metadata:
  name: controller-manager
spec:
  template:
    spec:
      containers:
      - name: kube-rbac-proxy
        image: quay.io/openshift/origin-kube-rbac-proxy:latest
        args:
        - "--secure-listen-address=0.0.0.0:8443"
        - "--upstream=http://127.0.0.1:8080/"
        - "--logtostderr=true"
        - "--tls-cert-file=/var/run/secrets/serving-cert/tls.crt"
        - "--tls-private-key-file=/var/run/secrets/serving-cert/tls.key"
        - "--v=0"
        ports:
        - containerPort: 8443
          name: https
        volumeMounts:
        - mountPath: /var/run/secrets/serving-cert
          name: loki-operator-metrics-cert
      volumes:
      - name: loki-operator-metrics-cert
        secret:
          defaultMode: 420
          optional: true
          secretName: loki-operator-metrics
