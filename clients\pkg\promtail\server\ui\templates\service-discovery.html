{{define "head"}}
<link type="text/css" rel="stylesheet" href="{{ pathPrefix }}/static/css/targets.css?v={{ buildVersion }}">
<script src="{{ pathPrefix }}/static/js/targets.js?v={{ buildVersion }}"></script>
{{end}}

<style>
  *[id]:before {
    display: block;
    content: " ";
    margin-top: -65px;
    height: 65px;
    visibility: hidden;
  }
</style>

{{define "content"}}
  <div class="container-fluid">

    <h1>Service Discovery</h1>
    <div>
      <ul>
        {{range $i, $job := .Index}}
          <li>
            <a href="#job-{{$job}}">{{$job}}</a> ({{ index $.Active $i }}/{{ index $.Total $i }} active targets)
          </li>
        {{end}}
      </ul>
    </div>

    {{$targets := .Targets}}
    {{range $i, $job := .Index}}

    <div class="table-container">
      <h2 class="job_header" id="job-{{$job}}">
        {{$job}}
        <button type="button" class="targets collapsed-table btn btn-primary">show more</button>
      </h2>
      {{with index $.Dropped $i}}
      {{if gt . 100 }}
      <div class="collapsed-element" style="display:none">
        {{ . }} targets have been dropped, showing only the first 100 dropped targets as examples.
      </div>
      {{end}}
      {{end}}
      <table class="table table-sm table-bordered table-striped table-hover" style="display:none">
        <thead class="job_details">
          <tr>
            <th>Discovered Labels</th>
            <th>Target Labels</th>
          </tr>
        </thead>
        <tbody>

        {{range index $targets $job}}
          <tr>
            <td class="labels">
                {{$labels := .DiscoveredLabels }}
                <ul class="list-inline" style="list-style-type:none">
                  {{range $label, $value := $labels}}
                    <li>
                      <span class="badge badge-primary">{{$label}}="{{$value}}"</span>
                    </li>
                  {{else}}
                  <li>
                    <span class="badge badge-default">none</span>
                  </li>
                  {{end}}
                </ul>
            </td>
            <td class="labels">
                {{$labels := .Labels }}
                <ul class="list-inline" style="list-style-type:none">
                  {{range $label, $value := $labels}}
                    <li>
                      <span class="badge badge-primary">{{$label}}="{{$value}}"</span>
                    </li>
                  {{else}}
                    <li>
                      <span class="badge badge-default">Dropped: {{ dropReason .Details }}</span>
                    </li>
                  {{end}}
                </ul>
            </td>
          </tr>
        {{end}}
        </tbody>
      </table>
    </div>
    {{ end }}
  </div>

{{end}}
