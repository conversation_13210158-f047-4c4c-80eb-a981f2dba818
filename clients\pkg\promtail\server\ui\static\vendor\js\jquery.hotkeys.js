/*
 * jQ<PERSON>y Hotkeys Plugin
 * Copyright 2010, <PERSON>
 * Dual licensed under the MIT or GPL Version 2 licenses.
 *
 * Based upon the plugin by <PERSON><PERSON><PERSON>:
 * http://github.com/tzuryby/hotkeys
 *
 * Original idea by:
 * Binny V A, http://www.openjs.com/scripts/events/keyboard_shortcuts/
*/

(function(jQuery){

    jQuery.hotkeys = {
        version: "0.8+",

        specialKeys: {
            8: "backspace", 9: "tab", 13: "return", 16: "shift", 17: "ctrl", 18: "alt", 19: "pause",
            20: "capslock", 27: "esc", 32: "space", 33: "pageup", 34: "pagedown", 35: "end", 36: "home",
            37: "left", 38: "up", 39: "right", 40: "down", 45: "insert", 46: "del",
            96: "0", 97: "1", 98: "2", 99: "3", 100: "4", 101: "5", 102: "6", 103: "7",
            104: "8", 105: "9", 106: "*", 107: "+", 109: "-", 110: ".", 111 : "/",
            112: "f1", 113: "f2", 114: "f3", 115: "f4", 116: "f5", 117: "f6", 118: "f7", 119: "f8",
            120: "f9", 121: "f10", 122: "f11", 123: "f12", 144: "numlock", 145: "scroll", 188: ",", 190: ".",
            191: "/", 224: "meta"
        },

        shiftNums: {
            "`": "~", "1": "!", "2": "@", "3": "#", "4": "$", "5": "%", "6": "^", "7": "&",
            "8": "*", "9": "(", "0": ")", "-": "_", "=": "+", ";": ": ", "'": "\"", ",": "<",
            ".": ">",  "/": "?",  "\\": "|"
        }
    };

    function keyHandler( handleObj ) {

        var origHandler = handleObj.handler,
            //use namespace as keys so it works with event delegation as well
            //will also allow removing listeners of a specific key combination
            //and support data objects
            keys = (handleObj.namespace || "").toLowerCase().split(" ");
        keys = jQuery.map(keys, function(key) { return key.split("."); });

        //no need to modify handler if no keys specified
        //Added keys[0].substring(0, 12) to work with jQuery ui 1.9.0
        //Added accordion, tabs and menu, then jquery ui can use keys.

        if (keys.length === 1 && (keys[0] === "" ||
                keys[0].substring(0, 12) === "autocomplete"  ||
                keys[0].substring(0, 9) === "accordion"  ||
                keys[0].substring(0, 4) === "tabs"  ||
                keys[0].substring(0, 4) === "menu")) {
            return;
        }

        handleObj.handler = function( event ) {
            // Don't fire in text-accepting inputs that we didn't directly bind to
            // important to note that $.fn.prop is only available on jquery 1.6+
            if ( this !== event.target && (/textarea|select/i.test( event.target.nodeName ) ||
                    event.target.type === "text" || $(event.target).prop('contenteditable') == 'true' )) {
                return;
            }

            // Keypress represents characters, not special keys
            var special = event.type !== "keypress" && jQuery.hotkeys.specialKeys[ event.which ],
                character = String.fromCharCode( event.which ).toLowerCase(),
                key, modif = "", possible = {};

            // check combinations (alt|ctrl|shift+anything)
            if ( event.altKey && special !== "alt" ) {
                modif += "alt_";
            }

            if ( event.ctrlKey && special !== "ctrl" ) {
                modif += "ctrl_";
            }

            // TODO: Need to make sure this works consistently across platforms
            if ( event.metaKey && !event.ctrlKey && special !== "meta" ) {
                modif += "meta_";
            }

            if ( event.shiftKey && special !== "shift" ) {
                modif += "shift_";
            }

            if ( special ) {
                possible[ modif + special ] = true;

            } else {
                possible[ modif + character ] = true;
                possible[ modif + jQuery.hotkeys.shiftNums[ character ] ] = true;

                // "$" can be triggered as "Shift+4" or "Shift+$" or just "$"
                if ( modif === "shift_" ) {
                    possible[ jQuery.hotkeys.shiftNums[ character ] ] = true;
                }
            }

            for ( var i = 0, l = keys.length; i < l; i++ ) {
                if ( possible[ keys[i] ] ) {
                    return origHandler.apply( this, arguments );
                }
            }
        };
    }

    jQuery.each([ "keydown", "keyup", "keypress" ], function() {
        jQuery.event.special[ this ] = { add: keyHandler };
    });

})( jQuery );